{"name": "faunalogic-platform", "namespaceSuffix": "a0ec71", "namespace": {"tools": "tools", "dev": "dev", "test": "test", "prod": "prod"}, "version": "1.0.0", "timezone": {"db": "America/Vancouver", "api": "America/Vancouver"}, "module": {"db": "faunalogic-platform-db", "api": "faunalogic-platform-api", "queue": "faunalogic-platform-queue", "app": "faunalogic-platform-app"}, "staticUrls": {"dev": "dev-faunalogic-platform.apps.silver.devops.gov.bc.ca", "test": "test-faunalogic-platform.apps.silver.devops.gov.bc.ca", "prod": "faunalogic-platform.apps.silver.devops.gov.bc.ca"}, "staticUrlsAPI": {"dev": "api-dev-faunalogic-platform.apps.silver.devops.gov.bc.ca", "test": "api-test-faunalogic-platform.apps.silver.devops.gov.bc.ca", "prod": "api-faunalogic-platform.apps.silver.devops.gov.bc.ca"}, "siteminderLogoutURL": {"dev": "https://logontest7.gov.bc.ca/clp-cgi/logoff.cgi", "test": "https://logontest7.gov.bc.ca/clp-cgi/logoff.cgi", "prod": "https://logon7.gov.bc.ca/clp-cgi/logoff.cgi"}, "sso": {"dev": {"host": "https://dev.loginproxy.gov.bc.ca/auth", "realm": "standard", "clientId": "fauna-logic-browser-4230", "keycloakSecret": "keycloak", "serviceClient": {"serviceClientName": "faunalogic-svc-4466", "keycloakSecretServiceClientPasswordKey": "faunalogic_svc_client_password"}, "cssApi": {"cssApiTokenUrl": "https://loginproxy.gov.bc.ca/auth/realms/standard/protocol/openid-connect/token", "cssApiClientId": "service-account-team-1159-4197", "cssApiHost": "https://api.loginproxy.gov.bc.ca/api/v1", "keycloakSecretCssApiSecretKey": "css_api_client_secret", "cssApiEnvironment": "dev"}}, "test": {"host": "https://test.loginproxy.gov.bc.ca/auth", "realm": "standard", "clientId": "fauna-logic-browser-4230", "keycloakSecret": "keycloak", "serviceClient": {"serviceClientName": "faunalogic-svc-4466", "keycloakSecretServiceClientPasswordKey": "faunalogic_svc_client_password"}, "cssApi": {"cssApiTokenUrl": "https://loginproxy.gov.bc.ca/auth/realms/standard/protocol/openid-connect/token", "cssApiClientId": "service-account-team-1159-4197", "cssApiHost": "https://api.loginproxy.gov.bc.ca/api/v1", "keycloakSecretCssApiSecretKey": "css_api_client_secret", "cssApiEnvironment": "test"}}, "prod": {"host": "https://loginproxy.gov.bc.ca/auth", "realm": "standard", "clientId": "fauna-logic-browser-4230", "keycloakSecret": "keycloak", "serviceClient": {"serviceClientName": "faunalogic-svc-4466", "keycloakSecretServiceClientPasswordKey": "faunalogic_svc_client_password"}, "cssApi": {"cssApiTokenUrl": "https://loginproxy.gov.bc.ca/auth/realms/standard/protocol/openid-connect/token", "cssApiClientId": "service-account-team-1159-4197", "cssApiHost": "https://api.loginproxy.gov.bc.ca/api/v1", "keycloakSecretCssApiSecretKey": "css_api_client_secret", "cssApiEnvironment": "prod"}}}}