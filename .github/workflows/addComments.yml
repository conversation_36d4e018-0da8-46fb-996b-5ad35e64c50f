# Add automated comments to the PR
name: Add Comments

on:
  pull_request:
    types: [opened, ready_for_review]

jobs:
  addOpenshiftURLComment:
    name: Add Openshift URL Comment
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: Add Comment
        uses: peter-evans/create-or-update-comment@v2
        with:
          issue-number: ${{ github.event.number }}
          body: |
            Openshift URLs for the PR Deployment:
            - App Route: https://faunalogic-platform-app-${{ github.event.number }}-a0ec71-dev.apps.silver.devops.gov.bc.ca/
            - Api Route: https://faunalogic-platform-api-${{ github.event.number }}-a0ec71-dev.apps.silver.devops.gov.bc.ca/
            - Pods: https://console.apps.silver.devops.gov.bc.ca/k8s/ns/a0ec71-dev/pods?name=${{ github.event.number }}
