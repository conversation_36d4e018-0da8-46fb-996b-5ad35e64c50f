# Some extra/old git action jobs, stored here as a backup, should they need to be re-instantiated.

name: PR-Based Deploy on OpenShift

on:
  pull_request:
    types: [opened, reopened, synchronize]

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.merged == false && github.event.pull_request.draft == false && github.base_ref != 'prod' }}
    env:
      CYPRESS_RECORD_KEY: ${{ secrets.RECORDING_KEY }}
      CYPRESS_username: ${{ secrets.CYPRESS_USER_NAME }}
      CYPRESS_password: ${{ secrets.CYPRESS_PASSWORD }}
      CYPRESS_BASE_URL: "https://faunalogic-platform-app-${{ github.event.number }}-a0ec71-dev.apps.silver.devops.gov.bc.ca"
      CYPRESS_host: "https://faunalogic-platform-app-${{ github.event.number }}-a0ec71-dev.apps.silver.devops.gov.bc.ca"
      CYPRESS_ENVIRONMENT: ${{ github.base_ref }}
      CYPRESS_authRealm: "35r1iman"
      CYPRESS_authClientId: "faunalogic"
      CYPRESS_authUrl: "https://${{ github.base_ref }}.oidc.gov.bc.ca"
    needs:
      - deployDatabase
      - deployDatabaseSetup
      - deployAPI
      - deployAPP
    steps:
      # Checkout the PR branch
      - name: Checkout Target Branch
        uses: actions/checkout@v4

      - name: Wait for API response
        uses: nev7n/wait_for_response@v1.0.1
        with:
          url: "https://faunalogic-platform-api-${{ github.event.number }}-a0ec71-dev.apps.silver.devops.gov.bc.ca/version"
          responseCode: 200
          timeout: 240000
          interval: 500

      - name: Wait for APP response
        uses: nev7n/wait_for_response@v1.0.1
        with:
          url: "https://faunalogic-platform-app-${{ github.event.number }}-a0ec71-dev.apps.silver.devops.gov.bc.ca"
          responseCode: 200
          timeout: 120000
          interval: 500

      - name: E2E Smoke tests
        uses: cypress-io/github-action@v6
        # let's give this action an ID so we can refer
        # to its output values later
        id: smoke
        continue-on-error: false
        with:
          wait-on: "https://faunalogic-platform-app-${{ github.event.number }}-a0ec71-dev.apps.silver.devops.gov.bc.ca"
          wait-on-timeout: 120
          record: true
          working-directory: testing/e2e
          spec: cypress/integration/smoke*.spec.ts
          browser: chrome
          ci-build-id: ${{ github.event.number }}

      - name: Print Env Vars
        run: |
          echo Git Base Ref: ${{ github.base_ref }}
          echo Git Change ID: ${{ github.event.number }}
          echo Cypress BaseUrl: $CYPRESS_BASE_URL
          echo Cypress Host: $CYPRESS_ENVIRONMENT
          echo $CYPRESS_authRealm
          echo $CYPRESS_authClientId
          echo $CYPRESS_authUrl
