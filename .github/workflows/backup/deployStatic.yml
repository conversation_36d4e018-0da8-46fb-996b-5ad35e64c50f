# Some extra/old git action jobs, stored here as a backup, should they need to be re-instantiated.

name: Static Deploy on OpenShift

on:
  pull_request:
    types: [opened, reopened, synchronize, closed]
    branches:
      - dev
      - test
      - prod

jobs:
  cycleschemaspy:
    name: Cycle SchemaSpy to refresh after database update in dev
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.merged == true && github.event.pull_request.draft == false && github.base_ref == 'dev' }}
    needs:
      - checkEnv
      - deployDatabaseSetup
    steps:
      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      - name: Scale down
        run: |
          oc project a0ec71-dev
          oc scale --replicas=0 dc schemaspy
          oc scale --replicas=1 dc schemaspy

  cypress-run:
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.merged == true && github.event.pull_request.draft == false && github.base_ref != 'prod' }}
    env:
      CYPRESS_RECORD_KEY: ${{ secrets.RECORDING_KEY }}
      CYPRESS_username: ${{ secrets.CYPRESS_USER_NAME }}
      CYPRESS_password: ${{ secrets.CYPRESS_PASSWORD }}
      CYPRESS_BASE_URL: "https://${{ github.base_ref }}-faunalogic.apps.silver.devops.gov.bc.ca"
      CYPRESS_host: "https://${{ github.base_ref }}-faunalogic.apps.silver.devops.gov.bc.ca"
      CYPRESS_ENVIRONMENT: ${{ github.base_ref }}
      CYPRESS_authRealm: "35r1iman"
      CYPRESS_authClientId: "faunalogic"
      CYPRESS_authUrl: "https://${{ github.base_ref }}.oidc.gov.bc.ca"
    needs:
      - deployDatabase
      - deployDatabaseSetup
      - deployAPI
      - deployAPP
    steps:
      # Checkout the PR branch
      - name: Checkout Target Branch
        uses: actions/checkout@v4

      - name: Wait for API response
        uses: nev7n/wait_for_response@v1.0.1
        with:
          url: "https://api-${{ github.base_ref }}-faunalogic.apps.silver.devops.gov.bc.ca/version"
          responseCode: 200
          timeout: 240000
          interval: 500

      - name: Wait for APP response
        uses: nev7n/wait_for_response@v1.0.1
        with:
          url: "https://${{ github.base_ref }}-faunalogic.apps.silver.devops.gov.bc.ca"
          responseCode: 200
          timeout: 240000
          interval: 500

      - name: E2E Smoke tests
        uses: cypress-io/github-action@v6
        # let's give this action an ID so we can refer
        # to its output values later
        id: smoke
        continue-on-error: false
        with:
          wait-on: "https://${{ github.base_ref }}-faunalogic.apps.silver.devops.gov.bc.ca"
          wait-on-timeout: 240
          record: true
          working-directory: testing/e2e

      - name: Print Env Vars
        run: |
          echo Git Base Ref: ${{ github.base_ref }}
          echo Git Change ID: ${{ github.event.number }}
          echo Cypress BaseUrl: $CYPRESS_BASE_URL
          echo Cypress Host: $CYPRESS_ENVIRONMENT
          echo $CYPRESS_authRealm
          echo $CYPRESS_authClientId
          echo $CYPRESS_authUrl

  notify:
    name: Discord Notification
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.merged == true && github.event.pull_request.draft == false }} && always()
    needs: # make sure the notification is sent AFTER the jobs you want included have completed
      - deployAPP
      - deployAPI
      - deployDatabaseSetup
    steps:
      - name: Notify
        uses: nobrayner/discord-webhook@v1
        with:
          github-token: ${{ secrets.github_token }}
          discord-webhook: ${{ secrets.DISCORD_WEBHOOK }}
          title: "${{ github.workflow }}: {{STATUS}}"
          username: ${{ github.actor }}
          description: "PR: ${{ github.event.number }} - ${{ github.event.pull_request.title }}: was deployed in ${{ github.base_ref }}!"
