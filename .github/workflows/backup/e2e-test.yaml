# Some extra/old git action jobs, stored here as a backup, should they need to be re-instantiated.

name: e2e
on: workflow_dispatch
jobs:
  cypress-run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: E2E Smoke tests
        uses: cypress-io/github-action@v6
        # let's give this action an ID so we can refer
        # to its output values later
        id: smoke
        # Continue the build in case of an error, as we need to set the
        # commit status in the next step, both in case of success and failure
        continue-on-error: true
        with:
          record: true
          working-directory: testing/e2e
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.RECORDING_KEY }}
          CYPRESS_BASE_URL: ${{ secrets.CYPRESS_HOST }}
          CYPRESS_host: ${{ secrets.CYPRESS_HOST }}
          CYPRESS_username: ${{ secrets.CYPRESS_USER_NAME }}
          CYPRESS_password: ${{ secrets.CYPRESS_PASSWORD }}
