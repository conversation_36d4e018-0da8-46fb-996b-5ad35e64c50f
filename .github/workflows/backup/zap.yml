# Some extra/old git action jobs, stored here as a backup, should they need to be re-instantiated.

name: zap
on: workflow_dispatch

jobs:
  zap_scan:
    runs-on: ubuntu-latest
    name: <PERSON>an the webapplication
    env:
      CYPRESS_password: ${{ secrets.CYPRESS_PASSWORD }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: dev
      - name: Subtitute Password
        working-directory: ./testing/zap
        run: |
          echo Path: $(pwd)
          LOCAL_VAR=$(echo 'cypressidir$CYPRESS_PW' | base64)
          sed -i 's/@pw@/'$LOCAL_VAR'/g' dev.context
      - name: <PERSON><PERSON>
        uses: zaproxy/action-full-scan@v0.3.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          docker_name: "owasp/zap2docker-stable"
          target: "https://dev-faunalogic.apps.silver.devops.gov.bc.ca/"
          cmd_options: "-a"
