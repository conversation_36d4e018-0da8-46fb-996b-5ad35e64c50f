# Clean out all deployment artifacts when a PR is closed, but not merged.
# Will attempt to remove all artifacts from any PR that was opened against any branch (and then closed (not merged)), except for test and prod.
name: Clean Closed PR Artifacts

on:
  pull_request:
    types: [closed]

jobs:
  clean:
    name: Clean Deployment Artifacts for API, App, and DB in Dev and Tools environment
    runs-on: ubuntu-latest
    # Don't run if the PR was merged
    if: ${{ github.event.pull_request.merged != true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
    steps:
      # Install Node - for `node` and `npm` commands
      # Note: This already uses actions/cache internally, so repeat calls in subsequent jobs are not a performance hit
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Clean the app deployment artifacts
      - name: Clean APP Deployment
        working-directory: "app/.pipeline/"
        run: |
          npm ci
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=build
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=dev

      # Clean the database build/deployment artifacts
      - name: Clean Database Artifacts
        working-directory: "database/.pipeline/"
        run: |
          npm ci
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=build
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=dev

      # Clean the api deployment artifacts
      - name: Clean API Deployment
        working-directory: "api/.pipeline/"
        run: |
          npm ci
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=build
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=dev

      #   # Clean the queue deployment artifacts
      #   - name: Clean Queue Deployment
      #     working-directory: "./api/.pipeline/"
      #     run: |
      #       npm ci
      #       DEBUG=* npm run queue:clean -- --pr=$PR_NUMBER --env=build
      #       DEBUG=* npm run queue:clean -- --pr=$PR_NUMBER --env=dev

      # Clean the reamaining build/deployment artifacts
      - name: Clean remaining Artifacts
        env:
          POD_SELECTOR: faunalogic-platform
        run: |
          oc --namespace a0ec71-dev   get all,pvc,secret,pods,ReplicationController,DeploymentConfig,Deployment,HorizontalPodAutoscaler,imagestreamtag -o name | grep $POD_SELECTOR | grep $PR_NUMBER | awk '{print "oc delete --ignore-not-found " $1}' | bash
          oc --namespace a0ec71-tools get all,pvc,secret,pods,ReplicationController,DeploymentConfig,Deployment,HorizontalPodAutoscaler,imagestreamtag -o name | grep $POD_SELECTOR | grep $PR_NUMBER | awk '{print "oc delete --ignore-not-found " $1}' | bash
