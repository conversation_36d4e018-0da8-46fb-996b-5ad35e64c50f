# Static Deploy On OpenShift
# Builds and Deploys merged PR's to persistent pods/services/routes/etc in the OpenShift Dev or Test or Prod environment.
name: Static Deploy on OpenShift

on:
  pull_request:
    types: [closed]
    branches:
      - dev
      - test
      - prod

jobs:
  # Print variables for logging and debugging purposes
  checkEnv:
    name: Print Env variables
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    steps:
      - name: Print Env Vars
        run: |
          echo OC CLI Version: $(oc version)
          echo Git Base Ref: ${{ github.base_ref }}
          echo Git Change ID: ${{ github.event.number }}
          echo Git Pull Request Ref: ${{ github.event.pull_request.head.sha }}
          echo Git Event Name: ${{ github.event_name }}
          echo Git Event Action: ${{ github.event.action }}
          echo Git Branch Ref: ${{ github.head_ref }}
          echo PR in Draft: ${{ github.event.pull_request.draft }}

  # Scale down any existing OpenShift pods for this PR deployment
  # Why? The new pods will be deployed before the existing pods are terminated, and twice the resources will be needed
  # in that moment. If not enough resources are available to spin up the new pods, then they may fail to deploy.
  scaleDownPods:
    name: Scale down the pods for this PR
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
    needs:
      - checkEnv
    steps:
      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      - name: Scale down
        run: oc get DeploymentConfig,Deployment --namespace a0ec71-dev --selector env-id=$PR_NUMBER -o name | awk '{print "oc scale --replicas=0 " $1}' | bash

  # Checkout the repo once and cache it for use in subsequent jobs
  checkoutRepo:
    name: Checkout and cache target branch
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
    needs:
      - checkEnv
    steps:
      # Install Node - for `node` and `npm` commands
      # Note: This already uses actions/cache internally, so repeat calls in subsequent jobs are not a performance hit
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Checkout Target Branch
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      # Cache the repo
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          # Cache repo based on the commit sha that triggered the workflow
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

  # Build the web frontend app image
  buildAPP:
    name: Build APP Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - checkoutRepo
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install app pipeline node modules
      - name: Install app pipeline node modules
        working-directory: "app/.pipeline/"
        run: npm ci

      # Build the app image
      - name: Build APP Image
        working-directory: "app/.pipeline/"
        run: |
          DEBUG=* npm run build -- --pr=$PR_NUMBER --branch=$BRANCH --type=static

  # Build the Database image
  buildDatabase:
    name: Build Database Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - checkoutRepo
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install database pipeline node modules
      - name: Install database pipeline node modules
        working-directory: "database/.pipeline/"
        run: npm ci

      # Build the database image
      - name: Build Database Image
        working-directory: "database/.pipeline/"
        run: |
          DEBUG=* npm run db:build -- --pr=$PR_NUMBER --branch=$BRANCH --type=static

  # Build the Database Setup image
  buildDatabaseSetup:
    name: Build Database Setup Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - checkoutRepo
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install database pipeline node modules
      - name: Install database pipeline node modules
        working-directory: "database/.pipeline/"
        run: npm ci

      # Build the database image
      - name: Build Database Setup Image
        working-directory: "database/.pipeline/"
        run: |
          DEBUG=* npm run db-setup:build -- --pr=$PR_NUMBER --branch=$BRANCH --type=static

  # Build the API image
  buildAPI:
    name: Build API Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - checkoutRepo
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install api pipeline node modules
      - name: Install api pipeline node modules
        working-directory: "api/.pipeline/"
        run: npm ci

      # Build the api image
      - name: Build API Image
        working-directory: "api/.pipeline/"
        run: |
          DEBUG=* npm run build -- --pr=$PR_NUMBER --branch=$BRANCH --type=static

  #   # Build the Queue image
  #   buildQueue:
  #     name: Build Queue Image
  #     runs-on: ubuntu-latest
  #     timeout-minutes: 20
  #     if: ${{ github.event.pull_request.merged == true }}
  #     env:
  #       PR_NUMBER: ${{ github.event.number }}
  #       BRANCH: ${{ github.base_ref }}
  #     needs:
  #       - checkoutRepo
  #     steps:
  #       # Install Node - for `node` and `npm` commands
  #       - name: Setup Node.js
  #         uses: actions/setup-node@v4
  #         with:
  #           node-version: 20

  #       # Load repo from cache
  #       - name: Cache repo
  #         uses: actions/cache@v4
  #         id: cache-repo
  #         env:
  #           cache-name: cache-repo
  #         with:
  #           path: ${{ github.workspace }}/*
  #           key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

  #       # Install oc, which was removed from the ubuntu-latest image in v24.04
  #       - name: Install OpenShift CLI tools
  #         uses: redhat-actions/openshift-tools-installer@v1
  #         with:
  #           oc: "4.14"

  #       # Checkout the branch if not restored via cache
  #       - name: Checkout Target Branch
  #         if: steps.cache-repo.outputs.cache-hit != 'true'
  #         uses: actions/checkout@v4

  #       # Log in to OpenShift.
  #       # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
  #       # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
  #       - name: Log in to OpenShift
  #         run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

  #       # Install api pipeline node modules
  #       - name: Install api pipeline node modules
  #         working-directory: "api/.pipeline/"
  #         run: npm ci

  #       # Build the queue image
  #       - name: Build Queue Image
  #         working-directory: "./api/.pipeline/"
  #         run: |
  #           DEBUG=* npm run queue:build -- --pr=$PR_NUMBER --branch=$BRANCH --type=static

  # Deploy APP image
  deployAPP:
    name: Deploy APP Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - scaleDownPods
      - buildAPP
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install app pipeline node modules
      - name: Install app pipeline node modules
        working-directory: "app/.pipeline"
        run: npm ci

      # Deploy the app image
      - name: Deploy APP Image
        working-directory: "app/.pipeline"
        run: |
          DEBUG=* npm run deploy -- --pr=$PR_NUMBER --env=$BRANCH --branch=$BRANCH --type=static

  # Deploy Database image
  deployDatabase:
    name: Deploy Database Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - scaleDownPods
      - buildDatabase
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install database pipeline node modules
      - name: Install database pipeline node modules
        working-directory: "database/.pipeline/"
        run: npm ci

      # Deploy the database image
      - name: Deploy Database Image
        working-directory: "database/.pipeline/"
        run: |
          DEBUG=* npm run db:deploy -- --pr=$PR_NUMBER --env=$BRANCH --branch=$BRANCH --type=static

  # Deploy Database setup image
  deployDatabaseSetup:
    name: Deploy Database Setup Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - scaleDownPods
      - buildDatabaseSetup
      - deployDatabase
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install database pipeline node modules
      - name: Install database pipeline node modules
        working-directory: "database/.pipeline/"
        run: npm ci

      # Deploy the database setup image
      - name: Deploy Database Setup Image
        working-directory: "database/.pipeline/"
        run: |
          DEBUG=* npm run db-setup:deploy -- --pr=$PR_NUMBER --env=$BRANCH --branch=$BRANCH --type=static

  # Deploy API image
  deployAPI:
    name: Deploy API Image
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    needs:
      - scaleDownPods
      - buildAPI
      - deployDatabase
      - deployDatabaseSetup
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Install api pipeline node modules
      - name: Install api pipeline node modules
        working-directory: "api/.pipeline/"
        run: npm ci

      # Deploy the api image
      - name: Deploy API Image
        working-directory: "api/.pipeline/"
        run: |
          DEBUG=* npm run deploy -- --pr=$PR_NUMBER --env=$BRANCH --branch=$BRANCH --type=static

  #   # Deploy Queue image
  #   deployQueue:
  #     name: Deploy Queue Image
  #     runs-on: ubuntu-latest
  #     timeout-minutes: 20
  #     if: ${{ github.event.pull_request.merged == true }}
  #     env:
  #       PR_NUMBER: ${{ github.event.number }}
  #       BRANCH: ${{ github.base_ref }}
  #     needs:
  #       - scaleDownPods
  #       - buildQueue
  #       - deployDatabase
  #     steps:
  #       # Install Node - for `node` and `npm` commands
  #       - name: Setup Node.js
  #         uses: actions/setup-node@v4
  #         with:
  #           node-version: 20

  #       # Load repo from cache
  #       - name: Cache repo
  #         uses: actions/cache@v4
  #         id: cache-repo
  #         env:
  #           cache-name: cache-repo
  #         with:
  #           path: ${{ github.workspace }}/*
  #           key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

  #       # Install oc, which was removed from the ubuntu-latest image in v24.04
  #       - name: Install OpenShift CLI tools
  #         uses: redhat-actions/openshift-tools-installer@v1
  #         with:
  #           oc: "4.14"

  #       # Checkout the branch if not restored via cache
  #       - name: Checkout Target Branch
  #         if: steps.cache-repo.outputs.cache-hit != 'true'
  #         uses: actions/checkout@v4

  #       # Log in to OpenShift.
  #       # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
  #       # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
  #       - name: Log in to OpenShift
  #         run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

  #       # Install api pipeline node modules
  #       - name: Install api pipeline node modules
  #         working-directory: "api/.pipeline/"
  #         run: npm ci

  #       # Deploy the queue image
  #       - name: Deploy Queue Image
  #         working-directory: "./api/.pipeline/"
  #         run: |
  #           npm ci
  #           DEBUG=* npm run queue:deploy -- --pr=$PR_NUMBER --env=$BRANCH --branch=$BRANCH --type=static

  # Clean build/deployment artifacts
  clean:
    name: Clean Build/Deployment Artifacts
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ github.event.pull_request.merged == true }}
    needs:
      - deployDatabase
      - deployDatabaseSetup
      - deployAPI
      - deployAPP
    env:
      PR_NUMBER: ${{ github.event.number }}
      BRANCH: ${{ github.base_ref }}
    steps:
      # Install Node - for `node` and `npm` commands
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      # Clean the app deployment artifacts
      - name: Clean APP Deployment
        working-directory: "app/.pipeline/"
        run: |
          npm ci
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=build
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=dev

      # Clean the database build/deployment artifacts
      - name: Clean Database Artifacts
        working-directory: "database/.pipeline/"
        run: |
          npm ci
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=build
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=dev

      # Clean the api deployment artifacts
      - name: Clean API Deployment
        working-directory: "api/.pipeline/"
        run: |
          npm ci
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=build
          DEBUG=* npm run clean -- --pr=$PR_NUMBER --env=dev

      #   # Clean the queue deployment artifacts
      #   - name: Clean Queue Deployment
      #     working-directory: "./api/.pipeline/"
      #     run: |
      #       npm ci
      #       DEBUG=* npm run queue:clean -- --pr=$PR_NUMBER --env=build
      #       DEBUG=* npm run queue:clean -- --pr=$PR_NUMBER --env=dev

      # Clean the reamaining build/deployment artifacts
      - name: Clean remaining Artifacts
        env:
          POD_SELECTOR: faunalogic-platform
        run: |
          oc --namespace a0ec71-dev   get all,pvc,secret,pods,ReplicationController,DeploymentConfig,Deployment,HorizontalPodAutoscaler,imagestreamtag -o name | grep $POD_SELECTOR | grep $PR_NUMBER | awk '{print "oc delete --ignore-not-found " $1}' | bash
          oc --namespace a0ec71-tools get all,pvc,secret,pods,ReplicationController,DeploymentConfig,Deployment,HorizontalPodAutoscaler,imagestreamtag -o name | grep $POD_SELECTOR | grep $PR_NUMBER | awk '{print "oc delete --ignore-not-found " $1}' | bash
