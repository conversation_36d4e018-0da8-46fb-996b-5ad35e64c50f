name: Test Checks

on:
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
  push:
    branches:
      - dev

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  # Checkout the repo once and cache it for use in subsequent jobs
  checkoutRepo:
    name: Checkout and cache target branch
    runs-on: ubuntu-latest
    timeout-minutes: 20
    env:
      PR_NUMBER: ${{ github.event.number }}
    steps:
      # Install Node - for `node` and `npm` commands
      # Note: This already uses actions/cache internally, so repeat calls in subsequent jobs are not a performance hit
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Checkout Target Branch
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      # Cache the repo
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          # Cache repo based on the commit sha that triggered the workflow
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

  test:
    name: Running Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs:
      - checkoutRepo
    steps:
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Load repo from cache
      - name: Cache repo
        uses: actions/cache@v4
        id: cache-repo
        env:
          cache-name: cache-repo
        with:
          path: ${{ github.workspace }}/*
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ github.event.pull_request.head.sha }}

      # Checkout the branch if not restored via cache
      - name: Checkout Target Branch
        if: steps.cache-repo.outputs.cache-hit != 'true'
        uses: actions/checkout@v4

      # api
      - name: Cache api node modules
        uses: actions/cache@v4
        env:
          cache-name: cache-api-node-modules
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('api/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-

      - name: Install api dependencies
        working-directory: api
        run: npm install

      - name: Run api tests with coverage
        working-directory: api
        run: CI=true npm run coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{secrets.CODECOV_TOKEN}}
          fail_ci_if_error: false

      # app
      - name: Cache app node modules
        uses: actions/cache@v4
        env:
          cache-name: cache-app-node-modules
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('app/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-

      - name: Install app dependencies
        working-directory: app
        run: npm install

      - name: Run app tests with coverage
        working-directory: app
        run: CI=true npm run coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{secrets.CODECOV_TOKEN}}
          fail_ci_if_error: false
