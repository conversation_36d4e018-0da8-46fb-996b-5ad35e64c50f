# Switch to Draft
# Scales down running PODS when switching to Draft
name: Draft Scale Down

on:
  pull_request:
    types: [converted_to_draft]

jobs:
  scaleDownPods:
    name: Scale down the pods for this PR
    runs-on: ubuntu-latest
    timeout-minutes: 20
    env:
      PR_NUMBER: ${{ github.event.number }}
    steps:
      # Install oc, which was removed from the ubuntu-latest image in v24.04
      - name: Install OpenShift CLI tools
        uses: redhat-actions/openshift-tools-installer@v1
        with:
          oc: "4.14"

      # Log in to OpenShift.
      # Note: The secrets needed to log in are NOT available if the PR comes from a FORK.
      # PR's must originate from a branch off the original repo or else all openshift `oc` commands will fail.
      - name: Log in to OpenShift
        run: oc login --token=${{ secrets.TOOLS_SA_TOKEN }} --server=https://api.silver.devops.gov.bc.ca:6443

      - name: Scale down
        run: oc get DeploymentConfig,Deployment --namespace a0ec71-dev --selector env-id=$PR_NUMBER -o name | awk '{print "oc scale --replicas=0 " $1}' | bash
