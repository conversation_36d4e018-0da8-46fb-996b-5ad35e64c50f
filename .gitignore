# FaunaLogic Monorepo .gitignore
# Generated on 2025-06-27

# ===================================
# Database Service Specific
# ===================================

# Database data and backups
database/backups/*.backup
database/backups/*.sql
database/backups/*.sql.gz
database/backups/*.dump

# Database logs
database/logs/
database/*.log
database/*.csv

# Environment files with secrets
database/.env
database/.env.local
database/.env.production

# Database runtime files
database/tmp/
database/data/
database/pg_data/

# Docker volumes (if mounted locally)
database/postgres_data/
database/pgdata/

# ===================================
# Docker & Container Related
# ===================================

# Docker override files
docker-compose.override.yml
docker-compose.local.yml

# Docker build cache
.dockerignore.local

# Container logs
*.container.log

# ===================================
# IDE and Editor Files
# ===================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-workspace
*.sublime-project

# ===================================
# Operating System Files
# ===================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# Temporary and Cache Files
# ===================================

# General temporary files
*.tmp
*.temp
*.cache
*.pid
*.seed
*.pid.lock

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===================================
# Security and Secrets
# ===================================

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# API keys and secrets
secrets/
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der

# Config files with sensitive data
config/local.json
config/production.json
config/secrets.json

# ===================================
# API Service (Future)
# ===================================

# API logs and data
api/logs/
api/uploads/
api/tmp/
api/.env*

# API dependencies (when Node.js/Python is added)
api/node_modules/
api/__pycache__/
api/*.pyc
api/venv/
api/.venv/

# Python cache files (global)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===================================
# Frontend Service (Future)
# ===================================

# Frontend build outputs
frontend/dist/
frontend/build/
frontend/out/
frontend/.next/

# Frontend dependencies
frontend/node_modules/
frontend/.npm
frontend/.yarn

# Frontend cache
frontend/.cache/
frontend/.parcel-cache/
frontend/.nuxt/

# ===================================
# Backup and Archive Files
# ===================================

# Database backups
*.backup
*.bak
*.dump
*.sql.gz

# Archive files
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z

# ===================================
# Development Tools
# ===================================

# Coverage reports
coverage/
*.lcov
.nyc_output/

# Testing artifacts
test-results/
junit.xml
coverage.xml

# Profiling
*.prof

# ===================================
# Claude Code Specific
# ===================================

# Claude temporary files
.claude/
.anthropic/

# ===================================
# Git and Version Control
# ===================================

# Git merge artifacts
*.orig
*.rej

# Patch files
*.patch
*.diff

# ===================================
# Database Development Files
# ===================================

# SQL temporary files
*.sql~
*.sql.bak

# Database IDE files
*.sqbpro
*.sqbproject

# Migration rollback files
database/migrations/rollback/

# Test database files
database/test_data/
database/sample_data/
*.sqlite
*.sqlite3
*.db

# ===================================
# Documentation Build
# ===================================

# Generated documentation
docs/build/
docs/_build/
docs/.doctrees/

# ===================================
# Monitoring and Observability
# ===================================

# Monitoring data
metrics/
traces/
*.metrics
*.traces

# ===================================
# Project Specific Exclusions
# ===================================

# Spatial data files (large files)
*.shp
*.shx
*.dbf
*.prj
*.cpg
*.qix
*.kml
*.kmz
*.geojson
# Note: Small reference .geojson files should be explicitly added with git add -f

# Large test datasets
test_datasets/
sample_wildlife_data/

# Performance test results
perf_results/
benchmark_data/

# ===================================
# END OF .gitignore
# ===================================