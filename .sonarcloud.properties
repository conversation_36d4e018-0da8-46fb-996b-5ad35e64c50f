sonar.projectKey=bcgov_faunalogic-platform
sonar.projectKey=bcgov-sonarcloud

sonar.projectName=FaunaLogic-Platform
sonar.projectVersion=Autoscan

# Path to sources
sonar.sources=app/src,api/src
sonar.exclusions=**/*.test.*,**/*.spec.*,**/__tests__/**,**/__mocks__/**,**/__snapshots__/**,**/constants/**,**/shared-api-docs.ts
#sonar.inclusions=

# Path to tests
sonar.tests=app/src,api/src,database
sonar.test.inclusions=**/*.test.*,**/*.spec.*,**/__tests__/**,**/__snapshots__/**
# sonar.test.exclusions=
#sonar.junit.reportPaths= **Unit tests will report out in this format

# Coverage Reports
#sonar.javascript.lcov.reportPaths=

# Source encoding
sonar.sourceEncoding=UTF-8

# Exclusions for copy-paste detection
#sonar.cpd.exclusions=
