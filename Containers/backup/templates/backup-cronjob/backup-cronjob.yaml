---
kind: "Template"
apiVersion: template.openshift.io/v1
metadata:
  name: "backup-cronjob-template"
  annotations:
    description: "Scheduled Task to perform a Database Backup"
    tags: "cronjob,backup"
parameters:
  - name: "JOB_NAME"
    displayName: "Job Name"
    description: "Name of the Scheduled Job to Create."
    value: "backup-postgres"
    required: true
  - name: "JOB_PERSISTENT_STORAGE_NAME"
    displayName: "Backup Persistent Storage Name"
    description: "Pre-Created PVC to use for backup target"
    value: "backup"
    required: true
  - name: "SCHEDULE"
    displayName: "Cron Schedule"
    description: "Cron Schedule to Execute the Job (using local cluster system TZ)"
    # Currently targeting 1:00 AM Daily
    value: "0 1 * * *"
    required: true
  - name: "SOURCE_IMAGE_NAME"
    displayName: "Source Image Name"
    description: "The name of the image to use for this resource."
    required: true
    value: "backup-postgres"
  - name: "IMAGE_REGISTRY"
    description: "The base OpenShift docker registry"
    displayName: "Docker Image Registry"
    required: true
    value: "image-registry.openshift-image-registry.svc:5000"
  - name: "IMAGE_NAMESPACE"
    displayName: "Image Namespace"
    description: "The namespace of the OpenShift project containing the imagestream for the application."
    required: true
    value: a0ec71-tools
  - name: "TAG_NAME"
    displayName: "Environment TAG name"
    description: "The TAG name for this environment, e.g., dev, test, prod"
    required: true
    value: "dev"
  - name: "DATABASE_SERVICE_NAME"
    displayName: "Database Service Name"
    description: "The name of the database service."
    required: true
    value: "postgresql"
  - name: "DATABASE_DEFAULT_PORT"
    displayName: "Database Service Port"
    description: "The configured port for the database service"
    required: true
    value: "5432"
  - name: "DATABASE_NAME"
    displayName: "Database Name"
    description: "The name of the database."
    required: true
    value: "MyDatabase"
  - name: "DATABASE_DEPLOYMENT_NAME"
    displayName: "Database Deployment Name"
    description: "The name associated to the database deployment resources.  In particular, this is used to wire up the credentials associated to the database."
    required: true
    value: "postgresql"
  - name: DATABASE_USER_KEY_NAME
    displayName: Database User Key Name
    description:
      The database user key name stored in database deployment resources specified
      by DATABASE_DEPLOYMENT_NAME.
    required: true
    value: database-user
  - name: DATABASE_PASSWORD_KEY_NAME
    displayName: Database Password Key Name
    description:
      The database password key name stored in database deployment resources
      specified by DATABASE_DEPLOYMENT_NAME.
    required: true
    value: database-password
  - name: "BACKUP_STRATEGY"
    displayName: "Backup Strategy"
    description: "The strategy to use for backups; for example daily, or rolling."
    required: true
    value: "rolling"
  - name: "BACKUP_DIR"
    displayName: "The root backup directory"
    description: "The name of the root backup directory"
    required: true
    value: "/backups/"
  - name: "NUM_BACKUPS"
    displayName: "The number of backup files to be retained"
    description: "The number of backup files to be retained.  Used for the `daily` backup strategy.  Ignored when using the `rolling` backup strategy."
    required: false
    value: "5"
  - name: "DAILY_BACKUPS"
    displayName: "Number of Daily Backups to Retain"
    description: "The number of daily backup files to be retained.  Used for the `rolling` backup strategy."
    required: false
    value: "7"
  - name: "WEEKLY_BACKUPS"
    displayName: "Number of Weekly Backups to Retain"
    description: "The number of weekly backup files to be retained.  Used for the `rolling` backup strategy."
    required: false
    value: "4"
  - name: "MONTHLY_BACKUPS"
    displayName: "Number of Monthly Backups to Retain"
    description: "The number of monthly backup files to be retained.  Used for the `rolling` backup strategy."
    required: false
    value: "1"
  - name: "JOB_SERVICE_ACCOUNT"
    displayName: "Service Account Name"
    description: "Name of the Service Account To Exeucte the Job As."
    value: "default"
    required: true
  - name: "SUCCESS_JOBS_HISTORY_LIMIT"
    displayName: "Successful Job History Limit"
    description: "The number of successful jobs that will be retained"
    value: "5"
    required: true
  - name: "FAILED_JOBS_HISTORY_LIMIT"
    displayName: "Failed Job History Limit"
    description: "The number of failed jobs that will be retained"
    value: "2"
    required: true
  - name: "JOB_BACKOFF_LIMIT"
    displayName: "Job Backoff Limit"
    description: "The number of attempts to try for a successful job outcome"
    value: "0"
    required: false
objects:
- kind: ConfigMap
  apiVersion: v1
  metadata:
    name: "${JOB_NAME}-config"
    labels:
      template: "${JOB_NAME}-config-template"
      cronjob: "${JOB_NAME}"
  data:
    DATABASE_SERVICE_NAME: "${DATABASE_SERVICE_NAME}"
    DEFAULT_PORT: "${DATABASE_DEFAULT_PORT}"
    POSTGRESQL_DATABASE: "${DATABASE_NAME}"
    # BACKUP_STRATEGY: "daily"
    BACKUP_STRATEGY: "rolling"
    RETENTION.NUM_BACKUPS: "${NUM_BACKUPS}"
    RETENTION.DAILY_BACKUPS: "${DAILY_BACKUPS}"
    RETENTION.WEEKLY_BACKUPS: "${WEEKLY_BACKUPS}"
    RETENTION.MONTHLY_BACKUPS: "${MONTHLY_BACKUPS}"
- kind: "CronJob"
  apiVersion: "batch/v1beta1"
  metadata:
    name: "${JOB_NAME}"
    labels:
      template: "${JOB_NAME}-cronjob"
      cronjob: "${JOB_NAME}"
  spec:
    schedule: "${SCHEDULE}"
    concurrencyPolicy: "Forbid"
    successfulJobsHistoryLimit: "${{SUCCESS_JOBS_HISTORY_LIMIT}}"
    failedJobsHistoryLimit: "${{FAILED_JOBS_HISTORY_LIMIT}}"
    jobTemplate:
      metadata:
        labels:
          template: "${JOB_NAME}-job"
          cronjob: "${JOB_NAME}"
      spec:
        backoffLimit: ${{JOB_BACKOFF_LIMIT}}
        template:
          metadata:
            labels:
              template: "${JOB_NAME}-job"
              cronjob: "${JOB_NAME}"
          spec:
            containers:
              - name: "${JOB_NAME}-cronjob"
                image: "${IMAGE_REGISTRY}/${IMAGE_NAMESPACE}/${SOURCE_IMAGE_NAME}:${TAG_NAME}"
                # image: backup
                command:
                  - "/bin/bash"
                  - "-c"
                  - "/backup.sh -1"
                volumeMounts:
                  - mountPath: "${BACKUP_DIR}"
                    name: "backup"
                env:
                  - name: BACKUP_DIR
                    value: "${BACKUP_DIR}"
                  - name: BACKUP_STRATEGY
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: BACKUP_STRATEGY
                  - name: NUM_BACKUPS
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: RETENTION.NUM_BACKUPS
                        optional: true
                  - name: DAILY_BACKUPS
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: RETENTION.DAILY_BACKUPS
                        optional: true
                  - name: WEEKLY_BACKUPS
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: RETENTION.WEEKLY_BACKUPS
                        optional: true
                  - name: MONTHLY_BACKUPS
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: RETENTION.MONTHLY_BACKUPS
                        optional: true
                  - name: DATABASE_SERVICE_NAME
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: DATABASE_SERVICE_NAME
                  - name: DEFAULT_PORT
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: DEFAULT_PORT
                        optional: true
                  - name: POSTGRESQL_DATABASE
                    valueFrom:
                      configMapKeyRef:
                        name: "${JOB_NAME}-config"
                        key: POSTGRESQL_DATABASE
                  - name: DATABASE_USER
                    valueFrom:
                      secretKeyRef:
                        name: "${DATABASE_DEPLOYMENT_NAME}"
                        key: "${DATABASE_USER_KEY_NAME}"
                  - name: DATABASE_PASSWORD
                    valueFrom:
                      secretKeyRef:
                        name: "${DATABASE_DEPLOYMENT_NAME}"
                        key: "${DATABASE_PASSWORD_KEY_NAME}"
            volumes:
              - name: backup
                persistentVolumeClaim:
                  claimName: "${JOB_PERSISTENT_STORAGE_NAME}"
            restartPolicy: "Never"
            terminationGracePeriodSeconds: 30
            activeDeadlineSeconds: 1600
            dnsPolicy: "ClusterFirst"
            serviceAccountName: "${JOB_SERVICE_ACCOUNT}"
            serviceAccount: "${JOB_SERVICE_ACCOUNT}"
