kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: backup-build-template
  creationTimestamp: null
objects:
  - kind: ImageStream
    apiVersion: v1
    metadata:
      name: ${NAME}
  - kind: BuildConfig
    apiVersion: v1
    metadata:
      name: ${NAME}
      labels:
        app: ${NAME}
    spec:
      triggers:
        - type: ImageChange
        - type: ConfigChange
      runPolicy: Serial
      source:
        type: Git
        git:
          uri: ${GIT_REPO_URL}
          ref: ${GIT_REF}
        contextDir: ${SOURCE_CONTEXT_DIR}
      strategy:
        type: Docker
        dockerStrategy:
          pullSecret:
            name: a0ec71-artifactory
          from:
            kind: DockerImage
            name: ${BASE_IMAGE_FOR_BUILD}
          dockerfilePath: ${DOCKER_FILE_PATH}
      output:
        to:
          kind: ImageStreamTag
          name: ${NAME}:${OUTPUT_IMAGE_TAG}
parameters:
  - name: NAME
    displayName: Name
    description: The name assigned to all of the resources.  Use 'backup-{database name}' depending on your database provider
    required: true
    value: backup-postgres
  - name: GIT_REPO_URL
    displayName: Git Repo URL
    description: The URL to your GIT repo.
    required: true
    value: https://github.com/bcgov/faunalogic-platform.git
  - name: GIT_REF
    displayName: Git Reference
    description: The git reference or branch.
    required: true
    value: dev
  - name: SOURCE_CONTEXT_DIR
    displayName: Source Context Directory
    description: The source context directory.
    required: false
    value: /containers/backup/docker
  - name: DOCKER_FILE_PATH
    displayName: Docker File
    description: The path and file of the docker file defining the build.  Choose either 'Dockerfile' for Postgres builds or 'Dockerfile_Mongo' for MongoDB builds or 'Dockerfile_MSSQL' for MSSQL builds.
    required: false
    value: Dockerfile
  - name: OUTPUT_IMAGE_TAG
    displayName: Output Image Tag
    description: The tag given to the built image.
    required: true
    value: latest-s3
  - name: BASE_IMAGE_FOR_BUILD
    displayName: FROM Image Tag
    description: Base image to build from.  Docker creds or Artificatory setup may be needed to alleviate docker rate-limiting
    required: true
    value: artifacts.developer.gov.bc.ca/docker-remote/centos/postgresql-12-centos7
