kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: backup-deployment-template
objects:
  - kind: NetworkPolicy
    apiVersion: networking.k8s.io/v1
    metadata:
      name: ${NAME}
      labels:
        name: ${NAME}
        app: ${APP_NAME}
        env: ${TAG_NAME}
    spec:
      description: |
        Allow the ${NAME} to access databases in this namespace.
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  name: ${NAME}
                  app: ${APP_NAME}
                  role: ${ROLE}
                  env: ${TAG_NAME}
              namespaceSelector:
                matchLabels:
                  name: ${NAMESPACE_NAME}
                  environment: ${TAG_NAME}
      podSelector:
        matchLabels:
          backup: "true"
          env: ${TAG_NAME}

  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: ${BACKUP_VOLUME_NAME}
      labels:
        name: ${NAME}
        app: ${APP_NAME}
        role: ${ROLE}
        env: ${TAG_NAME}
    spec:
      storageClassName: ${BACKUP_VOLUME_CLASS}
      accessModes:
        - ReadWriteOnce
      resources:
        requests:
          storage: ${BACKUP_VOLUME_SIZE}

  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: ${VERIFICATION_VOLUME_NAME}
      labels:
        name: ${NAME}
        app: ${APP_NAME}
        role: ${ROLE}
        env: ${TAG_NAME}
    spec:
      storageClassName: ${VERIFICATION_VOLUME_CLASS}
      accessModes:
        - ReadWriteOnce
      resources:
        requests:
          storage: ${VERIFICATION_VOLUME_SIZE}

  - kind: Secret
    apiVersion: v1
    metadata:
      name: ${NAME}
      labels:
        name: ${NAME}
        app: ${APP_NAME}
        role: ${ROLE}
        env: ${TAG_NAME}
    type: Opaque
    stringData:
      webhook-url: ${WEBHOOK_URL}
      webhook-url-host: ${WEBHOOK_URL_HOST}

  - kind: Secret
    apiVersion: v1
    metadata:
      name: ${FTP_SECRET_KEY}
      labels:
        name: ${NAME}
        app: ${APP_NAME}
        role: ${ROLE}
        env: ${TAG_NAME}
    type: Opaque
    stringData:
      ftp-url: ${FTP_URL}
      ftp-user: ${FTP_USER}
      ftp-password: ${FTP_PASSWORD}
      ftp-url-host: ${FTP_URL_HOST}

  - kind: DeploymentConfig
    apiVersion: v1
    metadata:
      name: ${NAME}
      labels:
        name: ${NAME}
        app: ${APP_NAME}
        role: ${ROLE}
        env: ${TAG_NAME}
    spec:
      strategy:
        type: Recreate
      triggers:
        - type: ConfigChange
        - type: ImageChange
          imageChangeParams:
            automatic: true
            containerNames:
              - ${NAME}
            from:
              kind: ImageStreamTag
              namespace: ${IMAGE_NAMESPACE}
              name: ${SOURCE_IMAGE_NAME}:${TAG_NAME}
      replicas: 1
      selector:
        name: ${NAME}
      template:
        metadata:
          name: ${NAME}
          labels:
            name: ${NAME}
            app: ${APP_NAME}
            role: ${ROLE}
            env: ${TAG_NAME}
        spec:
          volumes:
            - name: ${BACKUP_VOLUME_NAME}
              persistentVolumeClaim:
                claimName: ${BACKUP_VOLUME_NAME}
            - name: ${VERIFICATION_VOLUME_NAME}
              persistentVolumeClaim:
                claimName: ${VERIFICATION_VOLUME_NAME}
            - name: ${NAME}-config-volume
              configMap:
                name: ${CONFIG_MAP_NAME}
                items:
                  - key: ${CONFIG_FILE_NAME}
                    path: ${CONFIG_FILE_NAME}
          containers:
            - name: ${NAME}
              image: ""
              ports: []
              env:
                - name: BACKUP_STRATEGY
                  value: ${BACKUP_STRATEGY}
                - name: BACKUP_DIR
                  value: ${BACKUP_DIR}
                - name: NUM_BACKUPS
                  value: ${NUM_BACKUPS}
                - name: DAILY_BACKUPS
                  value: ${DAILY_BACKUPS}
                - name: WEEKLY_BACKUPS
                  value: ${WEEKLY_BACKUPS}
                - name: MONTHLY_BACKUPS
                  value: ${MONTHLY_BACKUPS}
                - name: BACKUP_PERIOD
                  value: ${BACKUP_PERIOD}
                - name: DATABASE_SERVICE_NAME
                  value: ${DATABASE_SERVICE_NAME}
                - name: DATABASE_NAME
                  value: ${DATABASE_NAME}
                - name: MONGODB_AUTHENTICATION_DATABASE
                  value: ${MONGODB_AUTHENTICATION_DATABASE}
                - name: TABLE_SCHEMA
                  value: ${TABLE_SCHEMA}
                - name: DATABASE_USER
                  valueFrom:
                    secretKeyRef:
                      name: ${DATABASE_DEPLOYMENT_NAME}
                      key: ${DATABASE_USER_KEY_NAME}
                - name: DATABASE_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: ${DATABASE_DEPLOYMENT_NAME}
                      key: ${DATABASE_PASSWORD_KEY_NAME}
                - name: FTP_URL
                  valueFrom:
                    secretKeyRef:
                      name: ${FTP_SECRET_KEY}
                      key: ftp-url
                - name: FTP_USER
                  valueFrom:
                    secretKeyRef:
                      name: ${FTP_SECRET_KEY}
                      key: ftp-user
                - name: FTP_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: ${FTP_SECRET_KEY}
                      key: ftp-password
                - name: WEBHOOK_URL
                  valueFrom:
                    secretKeyRef:
                      name: ${NAME}
                      key: webhook-url
                - name: ENVIRONMENT_FRIENDLY_NAME
                  value: ${ENVIRONMENT_FRIENDLY_NAME}
                - name: ENVIRONMENT_NAME
                  value: ${ENVIRONMENT_NAME}
                - name: OBJECT_STORE_URL
                  valueFrom:
                    secretKeyRef:
                      name: faunalogic-object-store
                      key: object_store_url
                - name: OBJECT_STORE_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: faunalogic-object-store
                      key: object_store_access_key_id
                - name: OBJECT_STORE_SECRET_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: faunalogic-object-store
                      key: object_store_secret_key_id
                - name: OBJECT_STORE_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      name: faunalogic-object-store
                      key: object_store_bucket_name
              resources:
                requests:
                  cpu: ${CPU_REQUEST}
                  memory: ${MEMORY_REQUEST}
                limits:
                  cpu: ${CPU_LIMIT}
                  memory: ${MEMORY_LIMIT}
              volumeMounts:
                - name: ${BACKUP_VOLUME_NAME}
                  mountPath: ${BACKUP_DIR}
                - name: ${VERIFICATION_VOLUME_NAME}
                  mountPath: ${VERIFICATION_VOLUME_MOUNT_PATH}
                - name: ${NAME}-config-volume
                  mountPath: ${CONFIG_MOUNT_PATH}${CONFIG_FILE_NAME}
                  subPath: ${CONFIG_FILE_NAME}

parameters:
  - name: NAME
    displayName: Name
    description: The name assigned to all of the resources.  Use 'backup-{database name}' depending on your database provider
    required: true
    value: backup-postgres
  - name: SOURCE_IMAGE_NAME
    displayName: Source Image Name
    description: The name of the image to use for this resource.  Use 'backup-{database name}' depending on your database provider
    required: true
    value: backup-postgres
  - name: APP_NAME
    displayName: App Name
    description: Used to group resources together.  Defaults to backup-container
    required: true
    value: backup-container
  - name: ROLE
    displayName: ROLE
    description: The role assigned to all of the resources.  Defaults to backup-container
    required: true
    value: backup-container
  - name: NAMESPACE_NAME
    displayName: Namespace Name
    description: The name of the namespace being deployed to..
    required: true
    value: a0ec71-dev
  - name: IMAGE_NAMESPACE
    displayName: Image Namespace
    description: The namespace of the OpenShift project containing the imagestream for the application.
    required: true
    value: a0ec71-tools
  - name: TAG_NAME
    displayName: Environment TAG name
    description: The TAG name for this environment, e.g., dev, test, prod
    required: true
    value: dev
  - name: DATABASE_SERVICE_NAME
    displayName: Database Service Name
    description: Used for backward compatibility only. Not needed when using the recommended 'backup.conf' configuration.  The name of the database service.
    required: false
    value: biohub-platform-db-postgresql-dev-deploy
  - name: DATABASE_NAME
    displayName: Database Name
    description: Used for backward compatibility only. Not needed when using the recommended 'backup.conf' configuration.  The name of the database.
    required: false
    value: faunalogic
  - name: MONGODB_AUTHENTICATION_DATABASE
    displayName: MongoDB Authentication Database
    description: This is only required if you are backing up mongo database with a separate authentication database.
    required: false
    value: ""
  - name: DATABASE_DEPLOYMENT_NAME
    displayName: Database Deployment Name
    description: The name associated to the database deployment resources.  In particular, this is used to wire up the credentials associated to the database.
    required: true
    value: faunalogic-platform-db-postgresql-dev-deploy
  - name: DATABASE_USER_KEY_NAME
    displayName: Database User Key Name
    description: The database user key name stored in database deployment resources specified by DATABASE_DEPLOYMENT_NAME.
    required: true
    value: database-user
  - name: DATABASE_PASSWORD_KEY_NAME
    displayName: Database Password Key Name
    description: The database password key name stored in database deployment resources specified by DATABASE_DEPLOYMENT_NAME.
    required: true
    value: database-user-password
  - name: MSSQL_SA_PASSWORD
    displayName: MSSQL SA Password
    description: The database password to use for the local backup database.
    required: false
  - name: TABLE_SCHEMA
    displayName: Table Schema
    description: The table schema for your database.  Used for Postgres backups.
    required: true
    value: biohub
  - name: BACKUP_STRATEGY
    displayName: Backup Strategy
    description: The strategy to use for backups; for example daily, or rolling.
    required: true
    value: rolling
  - name: FTP_SECRET_KEY
    displayName: FTP Secret Key
    description: The FTP secret key is used to wire up the credentials associated to the FTP.
    required: false
    value: ftp-secret
  - name: FTP_URL
    displayName: FTP Server URL
    description: The URL of the backup FTP server
    required: false
    value: ""
  - name: FTP_URL_HOST
    displayName: Ftp URL Hostname
    description: Ftp URL Hostname.  The backup-deploy.overrides.sh will parse this from the supplied FTP_URL, and fetch it from a secret for updates.
    required: false
    value:
  - name: FTP_USER
    displayName: FTP user name
    description: FTP user name
    required: false
    value: ""
  - name: FTP_PASSWORD
    displayName: FTP password
    description: FTP password
    required: false
    value: ""
  - name: WEBHOOK_URL
    displayName: Webhook URL
    description: The URL of the webhook to use for notifications.  If not specified, the webhook integration feature is disabled.
    required: false
    value: ""
  - name: WEBHOOK_URL_HOST
    displayName: Webhook URL Hostname
    description: Webhook URL Hostname.  The backup-deploy.overrides.sh will parse this from the supplied WEBHOOK_URL, and fetch it from a secret for updates.
    required: false
    value:
  - name: ENVIRONMENT_FRIENDLY_NAME
    displayName: Friendly Environment Name
    description: The human readable name of the environment.  This variable is used by the webhook integration to identify the environment in which the backup notifications originate.
    required: false
    value: BioHub
  - name: ENVIRONMENT_NAME
    displayName: Environment Name (Environment Id)
    description: The name or Id of the environment.  This variable is used by the webhook integration and by the NetworkSecurityPolicies to identify the environment in which the backup notifications originate.
    required: true
    value: BioHub
  - name: BACKUP_DIR
    displayName: The root backup directory
    description: The name of the root backup directory.  The backup volume will be mounted to this directory.
    required: true
    value: /backups/
  - name: NUM_BACKUPS
    displayName: The number of backup files to be retained
    description: Used for backward compatibility only.  Ignored when using the recommended `rolling` backup strategy.  The number of backup files to be retained.  Used for the `daily` backup strategy.
    required: false
    value: ""
  - name: DAILY_BACKUPS
    displayName: Number of Daily Backups to Retain
    description: The number of daily backup files to be retained.  Used for the `rolling` backup strategy.
    required: false
    value: ""
  - name: WEEKLY_BACKUPS
    displayName: Number of Weekly Backups to Retain
    description: The number of weekly backup files to be retained.  Used for the `rolling` backup strategy.
    required: false
    value: ""
  - name: MONTHLY_BACKUPS
    displayName: Number of Monthly Backups to Retain
    description: The number of monthly backup files to be retained.  Used for the `rolling` backup strategy.
    required: false
    value: ""
  - name: BACKUP_PERIOD
    displayName: Period (d,m,s) between backups in a format used by the sleep command
    description: Used for backward compatibility only.  Ignored when using the recommended `backup.conf` and cron backup strategy.  Period (d,m,s) between backups in a format used by the sleep command
    required: false
    value: ""
  - name: CONFIG_FILE_NAME
    displayName: Config File Name
    description: The name of the configuration file.
    required: true
    value: backup.conf
  - name: CONFIG_MAP_NAME
    displayName: Config Map Name
    description: The name of the configuration map.
    required: true
    value: backup-conf
  - name: CONFIG_MOUNT_PATH
    displayName: Config Mount Path
    description: The path to use to mount the config file.
    required: true
    value: /
  - name: BACKUP_VOLUME_NAME
    displayName: Backup Volume Name
    description: The name of the persistent volume used to store the backups.
    required: true
    value: backup
  - name: BACKUP_VOLUME_SIZE
    displayName: Backup Volume Size
    description: The size of the persistent volume used to store the backups, e.g. 512Mi, 1Gi, 2Gi.  Ensure this is sized correctly.  Refer to the container documentation for details.
    required: true
    value: 20Gi
  - name: BACKUP_VOLUME_CLASS
    displayName: Backup Volume Class
    description: The class of the persistent volume used to store the backups; netapp-file-standard is the recommended default.
    required: true
    value: netapp-file-backup
  - name: VERIFICATION_VOLUME_NAME
    displayName: Verification Volume Name
    description: The name for the verification volume, used for restoring and verifying backups.
    required: false
    value: backup-verification
  - name: VERIFICATION_VOLUME_SIZE
    displayName: Backup Volume Size
    description: The size of the persistent volume used for restoring and verifying backups, e.g. 512Mi, 1Gi, 2Gi.  Ensure this is sized correctly.  It should be large enough to contain your largest database.
    required: true
    value: 1Gi
  - name: VERIFICATION_VOLUME_CLASS
    displayName: Backup Volume Class
    description: The class of the persistent volume used for restoring and verifying backups; netapp-file-standard, netapp-block-standard.
    required: true
    value: netapp-file-standard
  - name: VERIFICATION_VOLUME_MOUNT_PATH
    displayName: Verification Volume Mount Path
    description: The path on which to mount the verification volume.  This is used by the database server to contain the database configuration and data files. For Mongo, please use /var/lib/mongodb/data . For MSSQL, please use /var/opt/mssql/data. For MariaDB, please use /var/lib/mysql/data
    required: true
    value: /var/lib/pgsql/data
  - name: CPU_REQUEST
    displayName: Resources CPU Request
    description: The resources CPU request (in cores) for this build.
    required: true
    value: "0"
  - name: CPU_LIMIT
    displayName: Resources CPU Limit
    description: The resources CPU limit (in cores) for this build.
    required: true
    value: "0"
  - name: MEMORY_REQUEST
    displayName: Resources Memory Request
    description: The resources Memory request (in Mi, Gi, etc) for this build.
    required: true
    value: 0Mi
  - name: MEMORY_LIMIT
    displayName: Resources Memory Limit
    description: The resources Memory limit (in Mi, Gi, etc) for this build.
    required: true
    value: 0Mi
  - name: OBJECT_STORE_URL
    displayName: Object Store URL
    description: URL for the object store
  - name: OBJECT_STORE_ACCESS_KEY_ID
    displayName: Access Key
    description: Access Key for the object store
  - name: OBJECT_STORE_SECRET_KEY_ID
    displayName: Secret Key
    description: Object Store Secret key
  - name: OBJECT_STORE_BUCKET_NAME
    displayName: Bucket name
    description: Object Store Bucket Name
