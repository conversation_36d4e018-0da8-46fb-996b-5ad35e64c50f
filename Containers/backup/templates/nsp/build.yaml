---
kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: global-nsp-build-template
objects:
  - kind: NetworkPolicy
    apiVersion: networking.k8s.io/v1
    metadata:
      name: deny-by-default
    spec:
      description: |
        Deny all traffic by default.
      podSelector: {}
      ingress: []

  - kind: NetworkSecurityPolicy
    apiVersion: security.devops.gov.bc.ca/v1alpha1
    metadata:
      name: any-to-any
    spec:
      description: |
        Disable Aporeto policies - Allow all pods within the namespace to communicate.
      source:
        - - $namespace=${NAMESPACE_NAME}-${ENV_NAME}
      destination:
        - - $namespace=${NAMESPACE_NAME}-${ENV_NAME}

  - kind: NetworkSecurityPolicy
    apiVersion: security.devops.gov.bc.ca/v1alpha1
    metadata:
      name: any-to-external
    spec:
      description: |
        Disable Aporeto policies - Allow all pods within the namespace full access to external systems.
      source:
        - - $namespace=${NAMESPACE_NAME}-${ENV_NAME}
      destination:
        - - ext:network=any

parameters:
  - name: NAMESPACE_NAME
    displayName: The target namespace for the resources.
    required: true
    value:
  - name: ENV_NAME
    displayName: Environment Name
    description: Environment name.  For the build environment this will typically be 'tools'
    required: true
    value: tools
