kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: clamav
parameters:
  - name: NAME
    displayName: Name
    description: The name assigned to all of the objects defined in this template.
    required: true
    value: clamav
  - name: GIT_SOURCE_URL
    displayName: GIT Source Repo URL
    description: A GIT URL to your source code.
    required: true
    value: https://github.com/bcgov/clamav.git
  - name: GIT_REF
    displayName: Git Reference
    description: The git reference or branch.
    required: true
    value: master
objects:
  - kind: ImageStream
    apiVersion: v1
    metadata:
      name: "${NAME}"
  - kind: BuildConfig
    apiVersion: v1
    metadata:
      name: "${NAME}-build"
      creationTimestamp:
      labels:
        app: "${NAME}"
    spec:
      runPolicy: Serial
      completionDeadlineSeconds: 1800
      triggers:
        - type: ConfigChange
      source:
        type: Git
        git:
          uri: "${GIT_SOURCE_URL}"
          ref: "${GIT_REF}"
      strategy:
        type: Docker
        dockerStrategy:
          env:
            - name: CLAMAV_NO_MILTERD
              value: "true"
      output:
        to:
          kind: ImageStreamTag
          name: "${NAME}:latest"
      resources:
        requests:
          cpu: 100m
          memory: 2Gi
        limits:
          cpu: "1"
          memory: 4Gi
