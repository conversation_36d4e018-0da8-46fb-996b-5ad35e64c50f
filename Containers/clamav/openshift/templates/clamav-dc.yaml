kind: Template
apiVersion: v1
metadata:
  name: clamav
parameters:
  - name: BASE_IMAGE_REGISTRY_URL
    description: The base image registry URL
    required: true
    value: image-registry.openshift-image-registry.svc:5000
  - name: IMAGE_NAMESPACE
    description: The namespace where to get the above image name
    displayName: Image Namespace
    required: true
    value: af2668-tools
  - name: NAME
    description:
      The name assigned to all of the openshift objects defined in this template.
      It is also the name of runtime image you want.
    displayName: Name
    required: true
    value: clamav
  - name: TAG_NAME
    description: The TAG name for this environment, e.g., dev, test, prod, latest
    displayName: Env TAG name
    value: latest
  - name: ROLE_NAME
    description: The name of the role label, used to uniquely identify this deployment
    displayName: Role Name
    value: clamav
objects:
  - kind: Deployment
    apiVersion: apps/v1
    metadata:
      creationTimestamp:
      labels:
        app: "${NAME}"
        role: "${ROLE_NAME}"
      name: "${NAME}"
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: "${NAME}"
          deployment: "${NAME}"
      strategy:
        type: RollingUpdate
        rollingUpdate:
          maxSurge: 25%
          maxUnavailable: 25%
      template:
        metadata:
          creationTimestamp:
          labels:
            app: "${NAME}"
            role: "${ROLE_NAME}"
            deployment: "${NAME}"
        spec:
          containers:
            - image: ${BASE_IMAGE_REGISTRY_URL}/${IMAGE_NAMESPACE}/${NAME}:${TAG_NAME}
              imagePullPolicy: Always
              name: "${NAME}"
              ports:
                - containerPort: 3310
                  protocol: TCP
              env:
                - name: RealIpFrom
                  value: "${REAL_IP_FROM}"
                - name: AdditionalRealIpFromRules
                  value: "${AdditionalRealIpFromRules}"
                - name: IpFilterRules
                  value: "${IpFilterRules}"
              resources:
                requests:
                  cpu: 1000m
                  memory: 1Gi
                limits:
                  cpu: 2000m
                  memory: 2Gi
          dnsPolicy: ClusterFirst
          restartPolicy: Always
          securityContext: {}
          terminationGracePeriodSeconds: 30
      test: false
      triggers:
        - type: ConfigChange
        - type: ImageChange
          imageChangeParams:
            automatic: true
            containerNames:
              - "${NAME}"
            from:
              kind: ImageStreamTag
              namespace: "${IMAGE_NAMESPACE}"
              name: "${NAME}:${TAG_NAME}"

  - kind: Service
    apiVersion: v1
    metadata:
      creationTimestamp:
      labels:
        app: "${NAME}"
      name: "${NAME}"
    spec:
      ports:
        - name: 3310-tcp
          port: 3310
          protocol: TCP
          targetPort: 3310
      selector:
        app: "${NAME}"
        deployment: "${NAME}"
      sessionAffinity: None
      type: ClusterIP
