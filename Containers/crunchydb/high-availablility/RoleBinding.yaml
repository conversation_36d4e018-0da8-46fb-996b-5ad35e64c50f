---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    vendor: crunchydata
  name: crunchy-monitoring
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    vendor: crunchydata
  name: crunchy-monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: crunchy-monitoring
subjects:
- kind: ServiceAccount
  name: prometheus-sa
  namespace: a0ec71-tools
