apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    vendor: crunchydata
  name: alertmanager
  namespace: a0ec71-tools
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    serviceaccounts.openshift.io/oauth-redirectreference.primary: '{"kind":"OAuthRedirectReference","apiVersion":"v1","reference":{"kind":"Route","name":"crunchy-grafana"}}'
  labels:
    vendor: crunchydata
  name: grafana
  namespace: a0ec71-tools
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    vendor: crunchydata
  name: prometheus-sa
  namespace: a0ec71-tools
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    vendor: crunchydata
  name: prometheus-cr
  namespace: a0ec71-tools
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    vendor: crunchydata
  name: prometheus-crb
  namespace: a0ec71-tools
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: prometheus-cr
subjects:
- kind: ServiceAccount
  name: prometheus-sa
  namespace: a0ec71-tools
---
apiVersion: v1
data:
  alertmanager.yml: |
    ###
    #
    # Copyright 2017-2022 Crunchy Data Solutions, Inc. All Rights Reserved.
    #
    ###

    # Based on upstream example file found here: https://github.com/prometheus/alertmanager/blob/master/doc/examples/simple.yml
    global:
        smtp_smarthost: 'apps.smtp.gov.bc.ca:25'
        smtp_require_tls: false
        smtp_from: 'Biohub HA Alertmanager <<EMAIL>>'

    # templates:
    # - '/etc/alertmanager/template/*.tmpl'

    inhibit_rules:
    # Apply inhibition of warning if the alertname for the same system and service is already critical
    - source_match:
        severity: 'critical'
      target_match:
        severity: 'warning'
      equal: ['alertname', 'job', 'service']

    receivers:
    - name: 'default-receiver'
      email_configs:
      - to: '<EMAIL>'
        send_resolved: true

    ## Examples of alternative alert receivers. See documentation for more info on how to configure these fully
    #- name: 'pagerduty-dba'
    #  pagerduty_configs:
    #      - service_key: <RANDOMKEYSTUFF>

    #- name: 'pagerduty-sre'
    #  pagerduty_configs:
    #      - service_key: <RANDOMKEYSTUFF>

    #- name: 'dba-team'
    #  email_configs:
    #  - to: '<EMAIL>'
    #    send_resolved: true

    #- name: 'sre-team'
    #  email_configs:
    #  - to: '<EMAIL>'
    #    send_resolved: true

    route:
        receiver: default-receiver
        group_by: [severity, service, job, alertname]
        group_wait: 30s
        group_interval: 5m
        repeat_interval: 24h

    ## Example routes to show how to route outgoing alerts based on the content of that alert
    #    routes:
    #    - match_re:
    #        service: ^(postgresql|mysql|oracle)$
    #      receiver: dba-team
    #      # sub route to send critical dba alerts to pagerduty
    #      routes:
    #      - match:
    #          severity: critical
    #        receiver: pagerduty-dba
    #
    #    - match:
    #        service: system
    #      receiver: sre-team
    #      # sub route to send critical sre alerts to pagerduty
    #      routes:
    #      - match:
    #          severity: critical
    #        receiver: pagerduty-sre
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    vendor: crunchydata
  name: alertmanager-config
  namespace: a0ec71-tools
---
apiVersion: v1
data:
  crunchy-alert-rules-pg.yml: |
    ###
    #
    # Copyright 2017-2022 Crunchy Data Solutions, Inc. All Rights Reserved.
    #
    ###

    groups:
    - name: alert-rules
      rules:

    ########## EXPORTER RULES ##########
      - alert: PGExporterScrapeError
        expr: pg_exporter_last_scrape_error > 0
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          summary: 'Postgres Exporter running on {{ $labels.job }} (instance: {{ $labels.instance }}) is encountering scrape errors processing queries. Error count: ( {{ $value }} )'


    ########## POSTGRESQL RULES ##########
      - alert: PGIsUp
        expr: pg_up < 1
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          summary: 'postgres_exporter running on {{ $labels.job }} is unable to communicate with the configured database'


    # Example to check for current version of PostgreSQL. Metric returns the version that the exporter is running on, so you can set a rule to check for the minimum version you'd like all systems to be on. Number returned is the 6 digit integer representation contained in the setting "server_version_num".
    #
    #  - alert: PGMinimumVersion
    #    expr:  ccp_postgresql_version_current < 110005
    #    for: 60s
    #    labels:
    #      service: postgresql
    #      severity: critical
    #      severity_num: 300
    #    annotations:
    #      summary: '{{ $labels.job }} is not running at least version 11.5 of PostgreSQL'


    # Whether a system switches from primary to replica or vice versa must be configured per named job.
    # No way to tell what value a system is supposed to be without a rule expression for that specific system
    # 2 to 1 means it changed from primary to replica. 1 to 2 means it changed from replica to primary
    # Set this alert for each system that you want to monitor a recovery status change
    # Below is an example for a target job called "Replica" and watches for the value to change above 1 which means it's no longer a replica
    #
    #  - alert: PGRecoveryStatusSwitch_Replica
    #    expr: ccp_is_in_recovery_status{job="Replica"} > 1
    #    for: 60s
    #    labels:
    #      service: postgresql
    #      severity: critical
    #      severity_num: 300
    #    annotations:
    #      summary: '{{ $labels.job }} has changed from replica to primary'


    # Absence alerts must be configured per named job, otherwise there's no way to know which job is down
    # Below is an example for a target job called "Prod"
    #  - alert: PGConnectionAbsent_Prod
    #    expr: absent(ccp_connection_stats_max_connections{job="Prod"})
    #    for: 10s
    #    labels:
    #      service: postgresql
    #      severity: critical
    #      severity_num: 300
    #    annotations:
    #      description: 'Connection metric is absent from target (Prod). Check that postgres_exporter can connect to PostgreSQL.'


    # Optional monitor for changes to pg_settings (postgresql.conf) system catalog.
    # A similar metric is available for monitoring pg_hba.conf. See ccp_hba_settings_checksum().
    # If metric returns 0, then NO settings have changed for either pg_settings since last known valid state
    # If metric returns 1, then pg_settings have changed since last known valid state
    # To see what may have changed, check the monitor.pg_settings_checksum table for a history of config state.
    #  - alert: PGSettingsChecksum
    #    expr: ccp_pg_settings_checksum > 0
    #    for 60s
    #    labels:
    #      service: postgresql
    #      severity: critical
    #      severity_num: 300
    #    annotations:
    #      description: 'Configuration settings on {{ $labels.job }} have changed from previously known valid state. To reset current config to a valid state after alert fires, run monitor.pg_settings_checksum_set_valid().'
    #      summary: 'PGSQL Instance settings checksum'


    # Monitor for data block checksum failures. Only works in PG12+
    #  - alert: PGDataChecksum
    #    expr: ccp_data_checksum_failure > 0
    #    for 60s
    #    labels:
    #      service: postgresql
    #      severity: critical
    #      severity_num: 300
    #    annotations:
    #      description: '{{ $labels.job }} has at least one data checksum failure in database {{ $labels.dbname }}. See pg_stat_database system catalog for more information.'
    #      summary: 'PGSQL Data Checksum failure'

      - alert: PGIdleTxn
        expr: ccp_connection_stats_max_idle_in_txn_time > 300
        for: 60s
        labels:
          service: postgresql
          severity: warning
          severity_num: 200
        annotations:
          description: '{{ $labels.job }} has at least one session idle in transaction for over 5 minutes.'
          summary: 'PGSQL Instance idle transactions'

      - alert: PGIdleTxn
        expr: ccp_connection_stats_max_idle_in_txn_time > 900
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: '{{ $labels.job }} has at least one session idle in transaction for over 15 minutes.'
          summary: 'PGSQL Instance idle transactions'

      - alert: PGQueryTime
        expr: ccp_connection_stats_max_query_time > 43200
        for: 60s
        labels:
          service: postgresql
          severity: warning
          severity_num: 200
        annotations:
          description: '{{ $labels.job }} has at least one query running for over 12 hours.'
          summary: 'PGSQL Max Query Runtime'

      - alert: PGQueryTime
        expr: ccp_connection_stats_max_query_time > 86400
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: '{{ $labels.job }} has at least one query running for over 1 day.'
          summary: 'PGSQL Max Query Runtime'

      - alert: PGConnPerc
        expr: 100 * (ccp_connection_stats_total / ccp_connection_stats_max_connections) > 75
        for: 60s
        labels:
          service: postgresql
          severity: warning
          severity_num: 200
        annotations:
          description: '{{ $labels.job }} is using 75% or more of available connections ({{ $value }}%)'
          summary: 'PGSQL Instance connections'

      - alert: PGConnPerc
        expr: 100 * (ccp_connection_stats_total / ccp_connection_stats_max_connections) > 90
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: '{{ $labels.job }} is using 90% or more of available connections ({{ $value }}%)'
          summary: 'PGSQL Instance connections'

      - alert: PGDiskSize
        expr: 100 * ((ccp_nodemx_data_disk_total_bytes - ccp_nodemx_data_disk_available_bytes) / ccp_nodemx_data_disk_total_bytes) > 75
        for: 60s
        labels:
          service: postgresql
          severity: warning
          severity_num: 200
        annotations:
          description: 'PGSQL Instance {{ $labels.deployment }} over 75% disk usage at mount point "{{ $labels.mount_point }}": {{ $value }}%'
          summary: PGSQL Instance usage warning

      - alert: PGDiskSize
        expr: 100 * ((ccp_nodemx_data_disk_total_bytes - ccp_nodemx_data_disk_available_bytes) / ccp_nodemx_data_disk_total_bytes) > 90
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: 'PGSQL Instance {{ $labels.deployment }} over 90% disk usage at mount point "{{ $labels.mount_point }}": {{ $value }}%'
          summary: 'PGSQL Instance size critical'

      - alert: PGReplicationByteLag
        expr: ccp_replication_status_byte_lag > 5.24288e+07
        for: 60s
        labels:
          service: postgresql
          severity: warning
          severity_num: 200
        annotations:
          description: 'PGSQL Instance {{ $labels.job }} has at least one replica lagging over 50MB behind.'
          summary: 'PGSQL Instance replica lag warning'

      - alert: PGReplicationByteLag
        expr: ccp_replication_status_byte_lag > 1.048576e+08
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: 'PGSQL Instance {{ $labels.job }} has at least one replica lagging over 100MB behind.'
          summary: 'PGSQL Instance replica lag warning'

      - alert: PGReplicationSlotsInactive
        expr: ccp_replication_slots_active == 0
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: 'PGSQL Instance {{ $labels.job }} has one or more inactive replication slots'
          summary: 'PGSQL Instance inactive replication slot'

      - alert: PGXIDWraparound
        expr: ccp_transaction_wraparound_percent_towards_wraparound > 50
        for: 60s
        labels:
          service: postgresql
          severity: warning
          severity_num: 200
        annotations:
          description: 'PGSQL Instance {{ $labels.job }} is over 50% towards transaction id wraparound.'
          summary: 'PGSQL Instance {{ $labels.job }} transaction id wraparound imminent'

      - alert: PGXIDWraparound
        expr: ccp_transaction_wraparound_percent_towards_wraparound > 75
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: 'PGSQL Instance {{ $labels.job }} is over 75% towards transaction id wraparound.'
          summary: 'PGSQL Instance transaction id wraparound imminent'

      - alert: PGEmergencyVacuum
        expr: ccp_transaction_wraparound_percent_towards_emergency_autovac > 110
        for: 60s
        labels:
          service: postgresql
          severity: warning
          severity_num: 200
        annotations:
          description: 'PGSQL Instance {{ $labels.job }} is over 110% beyond autovacuum_freeze_max_age value. Autovacuum may need tuning to better keep up.'
          summary: 'PGSQL Instance emergency vacuum imminent'

      - alert: PGEmergencyVacuum
        expr: ccp_transaction_wraparound_percent_towards_emergency_autovac > 125
        for: 60s
        labels:
          service: postgresql
          severity: critical
          severity_num: 300
        annotations:
          description: 'PGSQL Instance {{ $labels.job }} is over 125% beyond autovacuum_freeze_max_age value. Autovacuum needs tuning to better keep up.'
          summary: 'PGSQL Instance emergency vacuum imminent'

      - alert: PGArchiveCommandStatus
        expr: ccp_archive_command_status_seconds_since_last_fail > 300
        for: 60s
        labels:
            service: postgresql
            severity: critical
            severity_num: 300
        annotations:
            description: 'PGSQL Instance {{ $labels.job }} has a recent failing archive command'
            summary: 'Seconds since the last recorded failure of the archive_command'

      - alert: PGSequenceExhaustion
        expr: ccp_sequence_exhaustion_count > 0
        for: 60s
        labels:
            service: postgresql
            severity: critical
            severity_num: 300
        annotations:
            description: 'Count of sequences on instance {{ $labels.job }} at over 75% usage: {{ $value }}. Run following query to see full sequence status: SELECT * FROM monitor.sequence_status() WHERE percent >= 75'

      - alert: PGSettingsPendingRestart
        expr: ccp_settings_pending_restart_count > 0
        for: 60s
        labels:
            service: postgresql
            severity: critical
            severity_num: 300
        annotations:
            description: 'One or more settings in the pg_settings system catalog on system {{ $labels.job }} are in a pending_restart state. Check the system catalog for which settings are pending and review postgresql.conf for changes.'

    ########## PGBACKREST RULES ##########
    #
    # Uncomment and customize one or more of these rules to monitor your pgbackrest backups.
    # Full backups are considered the equivalent of both differentials and incrementals since both are based on the last full
    #   And differentials are considered incrementals since incrementals will be based off the last diff if one exists
    #   This avoid false alerts, for example when you don't run diff/incr backups on the days that you run a full
    # Stanza should also be set if different intervals are expected for each stanza.
    #   Otherwise rule will be applied to all stanzas returned on target system if not set.
    #
    # Relevant metric names are:
    #   ccp_backrest_last_full_time_since_completion_seconds
    #   ccp_backrest_last_incr_time_since_completion_seconds
    #   ccp_backrest_last_diff_time_since_completion_seconds
    #
    #  - alert: PGBackRestLastCompletedFull_main
    #    expr: ccp_backrest_last_full_backup_time_since_completion_seconds{stanza="main"} > 604800
    #    for: 60s
    #    labels:
    #       service: postgresql
    #       severity: critical
    #       severity_num: 300
    #    annotations:
    #       summary: 'Full backup for stanza [main] on system {{ $labels.job }} has not completed in the last week.'
    #
    #  - alert: PGBackRestLastCompletedIncr_main
    #    expr: ccp_backrest_last_incr_backup_time_since_completion_seconds{stanza="main"} > 86400
    #    for: 60s
    #    labels:
    #       service: postgresql
    #       severity: critical
    #       severity_num: 300
    #    annotations:
    #       summary: 'Incremental backup for stanza [main] on system {{ $labels.job }} has not completed in the last 24 hours.'
    #
    #
    # Runtime monitoring is handled with a single metric:
    #
    #   ccp_backrest_last_runtime_backup_runtime_seconds
    #
    # Runtime monitoring should have the "backup_type" label set.
    #   Otherwise the rule will apply to the last run of all backup types returned (full, diff, incr)
    # Stanza should also be set if runtimes per stanza have different expected times
    #
    #  - alert: PGBackRestLastRuntimeFull_main
    #    expr: ccp_backrest_last_runtime_backup_runtime_seconds{backup_type="full", stanza="main"} > 14400
    #    for: 60s
    #    labels:
    #       service: postgresql
    #       severity: critical
    #       severity_num: 300
    #    annotations:
    #       summary: 'Expected runtime of full backup for stanza [main] has exceeded 4 hours'
    #
    #  - alert: PGBackRestLastRuntimeDiff_main
    #    expr: ccp_backrest_last_runtime_backup_runtime_seconds{backup_type="diff", stanza="main"} > 3600
    #    for: 60s
    #    labels:
    #       service: postgresql
    #       severity: critical
    #       severity_num: 300
    #    annotations:
    #       summary: 'Expected runtime of diff backup for stanza [main] has exceeded 1 hour'
    ##
    #
    ## If the pgbackrest command fails to run, the metric disappears from the exporter output and the alert never fires.
    ## An absence alert must be configured explicitly for each target (job) that backups are being monitored.
    ## Checking for absence of just the full backup type should be sufficient (no need for diff/incr).
    ## Note that while the backrest check command failing will likely also cause a scrape error alert, the addition of this
    ## check gives a clearer answer as to what is causing it and that something is wrong with the backups.
    #
    #  - alert: PGBackrestAbsentFull_Prod
    #    expr: absent(ccp_backrest_last_full_backup_time_since_completion_seconds{job="Prod"})
    #    for: 10s
    #    labels:
    #      service: postgresql
    #      severity: critical
    #      severity_num: 300
    #    annotations:
    #      description: 'Backup Full status missing for Prod. Check that pgbackrest info command is working on target system.'
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    vendor: crunchydata
  name: alertmanager-rules-config
  namespace: a0ec71-tools
---
apiVersion: v1
data:
  prometheus.yml: |+
    ###
    #
    # Copyright 2017-2022 Crunchy Data Solutions, Inc. All Rights Reserved.
    #
    ###

    ---
    global:
      scrape_interval: 15s
      scrape_timeout: 15s
      evaluation_interval: 5s

    scrape_configs:
    - job_name: 'crunchy-postgres-exporter'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
            - a0ec71-tools
            - a0ec71-dev
            - a0ec71-test
            - a0ec71-prod      

      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_postgres_operator_crunchydata_com_crunchy_postgres_exporter,__meta_kubernetes_pod_label_crunchy_postgres_exporter]
        action: keep
        regex: true
        separator: ""
      - source_labels: [__meta_kubernetes_pod_container_port_number]
        action: drop
        regex: 5432
      - source_labels: [__meta_kubernetes_pod_container_port_number]
        action: drop
        regex: 10000
      - source_labels: [__meta_kubernetes_pod_container_port_number]
        action: drop
        regex: 8009
      - source_labels: [__meta_kubernetes_pod_container_port_number]
        action: drop
        regex: 2022
      - source_labels: [__meta_kubernetes_pod_container_port_number]
        action: drop
        regex: ^$
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_pod_label_postgres_operator_crunchydata_com_cluster,__meta_kubernetes_pod_label_pg_cluster]
        target_label: cluster
        separator: ""
        replacement: '$1'
      - source_labels: [__meta_kubernetes_namespace,cluster]
        target_label: pg_cluster
        separator: ":"
        replacement: '$1$2'
      - source_labels: [__meta_kubernetes_pod_ip]
        target_label: ip
        replacement: '$1'
      - source_labels: [__meta_kubernetes_pod_label_postgres_operator_crunchydata_com_instance,__meta_kubernetes_pod_label_deployment_name]
        target_label: deployment
        replacement: '$1'
        separator: ""
      - source_labels: [__meta_kubernetes_pod_label_postgres_operator_crunchydata_com_role,__meta_kubernetes_pod_label_role]
        target_label: role
        replacement: '$1'
        separator: ""
      - source_labels: [dbname]
        target_label: dbname
        replacement: '$1'
      - source_labels: [relname]
        target_label: relname
        replacement: '$1'
      - source_labels: [schemaname]
        target_label: schemaname
        replacement: '$1'

    rule_files:
      - /etc/prometheus/alert-rules.d/*.yml
    alerting:
      alertmanagers:
      - scheme: http
        static_configs:
        - targets:
          - "crunchy-alertmanager:9093"

kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    vendor: crunchydata
  name: crunchy-prometheus
  namespace: a0ec71-tools
---
apiVersion: v1
data:
  grafana.ini: |
    [auth]
    disable_login_form = true
    disable_signout_menu = true
    oauth_auto_login = true

    [auth.anonymous]
    enabled = false

    [auth.basic]
    enabled = true

    [auth.proxy]
    auto_sign_up = true
    enabled = true
    header_name = X-Forwarded-User
    header_property = username

    [log]
    level = warn
    mode = console

    [paths]
    data = /var/lib/grafana
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning/
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: a0ec71-tools
---
apiVersion: v1
data:
  crunchy_grafana_dashboards.yml: "###\r\n#\r\n# Copyright 2017-2022 Crunchy Data
    Solutions, Inc. All Rights Reserved.\r\n#\r\n###\r\napiVersion: 1\r\n\r\nproviders:\r\n-
    name: 'crunchy_dashboards'\r\n  orgId: 1\r\n  folder: ''\r\n  type: file\r\n  disableDeletion:
    false\r\n  updateIntervalSeconds: 3 #how often Grafana will scan for changed dashboards\r\n
    \ options:\r\n    path: $GF_PATHS_PROVISIONING/dashboards\r\n"
  pgbackrest.json: "{\r\n  \"__inputs\": [\r\n    {\r\n      \"name\": \"DS_PROMETHEUS\",\r\n
    \     \"label\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"type\":
    \"datasource\",\r\n      \"pluginId\": \"prometheus\",\r\n      \"pluginName\":
    \"Prometheus\"\r\n    }\r\n  ],\r\n  \"__requires\": [\r\n    {\r\n      \"type\":
    \"grafana\",\r\n      \"id\": \"grafana\",\r\n      \"name\": \"Grafana\",\r\n
    \     \"version\": \"7.4.5\"\r\n    },\r\n    {\r\n      \"type\": \"panel\",\r\n
    \     \"id\": \"graph\",\r\n      \"name\": \"Graph\",\r\n      \"version\": \"\"\r\n
    \   },\r\n    {\r\n      \"type\": \"datasource\",\r\n      \"id\": \"prometheus\",\r\n
    \     \"name\": \"Prometheus\",\r\n      \"version\": \"1.0.0\"\r\n    },\r\n
    \   {\r\n      \"type\": \"panel\",\r\n      \"id\": \"stat\",\r\n      \"name\":
    \"Stat\",\r\n      \"version\": \"\"\r\n    }\r\n  ],\r\n  \"annotations\": {\r\n
    \   \"list\": [\r\n      {\r\n        \"builtIn\": 1,\r\n        \"datasource\":
    \"-- Grafana --\",\r\n        \"enable\": true,\r\n        \"hide\": true,\r\n
    \       \"iconColor\": \"rgba(0, 211, 255, 1)\",\r\n        \"name\": \"Annotations
    & Alerts\",\r\n        \"type\": \"dashboard\"\r\n      }\r\n    ]\r\n  },\r\n
    \ \"editable\": false,\r\n  \"gnetId\": null,\r\n  \"graphTooltip\": 0,\r\n  \"id\":
    null,\r\n  \"iteration\": 1624546649377,\r\n  \"links\": [\r\n    {\r\n      \"asDropdown\":
    false,\r\n      \"icon\": \"external link\",\r\n      \"includeVars\": true,\r\n
    \     \"keepTime\": true,\r\n      \"tags\": [\r\n        \"vendor=crunchydata\"\r\n
    \     ],\r\n      \"title\": \"\",\r\n      \"type\": \"dashboards\"\r\n    }\r\n
    \ ],\r\n  \"panels\": [\r\n    {\r\n      \"datasource\": \"PROMETHEUS\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\": {\r\n
    \           \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"mappings\": [],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"semi-dark-blue\",\r\n                \"value\": null\r\n              }\r\n
    \           ]\r\n          },\r\n          \"unit\": \"dtdhms\"\r\n        },\r\n
    \       \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    3,\r\n        \"w\": 24,\r\n        \"x\": 0,\r\n        \"y\": 0\r\n      },\r\n
    \     \"id\": 8,\r\n      \"options\": {\r\n        \"colorMode\": \"background\",\r\n
    \       \"graphMode\": \"area\",\r\n        \"justifyMode\": \"auto\",\r\n        \"orientation\":
    \"auto\",\r\n        \"reduceOptions\": {\r\n          \"calcs\": [\r\n            \"last\"\r\n
    \         ],\r\n          \"fields\": \"/^Value$/\",\r\n          \"values\":
    false\r\n        },\r\n        \"text\": {\r\n          \"valueSize\": 45\r\n
    \       },\r\n        \"textMode\": \"auto\"\r\n      },\r\n      \"pluginVersion\":
    \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"time()-ccp_backrest_oldest_full_backup_time_seconds{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\"}\",\r\n          \"format\": \"table\",\r\n          \"instant\":
    true,\r\n          \"interval\": \"\",\r\n          \"legendFormat\": \"Recovery
    window\",\r\n          \"refId\": \"A\"\r\n        }\r\n      ],\r\n      \"title\":
    \"Recovery Window\",\r\n      \"type\": \"stat\"\r\n    },\r\n    {\r\n      \"aliasColors\":
    {\r\n        \"Differential\": \"dark-blue\",\r\n        \"Differential Backup\":
    \"dark-blue\",\r\n        \"Full\": \"dark-green\",\r\n        \"Full Backup\":
    \"dark-green\",\r\n        \"Incremental\": \"light-blue\",\r\n        \"Incremental
    Backup\": \"light-blue\"\r\n      },\r\n      \"bars\": false,\r\n      \"dashLength\":
    10,\r\n      \"dashes\": false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\":
    {\r\n        \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\":
    []\r\n        },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 7,\r\n        \"w\":
    12,\r\n        \"x\": 0,\r\n        \"y\": 3\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 2,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    false\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\":
    \"7.4.5\",\r\n      \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\":
    \"flot\",\r\n      \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n
    \     \"stack\": false,\r\n      \"steppedLine\": false,\r\n      \"targets\":
    [\r\n        {\r\n          \"expr\": \"min(ccp_backrest_last_incr_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\"}) without(deployment,instance,ip,pod)\",\r\n          \"format\":
    \"time_series\",\r\n          \"instant\": false,\r\n          \"interval\": \"\",\r\n
    \         \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Incremental
    Backup\",\r\n          \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\":
    \"min(ccp_backrest_last_diff_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\"}) without(deployment, instance,ip,pod)\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\": \"Differential
    Backup\",\r\n          \"refId\": \"B\"\r\n        },\r\n        {\r\n          \"expr\":
    \"min(ccp_backrest_last_full_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\"}) without(deployment, instance,ip,pod)\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\": \"Full
    Backup\",\r\n          \"refId\": \"C\"\r\n        },\r\n        {\r\n          \"expr\":
    \"min(ccp_archive_command_status_seconds_since_last_archive{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\"}) without(deployment, instance,ip,pod)\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\": \"WAL Archive\",\r\n
    \         \"refId\": \"D\"\r\n        }\r\n      ],\r\n      \"thresholds\": [],\r\n
    \     \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Time Since\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"s\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {\r\n        \"Differential\": \"dark-blue\",\r\n        \"Full\": \"dark-green\",\r\n
    \       \"Incremental\": \"light-blue\"\r\n      },\r\n      \"bars\": false,\r\n
    \     \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 7,\r\n        \"w\": 12,\r\n        \"x\": 12,\r\n        \"y\":
    3\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 4,\r\n      \"legend\":
    {\r\n        \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"hideEmpty\": false,\r\n        \"hideZero\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"min(ccp_backrest_last_info_backup_runtime_seconds{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\", backup_type=\\\"incr\\\"}) without (deployment,instance,pod,ip)\",\r\n
    \         \"format\": \"time_series\",\r\n          \"instant\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Incremental\",\r\n
    \         \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\": \"min(ccp_backrest_last_info_backup_runtime_seconds{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\", backup_type=\\\"diff\\\"}) without (deployment,instance,pod,ip)\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\":
    \"Differential\",\r\n          \"refId\": \"B\"\r\n        },\r\n        {\r\n
    \         \"expr\": \"min(ccp_backrest_last_info_backup_runtime_seconds{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\", backup_type=\\\"full\\\"}) without (deployment,instance,pod,ip)\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\":
    \"Full\",\r\n          \"refId\": \"C\"\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Backup Runtimes\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"s\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 2,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {\r\n        \"Differential\": \"dark-blue\",\r\n        \"Full\": \"dark-green\",\r\n
    \       \"Incremental\": \"light-blue\"\r\n      },\r\n      \"bars\": false,\r\n
    \     \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 7,\r\n        \"w\":
    12,\r\n        \"x\": 0,\r\n        \"y\": 10\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 5,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"hideEmpty\":
    false,\r\n        \"hideZero\": false,\r\n        \"max\": false,\r\n        \"min\":
    false,\r\n        \"rightSide\": true,\r\n        \"show\": true,\r\n        \"sideWidth\":
    150,\r\n        \"total\": false,\r\n        \"values\": false\r\n      },\r\n
    \     \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\": [],\r\n
    \     \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"min(ccp_backrest_last_info_repo_backup_size_bytes{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\", backup_type=\\\"incr\\\"}) without (deployment, instance,pod,ip)\",\r\n
    \         \"format\": \"time_series\",\r\n          \"instant\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Incremental\",\r\n
    \         \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\": \"min(ccp_backrest_last_info_repo_backup_size_bytes{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\", backup_type=\\\"diff\\\"}) without (deployment,instance,pod,ip)\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\":
    \"Differential\",\r\n          \"refId\": \"B\"\r\n        },\r\n        {\r\n
    \         \"expr\": \"min(ccp_backrest_last_info_repo_backup_size_bytes{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\", backup_type=\\\"full\\\"}) without (deployment,instance,pod,ip)\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\":
    \"Full\",\r\n          \"refId\": \"C\"\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Backup Size\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"bytes\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 2,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {\r\n        \"Archive age\": \"blue\",\r\n        \"Archive count\": \"green\",\r\n
    \       \"Differential\": \"dark-blue\",\r\n        \"Failed count\": \"red\",\r\n
    \       \"Full\": \"dark-green\",\r\n        \"Incremental\": \"light-blue\"\r\n
    \     },\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\": {},\r\n
    \         \"links\": []\r\n        },\r\n        \"overrides\": []\r\n      },\r\n
    \     \"fill\": 3,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\":
    7,\r\n        \"w\": 12,\r\n        \"x\": 12,\r\n        \"y\": 10\r\n      },\r\n
    \     \"hiddenSeries\": false,\r\n      \"id\": 6,\r\n      \"legend\": {\r\n
    \       \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"hideEmpty\": false,\r\n        \"hideZero\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"avg(idelta(ccp_archive_command_status_failed_count{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\"}[1m])) without (instance,ip)\",\r\n          \"format\": \"time_series\",\r\n
    \         \"instant\": false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"Failed count\",\r\n          \"refId\": \"A\"\r\n
    \       },\r\n        {\r\n          \"expr\": \"avg(idelta(ccp_archive_command_status_archived_count{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"master\\\"}[1m])) without (instance,pod, ip)\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\": \"Archive
    count\",\r\n          \"refId\": \"B\"\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"WAL Stats\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"short\",\r\n          \"label\": \"\",\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": \"0\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": \"0\",\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    }\r\n  ],\r\n  \"refresh\":
    \"5m\",\r\n  \"schemaVersion\": 27,\r\n  \"style\": \"dark\",\r\n  \"tags\": [\r\n
    \   \"vendor=crunchydata\"\r\n  ],\r\n  \"templating\": {\r\n    \"list\": [\r\n
    \     {\r\n        \"allValue\": null,\r\n        \"current\": {},\r\n        \"datasource\":
    \"PROMETHEUS\",\r\n        \"definition\": \"label_values(pg_cluster)\",\r\n        \"description\":
    null,\r\n        \"error\": null,\r\n        \"hide\": 0,\r\n        \"includeAll\":
    false,\r\n        \"label\": \"cluster\",\r\n        \"multi\": false,\r\n        \"name\":
    \"cluster\",\r\n        \"options\": [],\r\n        \"query\": {\r\n          \"query\":
    \"label_values(pg_cluster)\",\r\n          \"refId\": \"PROMETHEUS-cluster-Variable-Query\"\r\n
    \       },\r\n        \"refresh\": 1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\":
    false,\r\n        \"sort\": 1,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\":
    [],\r\n        \"tagsQuery\": \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\":
    false\r\n      }\r\n    ]\r\n  },\r\n  \"time\": {\r\n    \"from\": \"now-30m\",\r\n
    \   \"to\": \"now\"\r\n  },\r\n  \"timepicker\": {\r\n    \"time_options\": [\r\n
    \     \"5m\",\r\n      \"15m\",\r\n      \"1h\",\r\n      \"6h\",\r\n      \"12h\",\r\n
    \     \"24h\",\r\n      \"2d\",\r\n      \"7d\",\r\n      \"30d\"\r\n    ]\r\n
    \ },\r\n  \"timezone\": \"browser\",\r\n  \"title\": \"pgBackRest\",\r\n  \"uid\":
    \"2fcFZ6PGk\",\r\n  \"version\": 1\r\n}\r\n"
  pod_details.json: "{\r\n  \"__inputs\": [\r\n    {\r\n      \"name\": \"DS_PROMETHEUS\",\r\n
    \     \"label\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"type\":
    \"datasource\",\r\n      \"pluginId\": \"prometheus\",\r\n      \"pluginName\":
    \"Prometheus\"\r\n    }\r\n  ],\r\n  \"__requires\": [\r\n    {\r\n      \"type\":
    \"grafana\",\r\n      \"id\": \"grafana\",\r\n      \"name\": \"Grafana\",\r\n
    \     \"version\": \"7.4.5\"\r\n    },\r\n    {\r\n      \"type\": \"panel\",\r\n
    \     \"id\": \"graph\",\r\n      \"name\": \"Graph\",\r\n      \"version\": \"\"\r\n
    \   },\r\n    {\r\n      \"type\": \"datasource\",\r\n      \"id\": \"prometheus\",\r\n
    \     \"name\": \"Prometheus\",\r\n      \"version\": \"1.0.0\"\r\n    }\r\n  ],\r\n
    \ \"annotations\": {\r\n    \"list\": [\r\n      {\r\n        \"builtIn\": 1,\r\n
    \       \"datasource\": \"-- Grafana --\",\r\n        \"enable\": true,\r\n        \"hide\":
    true,\r\n        \"iconColor\": \"rgba(0, 211, 255, 1)\",\r\n        \"name\":
    \"Annotations & Alerts\",\r\n        \"type\": \"dashboard\"\r\n      }\r\n    ]\r\n
    \ },\r\n  \"editable\": true,\r\n  \"gnetId\": null,\r\n  \"graphTooltip\": 0,\r\n
    \ \"id\": null,\r\n  \"iteration\": 1624647381559,\r\n  \"links\": [\r\n    {\r\n
    \     \"icon\": \"external link\",\r\n      \"includeVars\": true,\r\n      \"keepTime\":
    true,\r\n      \"tags\": [\r\n        \"vendor=crunchydata\"\r\n      ],\r\n      \"type\":
    \"dashboards\"\r\n    }\r\n  ],\r\n  \"panels\": [\r\n    {\r\n      \"aliasColors\":
    {\r\n        \"% Throttled\": \"yellow\",\r\n        \"% Used\": \"blue\",\r\n
    \       \"Limit\": \"red\",\r\n        \"Process count\": \"blue\"\r\n      },\r\n
    \     \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\": false,\r\n
    \     \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\":
    {\r\n          \"color\": {},\r\n          \"custom\": {},\r\n          \"thresholds\":
    {\r\n            \"mode\": \"absolute\",\r\n            \"steps\": []\r\n          },\r\n
    \         \"unit\": \"short\"\r\n        },\r\n        \"overrides\": []\r\n      },\r\n
    \     \"fill\": 0,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\":
    6,\r\n        \"w\": 8,\r\n        \"x\": 0,\r\n        \"y\": 0\r\n      },\r\n
    \     \"hiddenSeries\": false,\r\n      \"id\": 11,\r\n      \"legend\": {\r\n
    \       \"avg\": false,\r\n        \"current\": false,\r\n        \"max\": false,\r\n
    \       \"min\": false,\r\n        \"show\": true,\r\n        \"total\": false,\r\n
    \       \"values\": false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\":
    1,\r\n      \"links\": [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\":
    {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\": false,\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n      \"points\":
    false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\": [\r\n        {\r\n
    \         \"alias\": \"Limit\",\r\n          \"dashes\": true\r\n        }\r\n
    \     ],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"avg((idelta(ccp_nodemx_cpuacct_usage{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}[30s])/1000000000)/idelta(ccp_nodemx_cpuacct_usage_ts{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}[30s])*100)
    without(instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"% Usage\",\r\n          \"refId\": \"A\"\r\n        },\r\n
    \       {\r\n          \"expr\": \"avg((ccp_nodemx_cpucfs_quota_us{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}*100/ccp_nodemx_cpucfs_period_us{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}))
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"Limit\",\r\n          \"refId\": \"D\"\r\n        }\r\n
    \     ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"CPU usage\",\r\n      \"tooltip\":
    {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n        \"value_type\":
    \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n      \"xaxis\": {\r\n
    \       \"buckets\": null,\r\n        \"mode\": \"time\",\r\n        \"name\":
    null,\r\n        \"show\": true,\r\n        \"values\": []\r\n      },\r\n      \"yaxes\":
    [\r\n        {\r\n          \"decimals\": null,\r\n          \"format\": \"short\",\r\n
    \         \"label\": \"Percent\",\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": \"0\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": \"Process
    count\",\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\":
    \"0\",\r\n          \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\":
    {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n      }\r\n
    \   },\r\n    {\r\n      \"aliasColors\": {\r\n        \"% Throttled\": \"yellow\",\r\n
    \       \"% Used\": \"blue\",\r\n        \"Limit\": \"red\",\r\n        \"Process
    count\": \"blue\"\r\n      },\r\n      \"bars\": false,\r\n      \"dashLength\":
    10,\r\n      \"dashes\": false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\":
    {\r\n        \"defaults\": {\r\n          \"color\": {},\r\n          \"custom\":
    {},\r\n          \"thresholds\": {\r\n            \"mode\": \"absolute\",\r\n
    \           \"steps\": []\r\n          },\r\n          \"unit\": \"short\"\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 0,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    8,\r\n        \"x\": 8,\r\n        \"y\": 0\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 15,\r\n      \"legend\": {\r\n        \"avg\": false,\r\n
    \       \"current\": false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n
    \       \"show\": true,\r\n        \"total\": false,\r\n        \"values\": false\r\n
    \     },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [\r\n        {\r\n          \"alias\": \"Limit\",\r\n
    \         \"dashes\": true\r\n        },\r\n        {\r\n          \"alias\":
    \"Process count\",\r\n          \"yaxis\": 2\r\n        }\r\n      ],\r\n      \"spaceLength\":
    10,\r\n      \"stack\": false,\r\n      \"steppedLine\": false,\r\n      \"targets\":
    [\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_process_count{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"Process count\",\r\n          \"refId\": \"B\"\r\n
    \       },\r\n        {\r\n          \"expr\": \"avg((idelta(ccp_nodemx_cpustat_throttled_time{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}[30s])/1000000000)/idelta(ccp_nodemx_cpustat_snap_ts{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}[30s])*100)
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"% Throttled\",\r\n          \"refId\": \"C\"\r\n
    \       }\r\n      ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n
    \     \"timeRegions\": [],\r\n      \"timeShift\": null,\r\n      \"title\": \"CPU
    Throttle\",\r\n      \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\":
    0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"decimals\": null,\r\n
    \         \"format\": \"short\",\r\n          \"label\": \"\",\r\n          \"logBase\":
    1,\r\n          \"max\": null,\r\n          \"min\": \"0\",\r\n          \"show\":
    true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\":
    \"Process count\",\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n
    \         \"min\": \"0\",\r\n          \"show\": true\r\n        }\r\n      ],\r\n
    \     \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n
    \     }\r\n    },\r\n    {\r\n      \"aliasColors\": {\r\n        \"Inactive anon\":
    \"super-light-purple\",\r\n        \"Limit\": \"red\",\r\n        \"Mem free\":
    \"green\",\r\n        \"Request\": \"blue\"\r\n      },\r\n      \"bars\": false,\r\n
    \     \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 0,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 6,\r\n        \"w\": 8,\r\n        \"x\": 16,\r\n        \"y\":
    0\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 6,\r\n      \"legend\":
    {\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"show\": true,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"links\": [],\r\n      \"nullPointMode\": \"null\",\r\n
    \     \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\":
    false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n
    \     \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [\r\n        {\r\n          \"alias\": \"Limit\",\r\n          \"dashes\": true\r\n
    \       },\r\n        {\r\n          \"alias\": \"Request\",\r\n          \"dashes\":
    true\r\n        }\r\n      ],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"avg(ccp_nodemx_mem_limit{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Limit\",\r\n
    \         \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_mem_request{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Request\",\r\n
    \         \"refId\": \"B\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_mem_usage_in_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Usage\",\r\n
    \         \"refId\": \"J\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(clamp_min((ccp_nodemx_mem_limit{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}
    - ccp_nodemx_mem_usage_in_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}),0))
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"Free\",\r\n          \"refId\": \"D\"\r\n        }\r\n
    \     ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Memory\",\r\n      \"tooltip\":
    {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n        \"value_type\":
    \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n      \"xaxis\": {\r\n
    \       \"buckets\": null,\r\n        \"mode\": \"time\",\r\n        \"name\":
    null,\r\n        \"show\": true,\r\n        \"values\": []\r\n      },\r\n      \"yaxes\":
    [\r\n        {\r\n          \"format\": \"bytes\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": false\r\n        }\r\n
    \     ],\r\n      \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\":
    null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\":
    false,\r\n      \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 6,\r\n        \"w\": 8,\r\n        \"x\": 0,\r\n        \"y\":
    6\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 8,\r\n      \"legend\":
    {\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"show\": true,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"links\": [],\r\n      \"nullPointMode\": \"null\",\r\n
    \     \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\":
    false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n
    \     \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [\r\n        {\r\n          \"alias\": \"/tx bytes/\",\r\n          \"transform\":
    \"negative-Y\"\r\n        }\r\n      ],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"avg(rate(ccp_nodemx_network_rx_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\",
    interface!=\\\"tunl0\\\"}[1m])) without(instance,ip,role)\",\r\n          \"format\":
    \"time_series\",\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"{{interface}} - rx bytes\",\r\n          \"refId\":
    \"A\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(rate(ccp_nodemx_network_tx_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\",
    interface!=\\\"tunl0\\\"}[1m])) without(instance,ip,role)\",\r\n          \"format\":
    \"time_series\",\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"{{interface}} - tx bytes\",\r\n          \"refId\":
    \"B\"\r\n        }\r\n      ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\":
    null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\": null,\r\n      \"title\":
    \"Network\",\r\n      \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\":
    0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\": \"Bps\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    8,\r\n        \"x\": 8,\r\n        \"y\": 6\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 10,\r\n      \"legend\": {\r\n        \"avg\": false,\r\n
    \       \"current\": false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n
    \       \"show\": true,\r\n        \"total\": false,\r\n        \"values\": false\r\n
    \     },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"avg((ccp_nodemx_data_disk_total_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"}-ccp_nodemx_data_disk_available_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})*100/ccp_nodemx_data_disk_total_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"{{mount_point}}\",\r\n
    \         \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\": \"avg((ccp_nodemx_data_disk_total_file_nodes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"}-ccp_nodemx_data_disk_free_file_nodes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})*100/ccp_nodemx_data_disk_total_file_nodes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})
    without(instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"{{mount_point}}
    - Inodes\",\r\n          \"refId\": \"B\"\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Disk Usage\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"percent\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    \"100\",\r\n          \"min\": \"0\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": true\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    8,\r\n        \"x\": 16,\r\n        \"y\": 6\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 12,\r\n      \"legend\": {\r\n        \"avg\": false,\r\n
    \       \"current\": false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n
    \       \"show\": true,\r\n        \"total\": false,\r\n        \"values\": false\r\n
    \     },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [\r\n        {\r\n          \"alias\": \"/Reads/\",\r\n
    \         \"transform\": \"negative-Y\"\r\n        }\r\n      ],\r\n      \"spaceLength\":
    10,\r\n      \"stack\": false,\r\n      \"steppedLine\": false,\r\n      \"targets\":
    [\r\n        {\r\n          \"expr\": \"avg(rate(ccp_nodemx_disk_activity_sectors_read{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}[1m])*512)
    without(instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"{{mount_point}}
    - Reads\",\r\n          \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(rate(ccp_nodemx_disk_activity_sectors_written{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"}[1m])*512)
    without(instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"{{mount_point}}
    - Writes \",\r\n          \"refId\": \"B\"\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Disk Activity\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"Bps\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {\r\n        \"Inactive anon\": \"super-light-purple\",\r\n        \"Limit\":
    \"red\",\r\n        \"Request\": \"green\"\r\n      },\r\n      \"bars\": false,\r\n
    \     \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 6,\r\n        \"w\": 12,\r\n        \"x\": 0,\r\n        \"y\":
    12\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 14,\r\n      \"legend\":
    {\r\n        \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n        \"rightSide\":
    true,\r\n        \"show\": true,\r\n        \"sideWidth\": 150,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"links\": [],\r\n      \"nullPointMode\": \"null\",\r\n
    \     \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\":
    false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n
    \     \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_mem_cache{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Cached\",\r\n
    \         \"refId\": \"C\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_mem_dirty{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Dirty\",\r\n
    \         \"refId\": \"D\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_mem_shmem{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"shared
    mem\",\r\n          \"refId\": \"E\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(ccp_nodemx_mem_rss{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"RSS\",\r\n
    \         \"refId\": \"F\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_mem_mapped_file{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Mapped
    file\",\r\n          \"refId\": \"G\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(ccp_nodemx_mem_kmem_usage_in_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"kmem\",\r\n
    \         \"refId\": \"H\"\r\n        },\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_mem_inactive_anon{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Inactive
    anon\",\r\n          \"refId\": \"I\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(ccp_nodemx_mem_active_file{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Active
    file\",\r\n          \"refId\": \"J\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(ccp_nodemx_mem_inactive_file{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Inactive
    file\",\r\n          \"refId\": \"K\"\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Memory Breakdown\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"bytes\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {\r\n        \"CPU limit\": \"red\",\r\n        \"CPU request\": \"blue\",\r\n
    \       \"Memory limit\": \"dark-red\",\r\n        \"Memory request\": \"dark-green\"\r\n
    \     },\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 0,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 12,\r\n        \"y\": 12\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 13,\r\n      \"legend\": {\r\n        \"avg\": false,\r\n
    \       \"current\": false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n
    \       \"show\": true,\r\n        \"total\": false,\r\n        \"values\": false\r\n
    \     },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [\r\n        {\r\n          \"alias\": \"CPU request\",\r\n
    \         \"yaxis\": 2\r\n        },\r\n        {\r\n          \"alias\": \"CPU
    limit\",\r\n          \"yaxis\": 2\r\n        }\r\n      ],\r\n      \"spaceLength\":
    10,\r\n      \"stack\": false,\r\n      \"steppedLine\": false,\r\n      \"targets\":
    [\r\n        {\r\n          \"expr\": \"avg(ccp_nodemx_cpu_limit{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"CPU
    limit\",\r\n          \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(ccp_nodemx_cpu_request{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"CPU
    request\",\r\n          \"refId\": \"B\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(ccp_nodemx_mem_limit{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Memory
    limit\",\r\n          \"refId\": \"C\"\r\n        },\r\n        {\r\n          \"expr\":
    \"avg(ccp_nodemx_mem_request{pg_cluster=\\\"[[cluster]]\\\",pod=\\\"[[pod]]\\\"})
    without (instance,ip,role)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Memory
    request\",\r\n          \"refId\": \"D\"\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Container resources\",\r\n      \"tooltip\": {\r\n
    \       \"shared\": true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n
    \     },\r\n      \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\":
    null,\r\n        \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\":
    true,\r\n        \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n
    \         \"format\": \"bytes\",\r\n          \"label\": \"Memory\",\r\n          \"logBase\":
    1,\r\n          \"max\": null,\r\n          \"min\": \"0\",\r\n          \"show\":
    true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\":
    \"CPU (millicores)\",\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n
    \         \"min\": \"0\",\r\n          \"show\": true\r\n        }\r\n      ],\r\n
    \     \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n
    \     }\r\n    }\r\n  ],\r\n  \"refresh\": \"5s\",\r\n  \"schemaVersion\": 27,\r\n
    \ \"style\": \"dark\",\r\n  \"tags\": [\r\n    \"vendor=crunchydata\"\r\n  ],\r\n
    \ \"templating\": {\r\n    \"list\": [\r\n      {\r\n        \"allValue\": null,\r\n
    \       \"current\": {},\r\n        \"datasource\": \"PROMETHEUS\",\r\n        \"definition\":
    \"label_values(pg_cluster)\",\r\n        \"description\": null,\r\n        \"error\":
    null,\r\n        \"hide\": 0,\r\n        \"includeAll\": false,\r\n        \"label\":
    \"cluster\",\r\n        \"multi\": false,\r\n        \"name\": \"cluster\",\r\n
    \       \"options\": [],\r\n        \"query\": {\r\n          \"query\": \"label_values(pg_cluster)\",\r\n
    \         \"refId\": \"PROMETHEUS-cluster-Variable-Query\"\r\n        },\r\n        \"refresh\":
    1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      },\r\n
    \     {\r\n        \"allValue\": null,\r\n        \"current\": {},\r\n        \"datasource\":
    \"PROMETHEUS\",\r\n        \"definition\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},pod)\",\r\n
    \       \"description\": null,\r\n        \"error\": null,\r\n        \"hide\":
    0,\r\n        \"includeAll\": false,\r\n        \"label\": \"pod\",\r\n        \"multi\":
    false,\r\n        \"name\": \"pod\",\r\n        \"options\": [],\r\n        \"query\":
    {\r\n          \"query\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},pod)\",\r\n
    \         \"refId\": \"PROMETHEUS-pod-Variable-Query\"\r\n        },\r\n        \"refresh\":
    1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      }\r\n
    \   ]\r\n  },\r\n  \"time\": {\r\n    \"from\": \"now-30m\",\r\n    \"to\": \"now\"\r\n
    \ },\r\n  \"timepicker\": {\r\n    \"time_options\": [\r\n      \"5m\",\r\n      \"15m\",\r\n
    \     \"1h\",\r\n      \"6h\",\r\n      \"12h\",\r\n      \"24h\",\r\n      \"2d\",\r\n
    \     \"7d\",\r\n      \"30d\"\r\n    ]\r\n  },\r\n  \"timezone\": \"browser\",\r\n
    \ \"title\": \"POD Details\",\r\n  \"uid\": \"4auP6Mk7k\",\r\n  \"version\": 1\r\n}\r\n"
  postgres_overview.json: "{\r\n  \"__inputs\": [\r\n    {\r\n      \"name\": \"DS_PROMETHEUS\",\r\n
    \     \"label\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"type\":
    \"datasource\",\r\n      \"pluginId\": \"prometheus\",\r\n      \"pluginName\":
    \"Prometheus\"\r\n    }\r\n  ],\r\n  \"__requires\": [\r\n    {\r\n      \"type\":
    \"grafana\",\r\n      \"id\": \"grafana\",\r\n      \"name\": \"Grafana\",\r\n
    \     \"version\": \"7.4.5\"\r\n    },\r\n    {\r\n      \"type\": \"datasource\",\r\n
    \     \"id\": \"prometheus\",\r\n      \"name\": \"Prometheus\",\r\n      \"version\":
    \"1.0.0\"\r\n    },\r\n    {\r\n      \"type\": \"panel\",\r\n      \"id\": \"stat\",\r\n
    \     \"name\": \"Stat\",\r\n      \"version\": \"\"\r\n    }\r\n  ],\r\n  \"annotations\":
    {\r\n    \"list\": [\r\n      {\r\n        \"builtIn\": 1,\r\n        \"datasource\":
    \"-- Grafana --\",\r\n        \"enable\": true,\r\n        \"hide\": true,\r\n
    \       \"iconColor\": \"rgba(0, 211, 255, 1)\",\r\n        \"name\": \"Annotations
    & Alerts\",\r\n        \"type\": \"dashboard\"\r\n      }\r\n    ]\r\n  },\r\n
    \ \"editable\": false,\r\n  \"gnetId\": null,\r\n  \"graphTooltip\": 0,\r\n  \"id\":
    null,\r\n  \"iteration\": 1624491413218,\r\n  \"links\": [],\r\n  \"panels\":
    [\r\n    {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\": \"PROMETHEUS\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\": {\r\n
    \           \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"links\": [\r\n            {\r\n              \"targetBlank\":
    true,\r\n              \"title\": \"Cluster Details\",\r\n              \"url\":
    \"dashboard/db/postgresqldetails?$__all_variables\"\r\n            },\r\n            {\r\n
    \             \"targetBlank\": true,\r\n              \"title\": \"Backup Details\",\r\n
    \             \"url\": \"dashboard/db/pgbackrest?$__all_variables\"\r\n            },\r\n
    \           {\r\n              \"targetBlank\": true,\r\n              \"title\":
    \"POD Details\",\r\n              \"url\": \"dashboard/db/pod-details?$__all_variables\"\r\n
    \           },\r\n            {\r\n              \"targetBlank\": true,\r\n              \"title\":
    \"Query Statistics\",\r\n              \"url\": \"dashboard/db/query-statistics?$__all_variables\"\r\n
    \           },\r\n            {\r\n              \"targetBlank\": true,\r\n              \"title\":
    \"Service Health\",\r\n              \"url\": \"dashboard/db/postgresql-service-health?$__all_variables\"\r\n
    \           }\r\n          ],\r\n          \"mappings\": [\r\n            {\r\n
    \             \"from\": \"0\",\r\n              \"id\": 0,\r\n              \"text\":
    \"DOWN\",\r\n              \"to\": \"99\",\r\n              \"type\": 2\r\n            },\r\n
    \           {\r\n              \"from\": \"100\",\r\n              \"id\": 1,\r\n
    \             \"text\": \"Standalone Cluster\",\r\n              \"to\": \"199\",\r\n
    \             \"type\": 2\r\n            },\r\n            {\r\n              \"from\":
    \"200\",\r\n              \"id\": 2,\r\n              \"text\": \"HA CLUSTER\",\r\n
    \             \"to\": \"1000\",\r\n              \"type\": 2\r\n            }\r\n
    \         ],\r\n          \"thresholds\": {\r\n            \"mode\": \"absolute\",\r\n
    \           \"steps\": [\r\n              {\r\n                \"color\": \"#bf1b00\",\r\n
    \               \"value\": null\r\n              },\r\n              {\r\n                \"color\":
    \"#eab839\",\r\n                \"value\": 10\r\n              },\r\n              {\r\n
    \               \"color\": \"#56A64B\",\r\n                \"value\": 100\r\n
    \             }\r\n            ]\r\n          },\r\n          \"unit\": \"short\"\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n
    \       \"h\": 2,\r\n        \"w\": 12,\r\n        \"x\": 0,\r\n        \"y\":
    0\r\n      },\r\n      \"id\": 1,\r\n      \"interval\": null,\r\n      \"links\":
    [],\r\n      \"maxDataPoints\": 100,\r\n      \"maxPerRow\": 2,\r\n      \"options\":
    {\r\n        \"colorMode\": \"background\",\r\n        \"graphMode\": \"none\",\r\n
    \       \"justifyMode\": \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n
    \       \"reduceOptions\": {\r\n          \"calcs\": [\r\n            \"lastNotNull\"\r\n
    \         ],\r\n          \"fields\": \"\",\r\n          \"values\": false\r\n
    \       },\r\n        \"text\": {\r\n          \"valueSize\": 30\r\n        },\r\n
    \       \"textMode\": \"auto\"\r\n      },\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"repeat\": \"cluster\",\r\n      \"repeatDirection\": \"h\",\r\n      \"targets\":
    [\r\n        {\r\n          \"$hashKey\": \"object:243\",\r\n          \"expr\":
    \"sum(pg_up{pg_cluster=~\\\"$cluster\\\"})*100+sum(ccp_is_in_recovery_status{pg_cluster=~\\\"$cluster\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"{{cluster}}\",\r\n          \"metric\": \"up\",\r\n
    \         \"refId\": \"A\",\r\n          \"step\": 2\r\n        }\r\n      ],\r\n
    \     \"title\": \"$cluster - Overview\",\r\n      \"type\": \"stat\"\r\n    }\r\n
    \ ],\r\n  \"refresh\": \"5m\",\r\n  \"schemaVersion\": 27,\r\n  \"style\": \"dark\",\r\n
    \ \"tags\": [],\r\n  \"templating\": {\r\n    \"list\": [\r\n      {\r\n        \"allFormat\":
    \"glob\",\r\n        \"allValue\": null,\r\n        \"current\": {},\r\n        \"datasource\":
    \"PROMETHEUS\",\r\n        \"definition\": \"label_values(pg_cluster)\",\r\n        \"description\":
    null,\r\n        \"error\": null,\r\n        \"hide\": 1,\r\n        \"includeAll\":
    true,\r\n        \"label\": \"cluster\",\r\n        \"multi\": true,\r\n        \"name\":
    \"cluster\",\r\n        \"options\": [],\r\n        \"query\": {\r\n          \"query\":
    \"label_values(pg_cluster)\",\r\n          \"refId\": \"PROMETHEUS-cluster-Variable-Query\"\r\n
    \       },\r\n        \"refresh\": 1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\":
    false,\r\n        \"sort\": 0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\":
    [],\r\n        \"tagsQuery\": \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\":
    false\r\n      }\r\n    ]\r\n  },\r\n  \"time\": {\r\n    \"from\": \"now-5m\",\r\n
    \   \"to\": \"now\"\r\n  },\r\n  \"timepicker\": {\r\n    \"time_options\": [\r\n
    \     \"5m\",\r\n      \"15m\",\r\n      \"1h\",\r\n      \"6h\",\r\n      \"12h\",\r\n
    \     \"24h\",\r\n      \"2d\",\r\n      \"7d\",\r\n      \"30d\"\r\n    ]\r\n
    \ },\r\n  \"timezone\": \"browser\",\r\n  \"title\": \"PostgreSQL Overview\",\r\n
    \ \"uid\": \"D2X39SlGk\",\r\n  \"version\": 1\r\n}\r\n"
  postgresql_details.json: "{\r\n  \"__inputs\": [\r\n    {\r\n      \"name\": \"DS_PROMETHEUS\",\r\n
    \     \"label\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"type\":
    \"datasource\",\r\n      \"pluginId\": \"prometheus\",\r\n      \"pluginName\":
    \"Prometheus\"\r\n    }\r\n  ],\r\n  \"__requires\": [\r\n    {\r\n      \"type\":
    \"panel\",\r\n      \"id\": \"gauge\",\r\n      \"name\": \"Gauge\",\r\n      \"version\":
    \"\"\r\n    },\r\n    {\r\n      \"type\": \"grafana\",\r\n      \"id\": \"grafana\",\r\n
    \     \"name\": \"Grafana\",\r\n      \"version\": \"7.4.5\"\r\n    },\r\n    {\r\n
    \     \"type\": \"panel\",\r\n      \"id\": \"graph\",\r\n      \"name\": \"Graph\",\r\n
    \     \"version\": \"\"\r\n    },\r\n    {\r\n      \"type\": \"datasource\",\r\n
    \     \"id\": \"prometheus\",\r\n      \"name\": \"Prometheus\",\r\n      \"version\":
    \"1.0.0\"\r\n    },\r\n    {\r\n      \"type\": \"panel\",\r\n      \"id\": \"stat\",\r\n
    \     \"name\": \"Stat\",\r\n      \"version\": \"\"\r\n    }\r\n  ],\r\n  \"annotations\":
    {\r\n    \"list\": [\r\n      {\r\n        \"builtIn\": 1,\r\n        \"datasource\":
    \"-- Grafana --\",\r\n        \"enable\": true,\r\n        \"hide\": true,\r\n
    \       \"iconColor\": \"rgba(0, 211, 255, 1)\",\r\n        \"name\": \"Annotations
    & Alerts\",\r\n        \"type\": \"dashboard\"\r\n      }\r\n    ]\r\n  },\r\n
    \ \"editable\": true,\r\n  \"gnetId\": null,\r\n  \"graphTooltip\": 0,\r\n  \"id\":
    null,\r\n  \"iteration\": 1624495934950,\r\n  \"links\": [\r\n    {\r\n      \"asDropdown\":
    false,\r\n      \"icon\": \"external link\",\r\n      \"includeVars\": true,\r\n
    \     \"keepTime\": true,\r\n      \"tags\": [\r\n        \"vendor=crunchydata\"\r\n
    \     ],\r\n      \"title\": \"\",\r\n      \"type\": \"dashboards\"\r\n    }\r\n
    \ ],\r\n  \"panels\": [\r\n    {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\":
    {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"links\": [\r\n            {\r\n              \"title\": \"pgBackrest\",\r\n
    \             \"url\": \"/dashboard/db/pgbackrest?${__all_variables}\"\r\n            }\r\n
    \         ],\r\n          \"mappings\": [\r\n            {\r\n              \"id\":
    0,\r\n              \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"thresholds\":
    {\r\n            \"mode\": \"absolute\",\r\n            \"steps\": [\r\n              {\r\n
    \               \"color\": \"#56A64B\",\r\n                \"value\": null\r\n
    \             },\r\n              {\r\n                \"color\": \"#FF9830\",\r\n
    \               \"value\": 86400\r\n              },\r\n              {\r\n                \"color\":
    \"#E02F44\",\r\n                \"value\": 172800\r\n              }\r\n            ]\r\n
    \         },\r\n          \"unit\": \"dtdurations\"\r\n        },\r\n        \"overrides\":
    []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\": 2,\r\n        \"w\":
    24,\r\n        \"x\": 0,\r\n        \"y\": 0\r\n      },\r\n      \"id\": 27,\r\n
    \     \"interval\": null,\r\n      \"links\": [\r\n        {\r\n          \"title\":
    \"pgBackRest\",\r\n          \"url\": \"/dashboard/db/pgbackrest?${__all_variables}\"\r\n
    \       }\r\n      ],\r\n      \"maxDataPoints\": 100,\r\n      \"options\": {\r\n
    \       \"colorMode\": \"background\",\r\n        \"graphMode\": \"none\",\r\n
    \       \"justifyMode\": \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n
    \       \"reduceOptions\": {\r\n          \"calcs\": [\r\n            \"min\"\r\n
    \         ],\r\n          \"fields\": \"\",\r\n          \"values\": false\r\n
    \       },\r\n        \"text\": {},\r\n        \"textMode\": \"auto\"\r\n      },\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\":
    \"min(ccp_backrest_last_incr_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\"}
    < ccp_backrest_last_diff_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\"}
    or ccp_backrest_last_incr_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\"}
    < ccp_backrest_last_full_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\"}
    or ccp_backrest_last_incr_backup_time_since_completion_seconds{pg_cluster=\\\"[[cluster]]\\\"})
    \",\r\n          \"format\": \"time_series\",\r\n          \"interval\": \"\",\r\n
    \         \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n          \"refId\":
    \"A\"\r\n        }\r\n      ],\r\n      \"timeFrom\": null,\r\n      \"timeShift\":
    null,\r\n      \"title\": \"[[cluster]] : Backup Status\",\r\n      \"type\":
    \"stat\"\r\n    },\r\n    {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\":
    {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\": 0,\r\n
    \             \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"max\":
    100,\r\n          \"min\": 0,\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"rgba(50, 172, 45, 0.97)\",\r\n                \"value\": null\r\n              },\r\n
    \             {\r\n                \"color\": \"rgba(237, 129, 40, 0.89)\",\r\n
    \               \"value\": 70\r\n              },\r\n              {\r\n                \"color\":
    \"rgba(245, 54, 54, 0.9)\",\r\n                \"value\": 90\r\n              }\r\n
    \           ]\r\n          },\r\n          \"unit\": \"percent\"\r\n        },\r\n
    \       \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    5,\r\n        \"w\": 4,\r\n        \"x\": 0,\r\n        \"y\": 2\r\n      },\r\n
    \     \"id\": 8,\r\n      \"interval\": null,\r\n      \"links\": [],\r\n      \"maxDataPoints\":
    100,\r\n      \"options\": {\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [\r\n            \"lastNotNull\"\r\n          ],\r\n
    \         \"fields\": \"\",\r\n          \"values\": false\r\n        },\r\n        \"showThresholdLabels\":
    false,\r\n        \"showThresholdMarkers\": true,\r\n        \"text\": {}\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum(pg_stat_activity_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",
    state=\\\"active\\\"})*100 /sum(pg_settings_max_connections{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"instant\":
    true,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"\",\r\n          \"metric\": \"pg_stat_activity_count\",\r\n
    \         \"refId\": \"A\",\r\n          \"step\": 10\r\n        }\r\n      ],\r\n
    \     \"title\": \"Active Connections\",\r\n      \"type\": \"gauge\"\r\n    },\r\n
    \   {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\": \"PROMETHEUS\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\": {\r\n
    \           \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\": 0,\r\n
    \             \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"max\":
    100,\r\n          \"min\": 0,\r\n          \"thresholds\": {\r\n            \"mode\":
    \"percentage\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"rgba(50, 172, 45, 0.97)\",\r\n                \"value\": null\r\n              },\r\n
    \             {\r\n                \"color\": \"rgba(237, 129, 40, 0.89)\",\r\n
    \               \"value\": 10\r\n              },\r\n              {\r\n                \"color\":
    \"rgba(245, 54, 54, 0.9)\",\r\n                \"value\": 30\r\n              }\r\n
    \           ]\r\n          },\r\n          \"unit\": \"percentunit\"\r\n        },\r\n
    \       \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    5,\r\n        \"w\": 4,\r\n        \"x\": 4,\r\n        \"y\": 2\r\n      },\r\n
    \     \"id\": 2,\r\n      \"interval\": null,\r\n      \"links\": [],\r\n      \"maxDataPoints\":
    100,\r\n      \"options\": {\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [\r\n            \"lastNotNull\"\r\n          ],\r\n
    \         \"fields\": \"\",\r\n          \"values\": false\r\n        },\r\n        \"showThresholdLabels\":
    false,\r\n        \"showThresholdMarkers\": true,\r\n        \"text\": {}\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum(pg_stat_activity_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",state=\\\"idle
    in transaction\\\"})/sum(pg_settings_max_connections{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"instant\": true,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n
    \         \"refId\": \"B\"\r\n        }\r\n      ],\r\n      \"title\": \"Idle
    In Transaction\",\r\n      \"type\": \"gauge\"\r\n    },\r\n    {\r\n      \"cacheTimeout\":
    null,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\": {\r\n
    \           \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\": 0,\r\n
    \             \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"max\":
    1200,\r\n          \"min\": 0,\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"rgba(50, 172, 45, 0.97)\",\r\n                \"value\": null\r\n              },\r\n
    \             {\r\n                \"color\": \"rgba(237, 129, 40, 0.89)\",\r\n
    \               \"value\": 300\r\n              },\r\n              {\r\n                \"color\":
    \"rgba(245, 54, 54, 0.9)\",\r\n                \"value\": 900\r\n              }\r\n
    \           ]\r\n          },\r\n          \"unit\": \"s\"\r\n        },\r\n        \"overrides\":
    []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\": 5,\r\n        \"w\":
    4,\r\n        \"x\": 8,\r\n        \"y\": 2\r\n      },\r\n      \"id\": 34,\r\n
    \     \"interval\": null,\r\n      \"links\": [],\r\n      \"maxDataPoints\":
    100,\r\n      \"options\": {\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [\r\n            \"lastNotNull\"\r\n          ],\r\n
    \         \"fields\": \"\",\r\n          \"values\": false\r\n        },\r\n        \"showThresholdLabels\":
    false,\r\n        \"showThresholdMarkers\": true,\r\n        \"text\": {}\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"max(abs(ccp_connection_stats_max_idle_in_txn_time{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"}))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"instant\": true,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n
    \         \"refId\": \"B\"\r\n        }\r\n      ],\r\n      \"title\": \"Max
    Idle In Transaction Time\",\r\n      \"type\": \"gauge\"\r\n    },\r\n    {\r\n
    \     \"cacheTimeout\": null,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\":
    {\r\n        \"defaults\": {\r\n          \"color\": {\r\n            \"mode\":
    \"thresholds\"\r\n          },\r\n          \"custom\": {},\r\n          \"mappings\":
    [\r\n            {\r\n              \"id\": 0,\r\n              \"op\": \"=\",\r\n
    \             \"text\": \"N/A\",\r\n              \"type\": 1,\r\n              \"value\":
    \"null\"\r\n            }\r\n          ],\r\n          \"max\": 100,\r\n          \"min\":
    0,\r\n          \"thresholds\": {\r\n            \"mode\": \"absolute\",\r\n            \"steps\":
    [\r\n              {\r\n                \"color\": \"rgba(50, 172, 45, 0.97)\",\r\n
    \               \"value\": null\r\n              },\r\n              {\r\n                \"color\":
    \"rgba(237, 129, 40, 0.89)\",\r\n                \"value\": 60\r\n              },\r\n
    \             {\r\n                \"color\": \"rgba(245, 54, 54, 0.9)\",\r\n
    \               \"value\": 80\r\n              }\r\n            ]\r\n          },\r\n
    \         \"unit\": \"percent\"\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"gridPos\": {\r\n        \"h\": 5,\r\n        \"w\": 4,\r\n
    \       \"x\": 12,\r\n        \"y\": 2\r\n      },\r\n      \"id\": 3,\r\n      \"interval\":
    null,\r\n      \"links\": [],\r\n      \"maxDataPoints\": 100,\r\n      \"options\":
    {\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\": {\r\n
    \         \"calcs\": [\r\n            \"lastNotNull\"\r\n          ],\r\n          \"fields\":
    \"\",\r\n          \"values\": false\r\n        },\r\n        \"showThresholdLabels\":
    false,\r\n        \"showThresholdMarkers\": true,\r\n        \"text\": {}\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum(pg_stat_activity_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",state=\\\"idle\\\"})*100/sum(pg_settings_max_connections{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"instant\": true,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n
    \         \"metric\": \"pg_stat_activity_count\",\r\n          \"refId\": \"A\",\r\n
    \         \"step\": 10\r\n        }\r\n      ],\r\n      \"title\": \"Idle\",\r\n
    \     \"type\": \"gauge\"\r\n    },\r\n    {\r\n      \"cacheTimeout\": null,\r\n
    \     \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\":
    {\r\n          \"color\": {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n
    \         \"custom\": {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\":
    0,\r\n              \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"max\":
    100,\r\n          \"min\": 0,\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"rgba(50, 172, 45, 0.97)\",\r\n                \"value\": null\r\n              },\r\n
    \             {\r\n                \"color\": \"rgba(237, 129, 40, 0.89)\",\r\n
    \               \"value\": 60\r\n              },\r\n              {\r\n                \"color\":
    \"rgba(245, 54, 54, 0.9)\",\r\n                \"value\": 80\r\n              }\r\n
    \           ]\r\n          },\r\n          \"unit\": \"percent\"\r\n        },\r\n
    \       \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    5,\r\n        \"w\": 4,\r\n        \"x\": 16,\r\n        \"y\": 2\r\n      },\r\n
    \     \"id\": 32,\r\n      \"interval\": null,\r\n      \"links\": [],\r\n      \"maxDataPoints\":
    100,\r\n      \"options\": {\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [\r\n            \"lastNotNull\"\r\n          ],\r\n
    \         \"fields\": \"\",\r\n          \"values\": false\r\n        },\r\n        \"showThresholdLabels\":
    false,\r\n        \"showThresholdMarkers\": true,\r\n        \"text\": {}\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"max(ccp_transaction_wraparound_percent_towards_wraparound{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"instant\": true,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n
    \         \"metric\": \"pg_stat_activity_count\",\r\n          \"refId\": \"A\",\r\n
    \         \"step\": 10\r\n        }\r\n      ],\r\n      \"title\": \"% Toward
    Wraparound\",\r\n      \"type\": \"gauge\"\r\n    },\r\n    {\r\n      \"cacheTimeout\":
    null,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\": {\r\n
    \           \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\": 0,\r\n
    \             \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"max\":
    100,\r\n          \"min\": 0,\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"#E02F44\",\r\n                \"value\": null\r\n              },\r\n              {\r\n
    \               \"color\": \"rgba(237, 129, 40, 0.89)\",\r\n                \"value\":
    80\r\n              },\r\n              {\r\n                \"color\": \"#56A64B\",\r\n
    \               \"value\": 90\r\n              }\r\n            ]\r\n          },\r\n
    \         \"unit\": \"percent\"\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"gridPos\": {\r\n        \"h\": 5,\r\n        \"w\": 4,\r\n
    \       \"x\": 20,\r\n        \"y\": 2\r\n      },\r\n      \"id\": 33,\r\n      \"interval\":
    null,\r\n      \"links\": [],\r\n      \"maxDataPoints\": 100,\r\n      \"options\":
    {\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\": {\r\n
    \         \"calcs\": [\r\n            \"lastNotNull\"\r\n          ],\r\n          \"fields\":
    \"\",\r\n          \"values\": false\r\n        },\r\n        \"showThresholdLabels\":
    false,\r\n        \"showThresholdMarkers\": true,\r\n        \"text\": {}\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum(ccp_stat_database_blks_hit{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})*100/sum(ccp_stat_database_blks_hit{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"}+ccp_stat_database_blks_read{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"instant\": true,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n
    \         \"metric\": \"pg_stat_activity_count\",\r\n          \"refId\": \"A\",\r\n
    \         \"step\": 10\r\n        }\r\n      ],\r\n      \"title\": \"Cache Hit
    Ratio\",\r\n      \"type\": \"gauge\"\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 0,\r\n        \"y\": 7\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 24,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"nullPointMode\":
    \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n
    \     \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\":
    5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum(irate(ccp_stat_database_xact_commit{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))
    + sum(irate(ccp_stat_database_xact_rollback{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"Transactions\",\r\n
    \         \"metric\": \"ccp_stat_database_tup_fetched\",\r\n          \"refId\":
    \"A\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n          \"expr\":
    \"sum(irate(ccp_pg_stat_statements_total_calls_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"Queries\",\r\n
    \         \"metric\": \"ccp_stat_database_tup_fetched\",\r\n          \"refId\":
    \"B\",\r\n          \"step\": 2\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Activity -  [[pod]]-[[datname]]\",\r\n      \"tooltip\":
    {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n        \"value_type\":
    \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n      \"xaxis\": {\r\n
    \       \"buckets\": null,\r\n        \"mode\": \"time\",\r\n        \"name\":
    null,\r\n        \"show\": true,\r\n        \"values\": []\r\n      },\r\n      \"yaxes\":
    [\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": \"0\",\r\n
    \         \"show\": true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": false\r\n        }\r\n
    \     ],\r\n      \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\":
    null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\":
    false,\r\n      \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 6,\r\n        \"w\": 12,\r\n        \"x\": 12,\r\n        \"y\":
    7\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 26,\r\n      \"legend\":
    {\r\n        \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n        \"rightSide\":
    true,\r\n        \"show\": true,\r\n        \"sideWidth\": 150,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"nullPointMode\": \"null\",\r\n      \"options\":
    {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\": false,\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n      \"points\":
    false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\": [],\r\n
    \     \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum by (state)
    (pg_stat_activity_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",state=\\\"idle\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"idle\",\r\n
    \         \"metric\": \"ccp_stat_database_tup_fetched\",\r\n          \"refId\":
    \"A\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n          \"expr\":
    \"sum by (state) (pg_stat_activity_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",state=\\\"idle
    in transaction\\\"})\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Idle
    in txn\",\r\n          \"refId\": \"B\"\r\n        },\r\n        {\r\n          \"expr\":
    \"sum by (state) (pg_stat_activity_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",state=\\\"active\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"active\",\r\n          \"refId\": \"C\"\r\n        }\r\n
    \     ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Connections -  [[pod]]-[[datname]]\",\r\n
    \     \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n
    \       \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": \"0\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": true\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 0,\r\n        \"y\": 13\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 12,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum(ccp_database_size_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})/(1024*1024)\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"Total : [[cluster]]-[[pod]]\",\r\n          \"refId\":
    \"B\"\r\n        },\r\n        {\r\n          \"expr\": \"ccp_database_size_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",dbname=~\\\"[[datname]]\\\"}/(1024*1024)\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"{{dbname}} ({{pod}})\",\r\n          \"refId\":
    \"A\"\r\n        }\r\n      ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\":
    null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\": null,\r\n      \"title\":
    \"database size - [[pod]]-[[datname]]\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"decmbytes\",\r\n          \"label\": null,\r\n          \"logBase\": 2,\r\n
    \         \"max\": null,\r\n          \"min\": null,\r\n          \"show\": true\r\n
    \       },\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\":
    null,\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\":
    null,\r\n          \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\":
    {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n      }\r\n
    \   },\r\n    {\r\n      \"aliasColors\": {\r\n        \"Max: ccp_monitoring(postgres)\":
    \"red\"\r\n      },\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n
    \     \"dashes\": false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"description\":
    \"\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 6,\r\n        \"w\": 12,\r\n        \"x\": 12,\r\n        \"y\":
    13\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 31,\r\n      \"legend\":
    {\r\n        \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n        \"rightSide\":
    true,\r\n        \"show\": true,\r\n        \"sideWidth\": 150,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"nullPointMode\": \"null\",\r\n      \"options\":
    {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\": false,\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n      \"points\":
    false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\": [\r\n        {\r\n
    \         \"alias\": \"/Max:/\",\r\n          \"color\": \"#FF9830\"\r\n        },\r\n
    \       {\r\n          \"alias\": \"/Avg:/\",\r\n          \"color\": \"#5794F2\"\r\n
    \       }\r\n      ],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n
    \     \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\":
    \"max(ccp_pg_stat_statements_total_mean_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",
    dbname=~\\\"[[datname]]\\\"}) without (instance,ip)\",\r\n          \"format\":
    \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\": \"\",\r\n
    \         \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Avg: {{exported_role}}({{dbname}})\",\r\n
    \         \"metric\": \"ccp_stat_database_tup_fetched\",\r\n          \"refId\":
    \"A\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n          \"expr\":
    \"max(ccp_pg_stat_statements_top_max_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",dbname=~\\\"[[datname]]\\\"})
    without (instance,ip,query,queryid)\",\r\n          \"format\": \"time_series\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"Max: {{exported_role}}({{dbname}})\",\r\n
    \         \"metric\": \"ccp_stat_database_tup_fetched\",\r\n          \"refId\":
    \"B\",\r\n          \"step\": 2\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Query Duration - [[pod]]-[[datname]]\",\r\n      \"tooltip\":
    {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n        \"value_type\":
    \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n      \"xaxis\": {\r\n
    \       \"buckets\": null,\r\n        \"mode\": \"time\",\r\n        \"name\":
    null,\r\n        \"show\": true,\r\n        \"values\": []\r\n      },\r\n      \"yaxes\":
    [\r\n        {\r\n          \"format\": \"ms\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": false\r\n        }\r\n
    \     ],\r\n      \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\":
    null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\":
    false,\r\n      \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 6,\r\n        \"w\": 12,\r\n        \"x\": 0,\r\n        \"y\":
    19\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 11,\r\n      \"interval\":
    \"\",\r\n      \"legend\": {\r\n        \"alignAsTable\": true,\r\n        \"avg\":
    false,\r\n        \"current\": false,\r\n        \"max\": false,\r\n        \"min\":
    false,\r\n        \"rightSide\": true,\r\n        \"show\": true,\r\n        \"sideWidth\":
    150,\r\n        \"total\": false,\r\n        \"values\": false\r\n      },\r\n
    \     \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"nullPointMode\":
    \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n
    \     \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\":
    5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum(irate(ccp_stat_database_tup_fetched{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"Fetched\",\r\n
    \         \"metric\": \"ccp_stat_database_tup_fetched\",\r\n          \"refId\":
    \"A\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n          \"expr\":
    \"sum(irate(ccp_stat_database_tup_inserted{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"Inserted\",\r\n          \"metric\": \"ccp_stat_database_tup_inserted\",\r\n
    \         \"refId\": \"B\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(irate(ccp_stat_database_tup_updated{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"Updated\",\r\n          \"metric\": \"ccp_stat_database_tup_updated\",\r\n
    \         \"refId\": \"C\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(irate(ccp_stat_database_tup_deleted{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"Deleted\",\r\n          \"metric\": \"ccp_stat_database_tup_deleted\",\r\n
    \         \"refId\": \"D\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(irate(ccp_stat_database_tup_returned{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"Returned\",\r\n          \"metric\": \"ccp_stat_database_tup_deleted\",\r\n
    \         \"refId\": \"E\",\r\n          \"step\": 2\r\n        }\r\n      ],\r\n
    \     \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Row activity - [[pod]]-[[datname]]\",\r\n
    \     \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n
    \       \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 2,\r\n          \"max\":
    null,\r\n          \"min\": \"0\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 12,\r\n        \"y\": 19\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 30,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"ccp_wal_activity_total_size_bytes{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"}/(1024*1024)\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"{{pod}}\",\r\n          \"refId\": \"B\"\r\n
    \       }\r\n      ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n
    \     \"timeRegions\": [],\r\n      \"timeShift\": null,\r\n      \"title\": \"WAL
    size MB - [[cluster]]-[[pod]]\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"decmbytes\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n
    \         \"max\": null,\r\n          \"min\": null,\r\n          \"show\": true\r\n
    \       },\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\":
    null,\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\":
    null,\r\n          \"show\": true\r\n        }\r\n      ],\r\n      \"yaxis\":
    {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n      }\r\n
    \   },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\": false,\r\n      \"dashLength\":
    10,\r\n      \"dashes\": false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\":
    {\r\n        \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\":
    []\r\n        },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 0,\r\n        \"y\": 25\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 15,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [\r\n        {\r\n          \"alias\": \"Commits\",\r\n
    \         \"color\": \"#73BF69\"\r\n        },\r\n        {\r\n          \"alias\":
    \"DeadLocks\",\r\n          \"color\": \"#C4162A\"\r\n        },\r\n        {\r\n
    \         \"alias\": \"Conflicts\",\r\n          \"color\": \"#FF9830\"\r\n        },\r\n
    \       {\r\n          \"alias\": \"Rollbacks\",\r\n          \"color\": \"#FFCB7D\"\r\n
    \       }\r\n      ],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n
    \     \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\":
    \"sum(rate(ccp_stat_database_deadlocks{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"Conflicts\",\r\n          \"metric\": \"ccp_stat_database_conflicts\",\r\n
    \         \"refId\": \"A\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(rate(ccp_stat_database_conflicts{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"DeadLocks\",\r\n          \"metric\": \"ccp_stat_database_deadlocks\",\r\n
    \         \"refId\": \"B\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(irate(ccp_stat_database_xact_commit{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"Commits\",\r\n
    \         \"metric\": \"ccp_stat_database_deadlocks\",\r\n          \"refId\":
    \"C\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n          \"expr\":
    \"sum(irate(ccp_stat_database_xact_rollback{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\"}[5m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"Rollbacks\",\r\n
    \         \"metric\": \"ccp_stat_database_deadlocks\",\r\n          \"refId\":
    \"D\",\r\n          \"step\": 2\r\n        }\r\n      ],\r\n      \"thresholds\":
    [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Key Counters - [[pod]] - [[datname]]\",\r\n      \"tooltip\":
    {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n        \"value_type\":
    \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n      \"xaxis\": {\r\n
    \       \"buckets\": null,\r\n        \"mode\": \"time\",\r\n        \"name\":
    null,\r\n        \"show\": true,\r\n        \"values\": []\r\n      },\r\n      \"yaxes\":
    [\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": \"0\",\r\n
    \         \"show\": true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        }\r\n
    \     ],\r\n      \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\":
    null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\":
    false,\r\n      \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 0,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 12,\r\n        \"y\": 25\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 29,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"hideEmpty\":
    false,\r\n        \"hideZero\": false,\r\n        \"max\": false,\r\n        \"min\":
    false,\r\n        \"rightSide\": true,\r\n        \"show\": true,\r\n        \"sideWidth\":
    150,\r\n        \"total\": false,\r\n        \"values\": true\r\n      },\r\n
    \     \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\": [],\r\n
    \     \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [\r\n        {\r\n          \"alias\": \"/Time/\",\r\n
    \         \"yaxis\": 2\r\n        }\r\n      ],\r\n      \"spaceLength\": 10,\r\n
    \     \"stack\": false,\r\n      \"steppedLine\": false,\r\n      \"targets\":
    [\r\n        {\r\n          \"exemplar\": false,\r\n          \"expr\": \"ccp_replication_lag_size_bytes{pg_cluster=\\\"[[cluster]]\\\",
    role!=\\\"replica\\\"}\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"instant\": false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"Bytes ({{replica}})\",\r\n          \"refId\":
    \"B\"\r\n        },\r\n        {\r\n          \"expr\": \"ccp_replication_lag_replay_time{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"replica\\\"}\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"Time ({{ip}})\",\r\n          \"refId\": \"A\"\r\n
    \       }\r\n      ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n
    \     \"timeRegions\": [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Replication
    Lag - [[cluster]]\",\r\n      \"tooltip\": {\r\n        \"shared\": true,\r\n
    \       \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"short\",\r\n          \"label\": \"Lag bytes\",\r\n          \"logBase\": 1,\r\n
    \         \"max\": null,\r\n          \"min\": 0,\r\n          \"show\": true\r\n
    \       },\r\n        {\r\n          \"format\": \"dtdhms\",\r\n          \"label\":
    \"Lag time (hh:mm:ss)\",\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n
    \         \"min\": 0,\r\n          \"show\": true\r\n        }\r\n      ],\r\n
    \     \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n
    \     }\r\n    },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\": false,\r\n
    \     \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 6,\r\n        \"w\": 12,\r\n        \"x\": 0,\r\n        \"y\":
    31\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 14,\r\n      \"legend\":
    {\r\n        \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n        \"rightSide\":
    true,\r\n        \"show\": true,\r\n        \"sideWidth\": 150,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"links\": [],\r\n      \"nullPointMode\": \"null\",\r\n
    \     \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\":
    false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n
    \     \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum(ccp_stat_bgwriter_buffers_alloc{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"Allocated\",\r\n          \"metric\": \"pg_stat_bgwriter_buffers_alloc\",\r\n
    \         \"refId\": \"A\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(ccp_stat_bgwriter_buffers_backend{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"Backend\",\r\n          \"metric\": \"pg_stat_bgwriter_buffers_backend\",\r\n
    \         \"refId\": \"B\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(ccp_stat_bgwriter_buffers_backend_fsync{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"FSync\",\r\n          \"metric\": \"pg_stat_bgwriter_buffers_backend_fsync\",\r\n
    \         \"refId\": \"C\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(ccp_stat_bgwriter_buffers_checkpoint{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"CheckPoint\",\r\n          \"metric\": \"pg_stat_bgwriter_buffers_checkpoint\",\r\n
    \         \"refId\": \"D\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(ccp_stat_bgwriter_buffers_clean{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"Clean\",\r\n          \"metric\": \"pg_stat_bgwriter_buffers_clean\",\r\n
    \         \"refId\": \"E\",\r\n          \"step\": 2\r\n        }\r\n      ],\r\n
    \     \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Buffers - [[pod]]\",\r\n
    \     \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n
    \       \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": \"0\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": true\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 12,\r\n        \"y\": 31\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 17,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum by (mode) (ccp_locks_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",mode=\\\"accessexclusivelock\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"{{mode}}\",\r\n
    \         \"refId\": \"A\",\r\n          \"step\": 2\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum by (mode) (ccp_locks_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",mode=\\\"exclusivelock\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"{{mode}}\",\r\n          \"refId\": \"C\",\r\n
    \         \"step\": 2\r\n        },\r\n        {\r\n          \"expr\": \"sum
    by (mode) (ccp_locks_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",mode=\\\"rowexclusivelock\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"{{mode}}\",\r\n          \"refId\": \"D\",\r\n
    \         \"step\": 2\r\n        },\r\n        {\r\n          \"expr\": \"sum
    by (mode) (ccp_locks_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",mode=\\\"sharerowexclusivelock\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"{{mode}}\",\r\n          \"refId\": \"G\",\r\n
    \         \"step\": 2\r\n        },\r\n        {\r\n          \"expr\": \"sum
    by (mode) (ccp_locks_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",mode=\\\"shareupdateexclusivelock\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"intervalFactor\":
    2,\r\n          \"legendFormat\": \"{{mode}}\",\r\n          \"refId\": \"H\",\r\n
    \         \"step\": 2\r\n        },\r\n        {\r\n          \"expr\": \"sum
    by (mode) (ccp_locks_count{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",datname=~\\\"[[datname]]\\\",mode=\\\"accesssharelock\\\"})\",\r\n
    \         \"format\": \"time_series\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"{{mode}}\",\r\n          \"refId\": \"B\"\r\n        }\r\n
    \     ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Locks - [[pod]]-[[datname]]\",\r\n
    \     \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n
    \       \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": \"0\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 0,\r\n      \"gridPos\": {\r\n        \"h\": 6,\r\n        \"w\":
    12,\r\n        \"x\": 12,\r\n        \"y\": 37\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 28,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"ccp_stat_database_blks_hit{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",
    dbname!~\\\"template0\\\", dbname!~\\\"template1\\\"}*100/(ccp_stat_database_blks_hit{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",
    dbname!~\\\"template0\\\", dbname!~\\\"template1\\\"} + ccp_stat_database_blks_read{pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\",dbname!~\\\"template0\\\",
    dbname!~\\\"template1\\\"})\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"{{dbname}} - ({{pod}})\",\r\n          \"refId\":
    \"A\"\r\n        }\r\n      ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\":
    null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\": null,\r\n      \"title\":
    \"Cache Hit Ratio - [[pod]]-[[datname]]\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"decimals\":
    null,\r\n          \"format\": \"percent\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": \"100\",\r\n          \"min\":
    \"0\",\r\n          \"show\": true\r\n        },\r\n        {\r\n          \"format\":
    \"short\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": false\r\n        }\r\n
    \     ],\r\n      \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\":
    null\r\n      }\r\n    }\r\n  ],\r\n  \"refresh\": \"5s\",\r\n  \"schemaVersion\":
    27,\r\n  \"style\": \"dark\",\r\n  \"tags\": [\r\n    \"vendor=crunchydata\"\r\n
    \ ],\r\n  \"templating\": {\r\n    \"list\": [\r\n      {\r\n        \"allFormat\":
    \"glob\",\r\n        \"allValue\": null,\r\n        \"current\": {},\r\n        \"datasource\":
    \"PROMETHEUS\",\r\n        \"definition\": \"\",\r\n        \"description\": null,\r\n
    \       \"error\": null,\r\n        \"hide\": 0,\r\n        \"includeAll\": false,\r\n
    \       \"label\": \"cluster\",\r\n        \"multi\": false,\r\n        \"name\":
    \"cluster\",\r\n        \"options\": [],\r\n        \"query\": {\r\n          \"query\":
    \"label_values(pg_cluster)\",\r\n          \"refId\": \"PROMETHEUS-cluster-Variable-Query\"\r\n
    \       },\r\n        \"refresh\": 1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\":
    false,\r\n        \"sort\": 0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\":
    [],\r\n        \"tagsQuery\": \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\":
    false\r\n      },\r\n      {\r\n        \"allFormat\": \"glob\",\r\n        \"allValue\":
    \".*\",\r\n        \"current\": {},\r\n        \"datasource\": \"PROMETHEUS\",\r\n
    \       \"definition\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},pod)\",\r\n
    \       \"description\": null,\r\n        \"error\": null,\r\n        \"hide\":
    0,\r\n        \"includeAll\": true,\r\n        \"label\": \"pod\",\r\n        \"multi\":
    true,\r\n        \"name\": \"pod\",\r\n        \"options\": [],\r\n        \"query\":
    {\r\n          \"query\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},pod)\",\r\n
    \         \"refId\": \"PROMETHEUS-pod-Variable-Query\"\r\n        },\r\n        \"refresh\":
    1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    1,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      },\r\n
    \     {\r\n        \"allFormat\": \"glob\",\r\n        \"allValue\": \".*\",\r\n
    \       \"current\": {},\r\n        \"datasource\": \"PROMETHEUS\",\r\n        \"definition\":
    \"label_values({pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"},dbname)\",\r\n
    \       \"description\": null,\r\n        \"error\": null,\r\n        \"hide\":
    0,\r\n        \"includeAll\": true,\r\n        \"label\": \"Database\",\r\n        \"multi\":
    true,\r\n        \"name\": \"datname\",\r\n        \"options\": [],\r\n        \"query\":
    {\r\n          \"query\": \"label_values({pg_cluster=\\\"[[cluster]]\\\",pod=~\\\"[[pod]]\\\"},dbname)\",\r\n
    \         \"refId\": \"PROMETHEUS-datname-Variable-Query\"\r\n        },\r\n        \"refresh\":
    1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    1,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      }\r\n
    \   ]\r\n  },\r\n  \"time\": {\r\n    \"from\": \"now-30m\",\r\n    \"to\": \"now\"\r\n
    \ },\r\n  \"timepicker\": {\r\n    \"time_options\": [\r\n      \"5m\",\r\n      \"15m\",\r\n
    \     \"1h\",\r\n      \"6h\",\r\n      \"12h\",\r\n      \"24h\",\r\n      \"2d\",\r\n
    \     \"7d\",\r\n      \"30d\"\r\n    ]\r\n  },\r\n  \"timezone\": \"browser\",\r\n
    \ \"title\": \"PostgreSQLDetails\",\r\n  \"uid\": \"pc4NNgknk\",\r\n  \"version\":
    1\r\n}\r\n"
  postgresql_service_health.json: "{\r\n  \"__inputs\": [\r\n    {\r\n      \"name\":
    \"DS_PROMETHEUS\",\r\n      \"label\": \"PROMETHEUS\",\r\n      \"description\":
    \"\",\r\n      \"type\": \"datasource\",\r\n      \"pluginId\": \"prometheus\",\r\n
    \     \"pluginName\": \"Prometheus\"\r\n    }\r\n  ],\r\n  \"__requires\": [\r\n
    \   {\r\n      \"type\": \"grafana\",\r\n      \"id\": \"grafana\",\r\n      \"name\":
    \"Grafana\",\r\n      \"version\": \"7.4.5\"\r\n    },\r\n    {\r\n      \"type\":
    \"panel\",\r\n      \"id\": \"graph\",\r\n      \"name\": \"Graph\",\r\n      \"version\":
    \"\"\r\n    },\r\n    {\r\n      \"type\": \"datasource\",\r\n      \"id\": \"prometheus\",\r\n
    \     \"name\": \"Prometheus\",\r\n      \"version\": \"1.0.0\"\r\n    }\r\n  ],\r\n
    \ \"annotations\": {\r\n    \"list\": [\r\n      {\r\n        \"builtIn\": 1,\r\n
    \       \"datasource\": \"-- Grafana --\",\r\n        \"enable\": true,\r\n        \"hide\":
    true,\r\n        \"iconColor\": \"rgba(0, 211, 255, 1)\",\r\n        \"name\":
    \"Annotations & Alerts\",\r\n        \"type\": \"dashboard\"\r\n      }\r\n    ]\r\n
    \ },\r\n  \"editable\": true,\r\n  \"gnetId\": null,\r\n  \"graphTooltip\": 0,\r\n
    \ \"id\": null,\r\n  \"iteration\": 1624491530019,\r\n  \"links\": [\r\n    {\r\n
    \     \"asDropdown\": false,\r\n      \"icon\": \"external link\",\r\n      \"includeVars\":
    true,\r\n      \"keepTime\": true,\r\n      \"tags\": [\r\n        \"vendor=crunchydata\"\r\n
    \     ],\r\n      \"title\": \"\",\r\n      \"type\": \"dashboards\"\r\n    }\r\n
    \ ],\r\n  \"panels\": [\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\":
    false,\r\n      \"dashLength\": 10,\r\n      \"dashes\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"links\": []\r\n        },\r\n        \"overrides\": []\r\n
    \     },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 5,\r\n      \"gridPos\":
    {\r\n        \"h\": 7,\r\n        \"w\": 12,\r\n        \"x\": 0,\r\n        \"y\":
    0\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 6,\r\n      \"legend\":
    {\r\n        \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n        \"rightSide\":
    true,\r\n        \"show\": true,\r\n        \"sideWidth\": 150,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"links\": [],\r\n      \"nullPointMode\": \"null\",\r\n
    \     \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\":
    false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n
    \     \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum(ccp_connection_stats_total{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    without (pod,instance,ip) / sum(ccp_connection_stats_max_connections{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    without (pod,instance,ip)\",\r\n          \"format\": \"time_series\",\r\n          \"instant\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"Connections\",\r\n          \"refId\": \"C\"\r\n
    \       },\r\n        {\r\n          \"expr\": \"100 - 100 * avg(ccp_nodemx_data_disk_available_bytes{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    without (pod,instance,ip) / avg(ccp_nodemx_data_disk_total_bytes{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    \ without (pod,instance,ip)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Mount:{{mount_point}}\",\r\n
    \         \"refId\": \"A\"\r\n        }\r\n      ],\r\n      \"thresholds\": [],\r\n
    \     \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Saturation (pct used)\",\r\n      \"tooltip\": {\r\n
    \       \"shared\": true,\r\n        \"sort\": 0,\r\n        \"value_type\": \"individual\"\r\n
    \     },\r\n      \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\":
    null,\r\n        \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\":
    true,\r\n        \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n
    \         \"decimals\": null,\r\n          \"format\": \"percent\",\r\n          \"label\":
    null,\r\n          \"logBase\": 1,\r\n          \"max\": \"100\",\r\n          \"min\":
    \"0\",\r\n          \"show\": true\r\n        },\r\n        {\r\n          \"format\":
    \"short\",\r\n          \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": false\r\n        }\r\n
    \     ],\r\n      \"yaxis\": {\r\n        \"align\": false,\r\n        \"alignLevel\":
    null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\":
    false,\r\n      \"cacheTimeout\": null,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\": []\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 5,\r\n      \"gridPos\": {\r\n        \"h\": 7,\r\n        \"w\":
    12,\r\n        \"x\": 12,\r\n        \"y\": 0\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 18,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 2,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"exemplar\": false,\r\n          \"expr\": \"  sum(irate(ccp_stat_database_xact_commit{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"}[1m]))
    \\n+ sum(irate(ccp_stat_database_xact_rollback{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"}[1m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"Transactions\",\r\n          \"refId\": \"A\"\r\n
    \       },\r\n        {\r\n          \"expr\": \"max(ccp_connection_stats_active{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    without (pod,instance,ip,dbname)\",\r\n          \"format\": \"time_series\",\r\n
    \         \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\":
    \"Active connections\",\r\n          \"refId\": \"C\"\r\n        },\r\n        {\r\n
    \         \"expr\": \"sum(irate(ccp_pg_stat_statements_total_calls_count{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"}[1m]))\",\r\n
    \         \"format\": \"time_series\",\r\n          \"hide\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Queries\",\r\n
    \         \"refId\": \"B\"\r\n        }\r\n      ],\r\n      \"thresholds\": [],\r\n
    \     \"timeFrom\": null,\r\n      \"timeRegions\": [],\r\n      \"timeShift\":
    null,\r\n      \"title\": \"Traffic\",\r\n      \"tooltip\": {\r\n        \"shared\":
    true,\r\n        \"sort\": 2,\r\n        \"value_type\": \"individual\"\r\n      },\r\n
    \     \"type\": \"graph\",\r\n      \"xaxis\": {\r\n        \"buckets\": null,\r\n
    \       \"mode\": \"time\",\r\n        \"name\": null,\r\n        \"show\": true,\r\n
    \       \"values\": []\r\n      },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\":
    \"short\",\r\n          \"label\": \"\",\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": \"0.001\",\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"aliasColors\":
    {},\r\n      \"bars\": false,\r\n      \"dashLength\": 10,\r\n      \"dashes\":
    false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"description\": \"Errors\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\": {},\r\n
    \         \"links\": []\r\n        },\r\n        \"overrides\": []\r\n      },\r\n
    \     \"fill\": 1,\r\n      \"fillGradient\": 5,\r\n      \"gridPos\": {\r\n        \"h\":
    7,\r\n        \"w\": 12,\r\n        \"x\": 0,\r\n        \"y\": 7\r\n      },\r\n
    \     \"hiddenSeries\": false,\r\n      \"id\": 4,\r\n      \"legend\": {\r\n
    \       \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n        \"rightSide\":
    true,\r\n        \"show\": true,\r\n        \"sideWidth\": 150,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"links\": [],\r\n      \"nullPointMode\": \"null\",\r\n
    \     \"options\": {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\":
    false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 5,\r\n
    \     \"points\": false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\":
    [],\r\n      \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum(irate(ccp_stat_database_xact_rollback{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"}[1m])
    without(pod,instance,ip))\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    true,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"Rollbacks\",\r\n          \"refId\": \"A\"\r\n        },\r\n
    \       {\r\n          \"expr\": \"sum(irate(ccp_stat_database_deadlocks{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"}[1m]))
    \ without(pod,instance,ip,dbname)\",\r\n          \"format\": \"time_series\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"Deadlock \",\r\n          \"refId\": \"D\"\r\n
    \       },\r\n        {\r\n          \"expr\": \"sum(irate(ccp_stat_database_conflicts{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"}[1m]))
    without(pod,instance,ip,dbname)\",\r\n          \"format\": \"time_series\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"Conflicts\",\r\n          \"refId\": \"B\"\r\n
    \       },\r\n        {\r\n          \"expr\": \"max(pg_exporter_last_scrape_error{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    without(pod,instance,ip,dbname)\",\r\n          \"format\": \"time_series\",\r\n
    \         \"hide\": false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"scrape error\",\r\n          \"refId\": \"C\"\r\n
    \       },\r\n        {\r\n          \"expr\": \"max(clamp_max(ccp_archive_command_status_seconds_since_last_fail{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"},1))
    without (instance,pod,ip)\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"archive error\",\r\n          \"refId\": \"E\"\r\n
    \       }\r\n      ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n
    \     \"timeRegions\": [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Errors\",\r\n
    \     \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n
    \       \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"decimals\": null,\r\n
    \         \"format\": \"short\",\r\n          \"label\": \"\",\r\n          \"logBase\":
    2,\r\n          \"max\": null,\r\n          \"min\": null,\r\n          \"show\":
    true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\":
    null,\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\":
    null,\r\n          \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\":
    {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n      }\r\n
    \   },\r\n    {\r\n      \"aliasColors\": {},\r\n      \"bars\": false,\r\n      \"dashLength\":
    10,\r\n      \"dashes\": false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\":
    {\r\n        \"defaults\": {\r\n          \"custom\": {},\r\n          \"links\":
    []\r\n        },\r\n        \"overrides\": []\r\n      },\r\n      \"fill\": 1,\r\n
    \     \"fillGradient\": 1,\r\n      \"gridPos\": {\r\n        \"h\": 7,\r\n        \"w\":
    12,\r\n        \"x\": 12,\r\n        \"y\": 7\r\n      },\r\n      \"hiddenSeries\":
    false,\r\n      \"id\": 10,\r\n      \"legend\": {\r\n        \"alignAsTable\":
    true,\r\n        \"avg\": false,\r\n        \"current\": false,\r\n        \"max\":
    false,\r\n        \"min\": false,\r\n        \"rightSide\": true,\r\n        \"show\":
    true,\r\n        \"sideWidth\": 150,\r\n        \"total\": false,\r\n        \"values\":
    false\r\n      },\r\n      \"lines\": true,\r\n      \"linewidth\": 1,\r\n      \"links\":
    [],\r\n      \"nullPointMode\": \"null\",\r\n      \"options\": {\r\n        \"alertThreshold\":
    true\r\n      },\r\n      \"percentage\": false,\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"pointradius\": 5,\r\n      \"points\": false,\r\n      \"renderer\": \"flot\",\r\n
    \     \"seriesOverrides\": [\r\n        {\r\n          \"alias\": \"/Max:/\",\r\n
    \         \"color\": \"#E02F44\",\r\n          \"nullPointMode\": \"null as zero\"\r\n
    \       },\r\n        {\r\n          \"alias\": \"/Avg:/\",\r\n          \"color\":
    \"#8AB8FF\"\r\n        }\r\n      ],\r\n      \"spaceLength\": 10,\r\n      \"stack\":
    false,\r\n      \"steppedLine\": false,\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"max(ccp_pg_stat_statements_total_mean_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    without (pod,instance,ip)\",\r\n          \"format\": \"time_series\",\r\n          \"hide\":
    false,\r\n          \"instant\": false,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\":
    1,\r\n          \"legendFormat\": \"Avg: {{exported_role}}({{dbname}})\",\r\n
    \         \"refId\": \"A\"\r\n        },\r\n        {\r\n          \"expr\": \"max(ccp_pg_stat_statements_top_max_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",role=\\\"[[role]]\\\"})
    without (pod,instance,ip,query,queryid)\",\r\n          \"format\": \"time_series\",\r\n
    \         \"hide\": false,\r\n          \"instant\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"Max:
    {{exported_role}}({{dbname}})\",\r\n          \"refId\": \"B\"\r\n        }\r\n
    \     ],\r\n      \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Query Duration\",\r\n
    \     \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n
    \       \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"decimals\": null,\r\n
    \         \"format\": \"ms\",\r\n          \"label\": null,\r\n          \"logBase\":
    2,\r\n          \"max\": null,\r\n          \"min\": \"0\",\r\n          \"show\":
    true\r\n        },\r\n        {\r\n          \"format\": \"short\",\r\n          \"label\":
    null,\r\n          \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\":
    null,\r\n          \"show\": false\r\n        }\r\n      ],\r\n      \"yaxis\":
    {\r\n        \"align\": false,\r\n        \"alignLevel\": null\r\n      }\r\n
    \   }\r\n  ],\r\n  \"refresh\": \"5m\",\r\n  \"schemaVersion\": 27,\r\n  \"style\":
    \"dark\",\r\n  \"tags\": [\r\n    \"vendor=crunchydata\"\r\n  ],\r\n  \"templating\":
    {\r\n    \"list\": [\r\n      {\r\n        \"allValue\": null,\r\n        \"current\":
    {},\r\n        \"datasource\": \"PROMETHEUS\",\r\n        \"definition\": \"label_values(pg_cluster)\",\r\n
    \       \"description\": null,\r\n        \"error\": null,\r\n        \"hide\":
    0,\r\n        \"includeAll\": false,\r\n        \"label\": null,\r\n        \"multi\":
    false,\r\n        \"name\": \"cluster\",\r\n        \"options\": [],\r\n        \"query\":
    {\r\n          \"query\": \"label_values(pg_cluster)\",\r\n          \"refId\":
    \"PROMETHEUS-cluster-Variable-Query\"\r\n        },\r\n        \"refresh\": 1,\r\n
    \       \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      },\r\n
    \     {\r\n        \"allValue\": null,\r\n        \"current\": {},\r\n        \"datasource\":
    \"PROMETHEUS\",\r\n        \"definition\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},role)\",\r\n
    \       \"description\": null,\r\n        \"error\": null,\r\n        \"hide\":
    0,\r\n        \"includeAll\": false,\r\n        \"label\": null,\r\n        \"multi\":
    false,\r\n        \"name\": \"role\",\r\n        \"options\": [],\r\n        \"query\":
    {\r\n          \"query\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},role)\",\r\n
    \         \"refId\": \"PROMETHEUS-role-Variable-Query\"\r\n        },\r\n        \"refresh\":
    1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      }\r\n
    \   ]\r\n  },\r\n  \"time\": {\r\n    \"from\": \"now-30m\",\r\n    \"to\": \"now\"\r\n
    \ },\r\n  \"timepicker\": {\r\n    \"time_options\": [\r\n      \"5m\",\r\n      \"15m\",\r\n
    \     \"1h\",\r\n      \"6h\",\r\n      \"12h\",\r\n      \"24h\",\r\n      \"2d\",\r\n
    \     \"7d\",\r\n      \"30d\"\r\n    ]\r\n  },\r\n  \"timezone\": \"browser\",\r\n
    \ \"title\": \"PostgreSQL Service Health\",\r\n  \"uid\": \"dhG1wgsMz\",\r\n  \"version\":
    1\r\n}\r\n"
  prometheus_alerts.json: "{\r\n  \"__inputs\": [\r\n    {\r\n      \"name\": \"DS_PROMETHEUS\",\r\n
    \     \"label\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"type\":
    \"datasource\",\r\n      \"pluginId\": \"prometheus\",\r\n      \"pluginName\":
    \"Prometheus\"\r\n    }\r\n  ],\r\n  \"__requires\": [\r\n    {\r\n      \"type\":
    \"grafana\",\r\n      \"id\": \"grafana\",\r\n      \"name\": \"Grafana\",\r\n
    \     \"version\": \"7.4.5\"\r\n    },\r\n    {\r\n      \"type\": \"datasource\",\r\n
    \     \"id\": \"prometheus\",\r\n      \"name\": \"Prometheus\",\r\n      \"version\":
    \"1.0.0\"\r\n    },\r\n    {\r\n      \"type\": \"panel\",\r\n      \"id\": \"stat\",\r\n
    \     \"name\": \"Stat\",\r\n      \"version\": \"\"\r\n    },\r\n    {\r\n      \"type\":
    \"panel\",\r\n      \"id\": \"table\",\r\n      \"name\": \"Table\",\r\n      \"version\":
    \"\"\r\n    }\r\n  ],\r\n  \"annotations\": {\r\n    \"list\": [\r\n      {\r\n
    \       \"builtIn\": 1,\r\n        \"datasource\": \"-- Grafana --\",\r\n        \"enable\":
    true,\r\n        \"hide\": true,\r\n        \"iconColor\": \"rgba(0, 211, 255,
    1)\",\r\n        \"name\": \"Annotations & Alerts\",\r\n        \"type\": \"dashboard\"\r\n
    \     }\r\n    ]\r\n  },\r\n  \"description\": \"Show current firing and pending
    alerts, and  severity alert counts.\",\r\n  \"editable\": false,\r\n  \"gnetId\":
    4181,\r\n  \"graphTooltip\": 0,\r\n  \"id\": null,\r\n  \"links\": [\r\n    {\r\n
    \     \"icon\": \"external link\",\r\n      \"tags\": [\r\n        \"vendor=crunchydata\"\r\n
    \     ],\r\n      \"type\": \"dashboards\"\r\n    }\r\n  ],\r\n  \"panels\": [\r\n
    \   {\r\n      \"collapsed\": false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n
    \     \"gridPos\": {\r\n        \"h\": 1,\r\n        \"w\": 24,\r\n        \"x\":
    0,\r\n        \"y\": 0\r\n      },\r\n      \"id\": 10,\r\n      \"panels\": [],\r\n
    \     \"repeat\": null,\r\n      \"title\": \"Environment Summary\",\r\n      \"type\":
    \"row\"\r\n    },\r\n    {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"color\": {\r\n            \"mode\": \"thresholds\"\r\n
    \         },\r\n          \"custom\": {},\r\n          \"mappings\": [\r\n            {\r\n
    \             \"id\": 0,\r\n              \"op\": \"=\",\r\n              \"text\":
    \"N/A\",\r\n              \"type\": 1,\r\n              \"value\": \"null\"\r\n
    \           }\r\n          ],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"semi-dark-blue\",\r\n                \"value\": null\r\n              }\r\n
    \           ]\r\n          },\r\n          \"unit\": \"none\"\r\n        },\r\n
    \       \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    2,\r\n        \"w\": 4,\r\n        \"x\": 0,\r\n        \"y\": 1\r\n      },\r\n
    \     \"id\": 6,\r\n      \"interval\": null,\r\n      \"links\": [],\r\n      \"maxDataPoints\":
    100,\r\n      \"options\": {\r\n        \"colorMode\": \"background\",\r\n        \"graphMode\":
    \"none\",\r\n        \"justifyMode\": \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n
    \       \"reduceOptions\": {\r\n          \"calcs\": [],\r\n          \"fields\":
    \"\",\r\n          \"values\": false\r\n        },\r\n        \"text\": {},\r\n
    \       \"textMode\": \"auto\"\r\n      },\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"targets\": [\r\n        {\r\n          \"expr\": \"count(count by (kubernetes_namespace)
    (pg_up))\",\r\n          \"format\": \"time_series\",\r\n          \"instant\":
    true,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"Namespaces\",\r\n          \"refId\": \"A\"\r\n
    \       }\r\n      ],\r\n      \"title\": \"Namespaces\",\r\n      \"type\": \"stat\"\r\n
    \   },\r\n    {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\": \"PROMETHEUS\",\r\n
    \     \"description\": \"\",\r\n      \"fieldConfig\": {\r\n        \"defaults\":
    {\r\n          \"color\": {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n
    \         \"custom\": {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\":
    0,\r\n              \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"thresholds\":
    {\r\n            \"mode\": \"absolute\",\r\n            \"steps\": [\r\n              {\r\n
    \               \"color\": \"semi-dark-blue\",\r\n                \"value\": null\r\n
    \             }\r\n            ]\r\n          },\r\n          \"unit\": \"none\"\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n
    \       \"h\": 2,\r\n        \"w\": 4,\r\n        \"x\": 4,\r\n        \"y\":
    1\r\n      },\r\n      \"id\": 13,\r\n      \"interval\": null,\r\n      \"links\":
    [],\r\n      \"maxDataPoints\": 100,\r\n      \"options\": {\r\n        \"colorMode\":
    \"background\",\r\n        \"graphMode\": \"none\",\r\n        \"justifyMode\":
    \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [\r\n            \"mean\"\r\n          ],\r\n          \"fields\":
    \"\",\r\n          \"values\": false\r\n        },\r\n        \"text\": {},\r\n
    \       \"textMode\": \"auto\"\r\n      },\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"targets\": [\r\n        {\r\n          \"expr\": \"count(count by (pg_cluster)
    (pg_up))\",\r\n          \"format\": \"time_series\",\r\n          \"instant\":
    true,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"PostgreSQL Clusters\",\r\n          \"refId\": \"A\"\r\n
    \       }\r\n      ],\r\n      \"title\": \"PG Clusters\",\r\n      \"type\":
    \"stat\"\r\n    },\r\n    {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"fieldConfig\": {\r\n
    \       \"defaults\": {\r\n          \"color\": {\r\n            \"mode\": \"thresholds\"\r\n
    \         },\r\n          \"custom\": {},\r\n          \"mappings\": [\r\n            {\r\n
    \             \"id\": 0,\r\n              \"op\": \"=\",\r\n              \"text\":
    \"N/A\",\r\n              \"type\": 1,\r\n              \"value\": \"null\"\r\n
    \           }\r\n          ],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"semi-dark-blue\",\r\n                \"value\": null\r\n              }\r\n
    \           ]\r\n          },\r\n          \"unit\": \"none\"\r\n        },\r\n
    \       \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    2,\r\n        \"w\": 4,\r\n        \"x\": 8,\r\n        \"y\": 1\r\n      },\r\n
    \     \"id\": 14,\r\n      \"interval\": null,\r\n      \"links\": [],\r\n      \"maxDataPoints\":
    100,\r\n      \"options\": {\r\n        \"colorMode\": \"background\",\r\n        \"graphMode\":
    \"none\",\r\n        \"justifyMode\": \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n
    \       \"reduceOptions\": {\r\n          \"calcs\": [\r\n            \"mean\"\r\n
    \         ],\r\n          \"fields\": \"\",\r\n          \"values\": false\r\n
    \       },\r\n        \"text\": {},\r\n        \"textMode\": \"auto\"\r\n      },\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\":
    \"count(pg_up)\",\r\n          \"format\": \"time_series\",\r\n          \"instant\":
    true,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"PostgreSQL Clusters\",\r\n          \"refId\": \"A\"\r\n
    \       }\r\n      ],\r\n      \"title\": \"PG Instances\",\r\n      \"type\":
    \"stat\"\r\n    },\r\n    {\r\n      \"collapsed\": false,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"gridPos\": {\r\n        \"h\": 1,\r\n        \"w\":
    24,\r\n        \"x\": 0,\r\n        \"y\": 3\r\n      },\r\n      \"id\": 11,\r\n
    \     \"panels\": [],\r\n      \"repeat\": null,\r\n      \"title\": \"Alert Summary\",\r\n
    \     \"type\": \"row\"\r\n    },\r\n    {\r\n      \"cacheTimeout\": null,\r\n
    \     \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\":
    {\r\n          \"color\": {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n
    \         \"custom\": {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\":
    0,\r\n              \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"thresholds\":
    {\r\n            \"mode\": \"absolute\",\r\n            \"steps\": [\r\n              {\r\n
    \               \"color\": \"semi-dark-red\",\r\n                \"value\": null\r\n
    \             },\r\n              {\r\n                \"color\": \"#F2495C\",\r\n
    \               \"value\": 1\r\n              },\r\n              {\r\n                \"color\":
    \"#F2495C\"\r\n              }\r\n            ]\r\n          },\r\n          \"unit\":
    \"none\"\r\n        },\r\n        \"overrides\": []\r\n      },\r\n      \"gridPos\":
    {\r\n        \"h\": 2,\r\n        \"w\": 4,\r\n        \"x\": 0,\r\n        \"y\":
    4\r\n      },\r\n      \"id\": 2,\r\n      \"interval\": null,\r\n      \"links\":
    [],\r\n      \"maxDataPoints\": 100,\r\n      \"options\": {\r\n        \"colorMode\":
    \"background\",\r\n        \"graphMode\": \"none\",\r\n        \"justifyMode\":
    \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [\r\n            \"mean\"\r\n          ],\r\n          \"fields\":
    \"\",\r\n          \"values\": false\r\n        },\r\n        \"text\": {},\r\n
    \       \"textMode\": \"auto\"\r\n      },\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"targets\": [\r\n        {\r\n          \"bucketAggs\": [\r\n            {\r\n
    \             \"id\": \"2\",\r\n              \"settings\": {\r\n                \"interval\":
    \"auto\",\r\n                \"min_doc_count\": 0,\r\n                \"trimEdges\":
    0\r\n              },\r\n              \"type\": \"date_histogram\"\r\n            }\r\n
    \         ],\r\n          \"dsType\": \"elasticsearch\",\r\n          \"expr\":
    \"sum(ALERTS{alertstate=\\\"firing\\\",severity=\\\"critical\\\"} > 0) OR on()
    vector(0)\",\r\n          \"format\": \"time_series\",\r\n          \"instant\":
    true,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 1,\r\n
    \         \"legendFormat\": \"Critical\",\r\n          \"metrics\": [\r\n            {\r\n
    \             \"field\": \"select field\",\r\n              \"id\": \"1\",\r\n
    \             \"type\": \"count\"\r\n            }\r\n          ],\r\n          \"refId\":
    \"A\"\r\n        }\r\n      ],\r\n      \"title\": \"Critical\",\r\n      \"type\":
    \"stat\"\r\n    },\r\n    {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\":
    {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\": 0,\r\n
    \             \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"thresholds\":
    {\r\n            \"mode\": \"absolute\",\r\n            \"steps\": [\r\n              {\r\n
    \               \"color\": \"semi-dark-orange\",\r\n                \"value\":
    null\r\n              }\r\n            ]\r\n          },\r\n          \"unit\":
    \"none\"\r\n        },\r\n        \"overrides\": []\r\n      },\r\n      \"gridPos\":
    {\r\n        \"h\": 2,\r\n        \"w\": 4,\r\n        \"x\": 4,\r\n        \"y\":
    4\r\n      },\r\n      \"id\": 5,\r\n      \"interval\": null,\r\n      \"links\":
    [],\r\n      \"maxDataPoints\": 100,\r\n      \"options\": {\r\n        \"colorMode\":
    \"background\",\r\n        \"graphMode\": \"none\",\r\n        \"justifyMode\":
    \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [],\r\n          \"fields\": \"\",\r\n          \"values\":
    false\r\n        },\r\n        \"text\": {},\r\n        \"textMode\": \"auto\"\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum(ALERTS{alertstate=\\\"firing\\\",severity=\\\"warning\\\"}
    > 0) OR on() vector(0)\",\r\n          \"format\": \"time_series\",\r\n          \"instant\":
    true,\r\n          \"interval\": \"\",\r\n          \"intervalFactor\": 2,\r\n
    \         \"legendFormat\": \"\",\r\n          \"refId\": \"A\"\r\n        }\r\n
    \     ],\r\n      \"title\": \"Warning\",\r\n      \"type\": \"stat\"\r\n    },\r\n
    \   {\r\n      \"cacheTimeout\": null,\r\n      \"datasource\": \"PROMETHEUS\",\r\n
    \     \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\": {\r\n
    \           \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {},\r\n          \"mappings\": [\r\n            {\r\n              \"id\": 0,\r\n
    \             \"op\": \"=\",\r\n              \"text\": \"N/A\",\r\n              \"type\":
    1,\r\n              \"value\": \"null\"\r\n            }\r\n          ],\r\n          \"thresholds\":
    {\r\n            \"mode\": \"absolute\",\r\n            \"steps\": [\r\n              {\r\n
    \               \"color\": \"#299c46\",\r\n                \"value\": null\r\n
    \             }\r\n            ]\r\n          },\r\n          \"unit\": \"none\"\r\n
    \       },\r\n        \"overrides\": []\r\n      },\r\n      \"gridPos\": {\r\n
    \       \"h\": 2,\r\n        \"w\": 4,\r\n        \"x\": 8,\r\n        \"y\":
    4\r\n      },\r\n      \"id\": 9,\r\n      \"interval\": null,\r\n      \"links\":
    [],\r\n      \"maxDataPoints\": 100,\r\n      \"options\": {\r\n        \"colorMode\":
    \"background\",\r\n        \"graphMode\": \"none\",\r\n        \"justifyMode\":
    \"auto\",\r\n        \"orientation\": \"horizontal\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [\r\n            \"mean\"\r\n          ],\r\n          \"fields\":
    \"\",\r\n          \"values\": false\r\n        },\r\n        \"text\": {},\r\n
    \       \"textMode\": \"auto\"\r\n      },\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"targets\": [\r\n        {\r\n          \"expr\": \"sum(ALERTS{alertstate=\\\"firing\\\",severity=\\\"info\\\"}
    > 0) OR on() vector(0)\",\r\n          \"format\": \"time_series\",\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 2,\r\n          \"legendFormat\": \"\",\r\n
    \         \"refId\": \"A\"\r\n        }\r\n      ],\r\n      \"title\": \"Info\",\r\n
    \     \"type\": \"stat\"\r\n    },\r\n    {\r\n      \"collapsed\": false,\r\n
    \     \"datasource\": \"PROMETHEUS\",\r\n      \"gridPos\": {\r\n        \"h\":
    1,\r\n        \"w\": 24,\r\n        \"x\": 0,\r\n        \"y\": 6\r\n      },\r\n
    \     \"id\": 12,\r\n      \"panels\": [],\r\n      \"repeat\": null,\r\n      \"title\":
    \"Alerts\",\r\n      \"type\": \"row\"\r\n    },\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\":
    {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {\r\n            \"align\": null,\r\n            \"displayMode\": \"auto\",\r\n
    \           \"filterable\": true\r\n          },\r\n          \"decimals\": 2,\r\n
    \         \"displayName\": \"\",\r\n          \"mappings\": [\r\n            {\r\n
    \             \"from\": \"\",\r\n              \"id\": 1,\r\n              \"text\":
    \"\",\r\n              \"to\": \"\",\r\n              \"type\": 1,\r\n              \"value\":
    \"\"\r\n            }\r\n          ],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"green\",\r\n                \"value\": null\r\n              },\r\n              {\r\n
    \               \"color\": \"blue\",\r\n                \"value\": 100\r\n              },\r\n
    \             {\r\n                \"color\": \"#EAB839\",\r\n                \"value\":
    200\r\n              },\r\n              {\r\n                \"color\": \"red\",\r\n
    \               \"value\": 300\r\n              }\r\n            ]\r\n          },\r\n
    \         \"unit\": \"short\"\r\n        },\r\n        \"overrides\": [\r\n          {\r\n
    \           \"matcher\": {\r\n              \"id\": \"byName\",\r\n              \"options\":
    \"severity_num\"\r\n            },\r\n            \"properties\": [\r\n              {\r\n
    \               \"id\": \"custom.displayMode\",\r\n                \"value\":
    \"color-background\"\r\n              },\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 124\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"Time\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    170\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"severity\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 119\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"alertname\"\r\n            },\r\n
    \           \"properties\": [\r\n              {\r\n                \"id\": \"custom.width\",\r\n
    \               \"value\": 206\r\n              }\r\n            ]\r\n          },\r\n
    \         {\r\n            \"matcher\": {\r\n              \"id\": \"byName\",\r\n
    \             \"options\": \"alertstate\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    128\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n
    \     \"gridPos\": {\r\n        \"h\": 5,\r\n        \"w\": 24,\r\n        \"x\":
    0,\r\n        \"y\": 7\r\n      },\r\n      \"id\": 1,\r\n      \"links\": [],\r\n
    \     \"options\": {\r\n        \"showHeader\": true,\r\n        \"sortBy\": []\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"ALERTS{alertstate='firing'} > 0\",\r\n          \"format\":
    \"table\",\r\n          \"instant\": true,\r\n          \"interval\": \"2s\",\r\n
    \         \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n          \"refId\":
    \"A\"\r\n        }\r\n      ],\r\n      \"title\": \"Firing\",\r\n      \"transformations\":
    [\r\n        {\r\n          \"id\": \"merge\",\r\n          \"options\": {\r\n
    \           \"reducers\": []\r\n          }\r\n        },\r\n        {\r\n          \"id\":
    \"organize\",\r\n          \"options\": {\r\n            \"excludeByName\": {\r\n
    \             \"Value\": true,\r\n              \"__name__\": true,\r\n              \"alertstate\":
    false,\r\n              \"deployment\": false,\r\n              \"exp_type\":
    true,\r\n              \"fs_type\": true,\r\n              \"instance\": true,\r\n
    \             \"job\": true,\r\n              \"kubernetes_namespace\": true,\r\n
    \             \"mount_point\": true,\r\n              \"server\": true,\r\n              \"service\":
    true,\r\n              \"severity_num\": false\r\n            },\r\n            \"indexByName\":
    {\r\n              \"Time\": 0,\r\n              \"Value\": 16,\r\n              \"__name__\":
    3,\r\n              \"alertname\": 4,\r\n              \"alertstate\": 5,\r\n
    \             \"deployment\": 7,\r\n              \"exp_type\": 9,\r\n              \"instance\":
    10,\r\n              \"ip\": 11,\r\n              \"job\": 12,\r\n              \"kubernetes_namespace\":
    13,\r\n              \"pg_cluster\": 6,\r\n              \"pod\": 8,\r\n              \"role\":
    14,\r\n              \"service\": 15,\r\n              \"severity\": 2,\r\n              \"severity_num\":
    1\r\n            },\r\n            \"renameByName\": {\r\n              \"Time\":
    \"\",\r\n              \"__name__\": \"\",\r\n              \"severity\": \"\",\r\n
    \             \"severity_num\": \"\"\r\n            }\r\n          }\r\n        }\r\n
    \     ],\r\n      \"type\": \"table\"\r\n    },\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"color\":
    {\r\n            \"mode\": \"thresholds\"\r\n          },\r\n          \"custom\":
    {\r\n            \"align\": null,\r\n            \"filterable\": true\r\n          },\r\n
    \         \"decimals\": 2,\r\n          \"displayName\": \"\",\r\n          \"mappings\":
    [],\r\n          \"thresholds\": {\r\n            \"mode\": \"absolute\",\r\n
    \           \"steps\": [\r\n              {\r\n                \"color\": \"green\",\r\n
    \               \"value\": null\r\n              },\r\n              {\r\n                \"color\":
    \"red\",\r\n                \"value\": 80\r\n              }\r\n            ]\r\n
    \         },\r\n          \"unit\": \"short\"\r\n        },\r\n        \"overrides\":
    [\r\n          {\r\n            \"matcher\": {\r\n              \"id\": \"byRegexp\",\r\n
    \             \"options\": \"/(instance|__name__|Time|alertstate|job|type|Value)/\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"unit\",\r\n                \"value\": \"short\"\r\n              },\r\n              {\r\n
    \               \"id\": \"decimals\",\r\n                \"value\": 2\r\n              },\r\n
    \             {\r\n                \"id\": \"custom.align\",\r\n                \"value\":
    null\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"Time\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": null\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"severity_num\"\r\n            },\r\n
    \           \"properties\": [\r\n              {\r\n                \"id\": \"custom.width\",\r\n
    \               \"value\": 126\r\n              }\r\n            ]\r\n          },\r\n
    \         {\r\n            \"matcher\": {\r\n              \"id\": \"byName\",\r\n
    \             \"options\": \"severity\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    115\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"alertname\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 207\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"alertstate\"\r\n            },\r\n
    \           \"properties\": [\r\n              {\r\n                \"id\": \"custom.width\",\r\n
    \               \"value\": 131\r\n              }\r\n            ]\r\n          }\r\n
    \       ]\r\n      },\r\n      \"gridPos\": {\r\n        \"h\": 7,\r\n        \"w\":
    24,\r\n        \"x\": 0,\r\n        \"y\": 12\r\n      },\r\n      \"id\": 3,\r\n
    \     \"links\": [],\r\n      \"options\": {\r\n        \"showHeader\": true,\r\n
    \       \"sortBy\": []\r\n      },\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"targets\": [\r\n        {\r\n          \"expr\": \"ALERTS{alertstate=\\\"pending\\\"}\",\r\n
    \         \"format\": \"table\",\r\n          \"instant\": false,\r\n          \"interval\":
    \"\",\r\n          \"intervalFactor\": 1,\r\n          \"legendFormat\": \"\",\r\n
    \         \"refId\": \"A\"\r\n        }\r\n      ],\r\n      \"title\": \"Alerts
    (1 week)\",\r\n      \"transformations\": [\r\n        {\r\n          \"id\":
    \"organize\",\r\n          \"options\": {\r\n            \"excludeByName\": {\r\n
    \             \"Value\": true,\r\n              \"__name__\": true,\r\n              \"exp_type\":
    true,\r\n              \"instance\": true,\r\n              \"job\": true,\r\n
    \             \"kubernetes_namespace\": true,\r\n              \"service\": true\r\n
    \           },\r\n            \"indexByName\": {\r\n              \"Time\": 0,\r\n
    \             \"Value\": 16,\r\n              \"__name__\": 3,\r\n              \"alertname\":
    4,\r\n              \"alertstate\": 5,\r\n              \"deployment\": 7,\r\n
    \             \"exp_type\": 8,\r\n              \"instance\": 9,\r\n              \"ip\":
    11,\r\n              \"job\": 12,\r\n              \"kubernetes_namespace\": 13,\r\n
    \             \"pg_cluster\": 6,\r\n              \"pod\": 10,\r\n              \"role\":
    14,\r\n              \"service\": 15,\r\n              \"severity\": 2,\r\n              \"severity_num\":
    1\r\n            },\r\n            \"renameByName\": {}\r\n          }\r\n        }\r\n
    \     ],\r\n      \"type\": \"table\"\r\n    }\r\n  ],\r\n  \"refresh\": \"15m\",\r\n
    \ \"schemaVersion\": 27,\r\n  \"style\": \"dark\",\r\n  \"tags\": [\r\n    \"vendor=crunchydata\"\r\n
    \ ],\r\n  \"templating\": {\r\n    \"list\": []\r\n  },\r\n  \"time\": {\r\n    \"from\":
    \"now-30m\",\r\n    \"to\": \"now\"\r\n  },\r\n  \"timepicker\": {\r\n    \"time_options\":
    [\r\n      \"5m\",\r\n      \"15m\",\r\n      \"1h\",\r\n      \"6h\",\r\n      \"12h\",\r\n
    \     \"24h\",\r\n      \"2d\",\r\n      \"7d\",\r\n      \"30d\"\r\n    ]\r\n
    \ },\r\n  \"timezone\": \"browser\",\r\n  \"title\": \"Prometheus Alerts\",\r\n
    \ \"uid\": \"lwxXsZsMk\",\r\n  \"version\": 1\r\n}\r\n"
  query_statistics.json: "{\r\n  \"__inputs\": [\r\n    {\r\n      \"name\": \"DS_PROMETHEUS\",\r\n
    \     \"label\": \"PROMETHEUS\",\r\n      \"description\": \"\",\r\n      \"type\":
    \"datasource\",\r\n      \"pluginId\": \"prometheus\",\r\n      \"pluginName\":
    \"Prometheus\"\r\n    }\r\n  ],\r\n  \"__requires\": [\r\n    {\r\n      \"type\":
    \"grafana\",\r\n      \"id\": \"grafana\",\r\n      \"name\": \"Grafana\",\r\n
    \     \"version\": \"7.4.5\"\r\n    },\r\n    {\r\n      \"type\": \"panel\",\r\n
    \     \"id\": \"graph\",\r\n      \"name\": \"Graph\",\r\n      \"version\": \"\"\r\n
    \   },\r\n    {\r\n      \"type\": \"datasource\",\r\n      \"id\": \"prometheus\",\r\n
    \     \"name\": \"Prometheus\",\r\n      \"version\": \"1.0.0\"\r\n    },\r\n
    \   {\r\n      \"type\": \"panel\",\r\n      \"id\": \"stat\",\r\n      \"name\":
    \"Stat\",\r\n      \"version\": \"\"\r\n    },\r\n    {\r\n      \"type\": \"panel\",\r\n
    \     \"id\": \"table\",\r\n      \"name\": \"Table\",\r\n      \"version\": \"\"\r\n
    \   }\r\n  ],\r\n  \"annotations\": {\r\n    \"list\": [\r\n      {\r\n        \"builtIn\":
    1,\r\n        \"datasource\": \"-- Grafana --\",\r\n        \"enable\": true,\r\n
    \       \"hide\": true,\r\n        \"iconColor\": \"rgba(0, 211, 255, 1)\",\r\n
    \       \"name\": \"Annotations & Alerts\",\r\n        \"type\": \"dashboard\"\r\n
    \     }\r\n    ]\r\n  },\r\n  \"description\": \"\",\r\n  \"editable\": false,\r\n
    \ \"gnetId\": null,\r\n  \"graphTooltip\": 0,\r\n  \"id\": null,\r\n  \"iteration\":
    1624501789811,\r\n  \"links\": [\r\n    {\r\n      \"icon\": \"external link\",\r\n
    \     \"tags\": [\r\n        \"vendor=crunchydata\"\r\n      ],\r\n      \"type\":
    \"dashboards\"\r\n    }\r\n  ],\r\n  \"panels\": [\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"mappings\": [],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"blue\",\r\n                \"value\": null\r\n              }\r\n            ]\r\n
    \         },\r\n          \"unit\": \"none\"\r\n        },\r\n        \"overrides\":
    []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\": 3,\r\n        \"w\":
    5,\r\n        \"x\": 0,\r\n        \"y\": 0\r\n      },\r\n      \"id\": 8,\r\n
    \     \"options\": {\r\n        \"colorMode\": \"value\",\r\n        \"graphMode\":
    \"area\",\r\n        \"justifyMode\": \"auto\",\r\n        \"orientation\": \"auto\",\r\n
    \       \"reduceOptions\": {\r\n          \"calcs\": [],\r\n          \"fields\":
    \"\",\r\n          \"values\": false\r\n        },\r\n        \"text\": {},\r\n
    \       \"textMode\": \"value\"\r\n      },\r\n      \"pluginVersion\": \"7.4.5\",\r\n
    \     \"targets\": [\r\n        {\r\n          \"expr\": \"sum(sum_over_time(ccp_pg_stat_statements_total_calls_count{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment,pod,dbname,exported_role)\",\r\n          \"instant\":
    false,\r\n          \"interval\": \"\",\r\n          \"legendFormat\": \"\",\r\n
    \         \"refId\": \"A\"\r\n        }\r\n      ],\r\n      \"timeFrom\": null,\r\n
    \     \"timeShift\": null,\r\n      \"title\": \"Queries Executed\",\r\n      \"type\":
    \"stat\"\r\n    },\r\n    {\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\":
    {\r\n        \"defaults\": {\r\n          \"custom\": {},\r\n          \"mappings\":
    [],\r\n          \"thresholds\": {\r\n            \"mode\": \"absolute\",\r\n
    \           \"steps\": [\r\n              {\r\n                \"color\": \"blue\",\r\n
    \               \"value\": null\r\n              }\r\n            ]\r\n          },\r\n
    \         \"unit\": \"ms\"\r\n        },\r\n        \"overrides\": []\r\n      },\r\n
    \     \"gridPos\": {\r\n        \"h\": 3,\r\n        \"w\": 6,\r\n        \"x\":
    5,\r\n        \"y\": 0\r\n      },\r\n      \"id\": 9,\r\n      \"options\": {\r\n
    \       \"colorMode\": \"value\",\r\n        \"graphMode\": \"area\",\r\n        \"justifyMode\":
    \"auto\",\r\n        \"orientation\": \"auto\",\r\n        \"reduceOptions\":
    {\r\n          \"calcs\": [],\r\n          \"fields\": \"\",\r\n          \"values\":
    false\r\n        },\r\n        \"text\": {},\r\n        \"textMode\": \"value\"\r\n
    \     },\r\n      \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n
    \         \"expr\": \"sum(sum_over_time(ccp_pg_stat_statements_total_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment,pod,dbname,exported_role)\",\r\n          \"format\":
    \"time_series\",\r\n          \"instant\": false,\r\n          \"interval\": \"\",\r\n
    \         \"legendFormat\": \"\",\r\n          \"refId\": \"A\"\r\n        }\r\n
    \     ],\r\n      \"timeFrom\": null,\r\n      \"timeShift\": null,\r\n      \"title\":
    \"Query Runtime \",\r\n      \"type\": \"stat\"\r\n    },\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"mappings\": [],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"blue\",\r\n                \"value\": null\r\n              }\r\n            ]\r\n
    \         },\r\n          \"unit\": \"ms\"\r\n        },\r\n        \"overrides\":
    []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\": 3,\r\n        \"w\":
    6,\r\n        \"x\": 11,\r\n        \"y\": 0\r\n      },\r\n      \"id\": 10,\r\n
    \     \"options\": {\r\n        \"colorMode\": \"value\",\r\n        \"graphMode\":
    \"area\",\r\n        \"justifyMode\": \"auto\",\r\n        \"orientation\": \"auto\",\r\n
    \       \"reduceOptions\": {\r\n          \"calcs\": [\r\n            \"mean\"\r\n
    \         ],\r\n          \"fields\": \"\",\r\n          \"values\": false\r\n
    \       },\r\n        \"text\": {},\r\n        \"textMode\": \"value\"\r\n      },\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\":
    \"avg(sum_over_time(ccp_pg_stat_statements_total_mean_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment,pod,dbname,exported_role)\",\r\n          \"format\":
    \"time_series\",\r\n          \"instant\": false,\r\n          \"interval\": \"\",\r\n
    \         \"legendFormat\": \"\",\r\n          \"refId\": \"A\"\r\n        }\r\n
    \     ],\r\n      \"timeFrom\": null,\r\n      \"timeShift\": null,\r\n      \"title\":
    \"Query Mean Runtime\",\r\n      \"type\": \"stat\"\r\n    },\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {},\r\n          \"mappings\": [],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"blue\",\r\n                \"value\": null\r\n              }\r\n            ]\r\n
    \         },\r\n          \"unit\": \"none\"\r\n        },\r\n        \"overrides\":
    []\r\n      },\r\n      \"gridPos\": {\r\n        \"h\": 3,\r\n        \"w\":
    6,\r\n        \"x\": 17,\r\n        \"y\": 0\r\n      },\r\n      \"id\": 11,\r\n
    \     \"options\": {\r\n        \"colorMode\": \"value\",\r\n        \"graphMode\":
    \"area\",\r\n        \"justifyMode\": \"auto\",\r\n        \"orientation\": \"auto\",\r\n
    \       \"reduceOptions\": {\r\n          \"calcs\": [\r\n            \"mean\"\r\n
    \         ],\r\n          \"fields\": \"\",\r\n          \"values\": false\r\n
    \       },\r\n        \"text\": {},\r\n        \"textMode\": \"value\"\r\n      },\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\":
    \"sum(sum_over_time(ccp_pg_stat_statements_total_row_count{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment, pod,dbname,exported_role)\",\r\n          \"format\":
    \"time_series\",\r\n          \"instant\": false,\r\n          \"interval\": \"\",\r\n
    \         \"legendFormat\": \"\",\r\n          \"refId\": \"A\"\r\n        }\r\n
    \     ],\r\n      \"timeFrom\": null,\r\n      \"timeShift\": null,\r\n      \"title\":
    \"Rows Retrieved or Affected\",\r\n      \"type\": \"stat\"\r\n    },\r\n    {\r\n
    \     \"aliasColors\": {},\r\n      \"bars\": false,\r\n      \"dashLength\":
    10,\r\n      \"dashes\": false,\r\n      \"datasource\": \"PROMETHEUS\",\r\n      \"fieldConfig\":
    {\r\n        \"defaults\": {\r\n          \"custom\": {}\r\n        },\r\n        \"overrides\":
    []\r\n      },\r\n      \"fill\": 1,\r\n      \"fillGradient\": 0,\r\n      \"gridPos\":
    {\r\n        \"h\": 7,\r\n        \"w\": 23,\r\n        \"x\": 0,\r\n        \"y\":
    3\r\n      },\r\n      \"hiddenSeries\": false,\r\n      \"id\": 2,\r\n      \"legend\":
    {\r\n        \"alignAsTable\": true,\r\n        \"avg\": false,\r\n        \"current\":
    false,\r\n        \"max\": false,\r\n        \"min\": false,\r\n        \"rightSide\":
    true,\r\n        \"show\": true,\r\n        \"sideWidth\": null,\r\n        \"total\":
    false,\r\n        \"values\": false\r\n      },\r\n      \"lines\": true,\r\n
    \     \"linewidth\": 1,\r\n      \"nullPointMode\": \"null\",\r\n      \"options\":
    {\r\n        \"alertThreshold\": true\r\n      },\r\n      \"percentage\": false,\r\n
    \     \"pluginVersion\": \"7.4.5\",\r\n      \"pointradius\": 2,\r\n      \"points\":
    false,\r\n      \"renderer\": \"flot\",\r\n      \"seriesOverrides\": [],\r\n
    \     \"spaceLength\": 10,\r\n      \"stack\": false,\r\n      \"steppedLine\":
    false,\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum(irate(ccp_pg_stat_statements_total_calls_count{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment, pod)\",\r\n          \"instant\": false,\r\n
    \         \"interval\": \"\",\r\n          \"legendFormat\": \"db: {{dbname}},
    user: {{exported_role}}\",\r\n          \"refId\": \"A\"\r\n        }\r\n      ],\r\n
    \     \"thresholds\": [],\r\n      \"timeFrom\": null,\r\n      \"timeRegions\":
    [],\r\n      \"timeShift\": null,\r\n      \"title\": \"Query Executions\",\r\n
    \     \"tooltip\": {\r\n        \"shared\": true,\r\n        \"sort\": 0,\r\n
    \       \"value_type\": \"individual\"\r\n      },\r\n      \"type\": \"graph\",\r\n
    \     \"xaxis\": {\r\n        \"buckets\": null,\r\n        \"mode\": \"time\",\r\n
    \       \"name\": null,\r\n        \"show\": true,\r\n        \"values\": []\r\n
    \     },\r\n      \"yaxes\": [\r\n        {\r\n          \"format\": \"short\",\r\n
    \         \"label\": null,\r\n          \"logBase\": 1,\r\n          \"max\":
    null,\r\n          \"min\": null,\r\n          \"show\": true\r\n        },\r\n
    \       {\r\n          \"format\": \"short\",\r\n          \"label\": null,\r\n
    \         \"logBase\": 1,\r\n          \"max\": null,\r\n          \"min\": null,\r\n
    \         \"show\": true\r\n        }\r\n      ],\r\n      \"yaxis\": {\r\n        \"align\":
    false,\r\n        \"alignLevel\": null\r\n      }\r\n    },\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {\r\n            \"align\": null,\r\n            \"displayMode\": \"auto\",\r\n
    \           \"filterable\": false\r\n          },\r\n          \"mappings\": [],\r\n
    \         \"thresholds\": {\r\n            \"mode\": \"percentage\",\r\n            \"steps\":
    [\r\n              {\r\n                \"color\": \"green\",\r\n                \"value\":
    null\r\n              },\r\n              {\r\n                \"color\": \"red\",\r\n
    \               \"value\": 80\r\n              }\r\n            ]\r\n          }\r\n
    \       },\r\n        \"overrides\": [\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"Runtime\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"unit\",\r\n                \"value\": \"ms\"\r\n              },\r\n              {\r\n
    \               \"id\": \"custom.displayMode\",\r\n                \"value\":
    \"color-background\"\r\n              },\r\n              {\r\n                \"id\":
    \"thresholds\",\r\n                \"value\": {\r\n                  \"mode\":
    \"percentage\",\r\n                  \"steps\": [\r\n                    {\r\n
    \                     \"color\": \"green\",\r\n                      \"value\":
    null\r\n                    },\r\n                    {\r\n                      \"color\":
    \"red\",\r\n                      \"value\": 90\r\n                    }\r\n                  ]\r\n
    \               }\r\n              },\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 169\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"queryid\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    189\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"query\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 308\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"role\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    82\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"dbname\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 184\r\n              }\r\n            ]\r\n
    \         }\r\n        ]\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    8,\r\n        \"w\": 23,\r\n        \"x\": 0,\r\n        \"y\": 10\r\n      },\r\n
    \     \"id\": 4,\r\n      \"options\": {\r\n        \"showHeader\": true,\r\n
    \       \"sortBy\": [\r\n          {\r\n            \"desc\": true,\r\n            \"displayName\":
    \"Runtime\"\r\n          }\r\n        ]\r\n      },\r\n      \"pluginVersion\":
    \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"avg(avg_over_time(ccp_pg_stat_statements_top_mean_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment,pod)\",\r\n          \"format\": \"table\",\r\n
    \         \"hide\": false,\r\n          \"instant\": true,\r\n          \"interval\":
    \"\",\r\n          \"legendFormat\": \"\",\r\n          \"refId\": \"B\"\r\n        }\r\n
    \     ],\r\n      \"timeFrom\": null,\r\n      \"timeShift\": null,\r\n      \"title\":
    \"Query Mean Runtime (Top N)\",\r\n      \"transformations\": [\r\n        {\r\n
    \         \"id\": \"organize\",\r\n          \"options\": {\r\n            \"excludeByName\":
    {\r\n              \"Time\": true,\r\n              \"__name__\": true,\r\n              \"exp_type\":
    true,\r\n              \"job\": true,\r\n              \"kubernetes_namespace\":
    true,\r\n              \"queryid\": false,\r\n              \"role\": false,\r\n
    \             \"server\": true\r\n            },\r\n            \"indexByName\":
    {\r\n              \"Time\": 6,\r\n              \"Value\": 5,\r\n              \"__name__\":
    7,\r\n              \"dbname\": 0,\r\n              \"exp_type\": 8,\r\n              \"instance\":
    2,\r\n              \"job\": 9,\r\n              \"query\": 3,\r\n              \"queryid\":
    4,\r\n              \"role\": 1\r\n            },\r\n            \"renameByName\":
    {\r\n              \"Value\": \"Runtime\"\r\n            }\r\n          }\r\n
    \       }\r\n      ],\r\n      \"type\": \"table\"\r\n    },\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {\r\n            \"align\": null,\r\n            \"displayMode\": \"auto\",\r\n
    \           \"filterable\": false\r\n          },\r\n          \"mappings\": [],\r\n
    \         \"thresholds\": {\r\n            \"mode\": \"percentage\",\r\n            \"steps\":
    [\r\n              {\r\n                \"color\": \"green\",\r\n                \"value\":
    null\r\n              },\r\n              {\r\n                \"color\": \"red\",\r\n
    \               \"value\": 80\r\n              }\r\n            ]\r\n          }\r\n
    \       },\r\n        \"overrides\": [\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"Runtime\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"unit\",\r\n                \"value\": \"ms\"\r\n              },\r\n              {\r\n
    \               \"id\": \"custom.displayMode\",\r\n                \"value\":
    \"color-background\"\r\n              },\r\n              {\r\n                \"id\":
    \"thresholds\",\r\n                \"value\": {\r\n                  \"mode\":
    \"percentage\",\r\n                  \"steps\": [\r\n                    {\r\n
    \                     \"color\": \"green\",\r\n                      \"value\":
    null\r\n                    },\r\n                    {\r\n                      \"color\":
    \"red\",\r\n                      \"value\": 90\r\n                    }\r\n                  ]\r\n
    \               }\r\n              },\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 170\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"dbname\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    184\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"role\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 82\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"query\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    310\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"queryid\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 186\r\n              }\r\n            ]\r\n
    \         }\r\n        ]\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    8,\r\n        \"w\": 23,\r\n        \"x\": 0,\r\n        \"y\": 18\r\n      },\r\n
    \     \"id\": 6,\r\n      \"options\": {\r\n        \"showHeader\": true,\r\n
    \       \"sortBy\": [\r\n          {\r\n            \"desc\": true,\r\n            \"displayName\":
    \"Runtime\"\r\n          }\r\n        ]\r\n      },\r\n      \"pluginVersion\":
    \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"max(max_over_time(ccp_pg_stat_statements_top_max_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment,pod)\",\r\n          \"format\": \"table\",\r\n
    \         \"hide\": false,\r\n          \"instant\": true,\r\n          \"interval\":
    \"\",\r\n          \"legendFormat\": \"\",\r\n          \"refId\": \"B\"\r\n        }\r\n
    \     ],\r\n      \"timeFrom\": null,\r\n      \"timeShift\": null,\r\n      \"title\":
    \"Query Max Runtime (Top N)\",\r\n      \"transformations\": [\r\n        {\r\n
    \         \"id\": \"organize\",\r\n          \"options\": {\r\n            \"excludeByName\":
    {\r\n              \"Time\": true,\r\n              \"__name__\": true,\r\n              \"exp_type\":
    true,\r\n              \"job\": true,\r\n              \"kubernetes_namespace\":
    true,\r\n              \"queryid\": false,\r\n              \"role\": false,\r\n
    \             \"server\": true\r\n            },\r\n            \"indexByName\":
    {\r\n              \"Time\": 6,\r\n              \"Value\": 5,\r\n              \"__name__\":
    7,\r\n              \"dbname\": 0,\r\n              \"exp_type\": 8,\r\n              \"instance\":
    2,\r\n              \"job\": 9,\r\n              \"query\": 3,\r\n              \"queryid\":
    4,\r\n              \"role\": 1\r\n            },\r\n            \"renameByName\":
    {\r\n              \"Value\": \"Runtime\"\r\n            }\r\n          }\r\n
    \       }\r\n      ],\r\n      \"type\": \"table\"\r\n    },\r\n    {\r\n      \"datasource\":
    \"PROMETHEUS\",\r\n      \"fieldConfig\": {\r\n        \"defaults\": {\r\n          \"custom\":
    {\r\n            \"align\": null,\r\n            \"filterable\": false\r\n          },\r\n
    \         \"mappings\": [],\r\n          \"thresholds\": {\r\n            \"mode\":
    \"absolute\",\r\n            \"steps\": [\r\n              {\r\n                \"color\":
    \"green\",\r\n                \"value\": null\r\n              },\r\n              {\r\n
    \               \"color\": \"red\",\r\n                \"value\": 80\r\n              }\r\n
    \           ]\r\n          }\r\n        },\r\n        \"overrides\": [\r\n          {\r\n
    \           \"matcher\": {\r\n              \"id\": \"byName\",\r\n              \"options\":
    \"Runtime\"\r\n            },\r\n            \"properties\": [\r\n              {\r\n
    \               \"id\": \"unit\",\r\n                \"value\": \"ms\"\r\n              },\r\n
    \             {\r\n                \"id\": \"custom.displayMode\",\r\n                \"value\":
    \"color-background\"\r\n              },\r\n              {\r\n                \"id\":
    \"thresholds\",\r\n                \"value\": {\r\n                  \"mode\":
    \"percentage\",\r\n                  \"steps\": [\r\n                    {\r\n
    \                     \"color\": \"green\",\r\n                      \"value\":
    null\r\n                    },\r\n                    {\r\n                      \"color\":
    \"red\",\r\n                      \"value\": 90\r\n                    }\r\n                  ]\r\n
    \               }\r\n              },\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 172\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"dbname\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    182\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"role\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 84\r\n              }\r\n            ]\r\n
    \         },\r\n          {\r\n            \"matcher\": {\r\n              \"id\":
    \"byName\",\r\n              \"options\": \"query\"\r\n            },\r\n            \"properties\":
    [\r\n              {\r\n                \"id\": \"custom.width\",\r\n                \"value\":
    309\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            \"matcher\":
    {\r\n              \"id\": \"byName\",\r\n              \"options\": \"queryid\"\r\n
    \           },\r\n            \"properties\": [\r\n              {\r\n                \"id\":
    \"custom.width\",\r\n                \"value\": 189\r\n              }\r\n            ]\r\n
    \         }\r\n        ]\r\n      },\r\n      \"gridPos\": {\r\n        \"h\":
    8,\r\n        \"w\": 23,\r\n        \"x\": 0,\r\n        \"y\": 26\r\n      },\r\n
    \     \"id\": 5,\r\n      \"options\": {\r\n        \"showHeader\": true,\r\n
    \       \"sortBy\": [\r\n          {\r\n            \"desc\": true,\r\n            \"displayName\":
    \"Runtime\"\r\n          }\r\n        ]\r\n      },\r\n      \"pluginVersion\":
    \"7.4.5\",\r\n      \"targets\": [\r\n        {\r\n          \"expr\": \"sum(irate(ccp_pg_stat_statements_top_total_exec_time_ms{pg_cluster=\\\"[[cluster]]\\\",
    role=\\\"[[role]]\\\",dbname=~\\\"[[dbname]]\\\",exported_role=~\\\"[[dbuser]]\\\"}[$__range]))
    without(instance, ip, deployment,pod)\",\r\n          \"format\": \"table\",\r\n
    \         \"instant\": true,\r\n          \"interval\": \"\",\r\n          \"legendFormat\":
    \"\",\r\n          \"refId\": \"A\"\r\n        }\r\n      ],\r\n      \"timeFrom\":
    null,\r\n      \"timeShift\": null,\r\n      \"title\": \"Query Total Runtime
    (Top N)\",\r\n      \"transformations\": [\r\n        {\r\n          \"id\": \"organize\",\r\n
    \         \"options\": {\r\n            \"excludeByName\": {\r\n              \"Time\":
    true,\r\n              \"__name__\": true,\r\n              \"exp_type\": true,\r\n
    \             \"job\": true,\r\n              \"kubernetes_namespace\": true,\r\n
    \             \"queryid\": false,\r\n              \"role\": false,\r\n              \"server\":
    true\r\n            },\r\n            \"indexByName\": {\r\n              \"Time\":
    6,\r\n              \"Value\": 5,\r\n              \"__name__\": 7,\r\n              \"dbname\":
    0,\r\n              \"exp_type\": 8,\r\n              \"instance\": 2,\r\n              \"job\":
    9,\r\n              \"query\": 3,\r\n              \"queryid\": 4,\r\n              \"role\":
    1\r\n            },\r\n            \"renameByName\": {\r\n              \"Value\":
    \"Runtime\"\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      \"type\":
    \"table\"\r\n    }\r\n  ],\r\n  \"refresh\": \"15m\",\r\n  \"schemaVersion\":
    27,\r\n  \"style\": \"dark\",\r\n  \"tags\": [\r\n    \"vendor=crunchydata\"\r\n
    \ ],\r\n  \"templating\": {\r\n    \"list\": [\r\n      {\r\n        \"allValue\":
    null,\r\n        \"current\": {},\r\n        \"datasource\": \"PROMETHEUS\",\r\n
    \       \"definition\": \"label_values(pg_cluster)\",\r\n        \"description\":
    null,\r\n        \"error\": null,\r\n        \"hide\": 0,\r\n        \"includeAll\":
    false,\r\n        \"label\": \"cluster\",\r\n        \"multi\": false,\r\n        \"name\":
    \"cluster\",\r\n        \"options\": [],\r\n        \"query\": {\r\n          \"query\":
    \"label_values(pg_cluster)\",\r\n          \"refId\": \"StandardVariableQuery\"\r\n
    \       },\r\n        \"refresh\": 1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\":
    false,\r\n        \"sort\": 1,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\":
    [],\r\n        \"tagsQuery\": \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\":
    false\r\n      },\r\n      {\r\n        \"allValue\": null,\r\n        \"current\":
    {},\r\n        \"datasource\": \"PROMETHEUS\",\r\n        \"definition\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},role)\",\r\n
    \       \"description\": null,\r\n        \"error\": null,\r\n        \"hide\":
    0,\r\n        \"includeAll\": false,\r\n        \"label\": \"service\",\r\n        \"multi\":
    false,\r\n        \"name\": \"role\",\r\n        \"options\": [],\r\n        \"query\":
    {\r\n          \"query\": \"label_values({pg_cluster=\\\"[[cluster]]\\\"},role)\",\r\n
    \         \"refId\": \"StandardVariableQuery\"\r\n        },\r\n        \"refresh\":
    2,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    1,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      },\r\n
    \     {\r\n        \"allValue\": null,\r\n        \"current\": {},\r\n        \"datasource\":
    \"PROMETHEUS\",\r\n        \"definition\": \"label_values(ccp_database_size_bytes{pg_cluster=\\\"[[cluster]]\\\"},dbname)\",\r\n
    \       \"description\": null,\r\n        \"error\": null,\r\n        \"hide\":
    0,\r\n        \"includeAll\": true,\r\n        \"label\": \"dbname\",\r\n        \"multi\":
    false,\r\n        \"name\": \"dbname\",\r\n        \"options\": [],\r\n        \"query\":
    {\r\n          \"query\": \"label_values(ccp_database_size_bytes{pg_cluster=\\\"[[cluster]]\\\"},dbname)\",\r\n
    \         \"refId\": \"StandardVariableQuery\"\r\n        },\r\n        \"refresh\":
    1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\": false,\r\n        \"sort\":
    0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\": [],\r\n        \"tagsQuery\":
    \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\": false\r\n      },\r\n
    \     {\r\n        \"allValue\": null,\r\n        \"current\": {},\r\n        \"datasource\":
    \"PROMETHEUS\",\r\n        \"definition\": \"label_values(ccp_pg_stat_statements_total_calls_count{pg_cluster=\\\"[[cluster]]\\\",
    dbname=~\\\"[[dbname]]\\\"},exported_role)\",\r\n        \"description\": null,\r\n
    \       \"error\": null,\r\n        \"hide\": 0,\r\n        \"includeAll\": true,\r\n
    \       \"label\": \"dbuser\",\r\n        \"multi\": false,\r\n        \"name\":
    \"dbuser\",\r\n        \"options\": [],\r\n        \"query\": {\r\n          \"query\":
    \"label_values(ccp_pg_stat_statements_total_calls_count{pg_cluster=\\\"[[cluster]]\\\",
    dbname=~\\\"[[dbname]]\\\"},exported_role)\",\r\n          \"refId\": \"StandardVariableQuery\"\r\n
    \       },\r\n        \"refresh\": 1,\r\n        \"regex\": \"\",\r\n        \"skipUrlSync\":
    false,\r\n        \"sort\": 0,\r\n        \"tagValuesQuery\": \"\",\r\n        \"tags\":
    [],\r\n        \"tagsQuery\": \"\",\r\n        \"type\": \"query\",\r\n        \"useTags\":
    false\r\n      }\r\n    ]\r\n  },\r\n  \"time\": {\r\n    \"from\": \"now-1h\",\r\n
    \   \"to\": \"now\"\r\n  },\r\n  \"timepicker\": {\r\n    \"time_options\": [\r\n
    \     \"5m\",\r\n      \"15m\",\r\n      \"1h\",\r\n      \"6h\",\r\n      \"12h\",\r\n
    \     \"24h\",\r\n      \"2d\",\r\n      \"7d\",\r\n      \"30d\"\r\n    ]\r\n
    \ },\r\n  \"timezone\": \"browser\",\r\n  \"title\": \"Query Statistics\",\r\n
    \ \"uid\": \"ZKoTOHDGk\",\r\n  \"version\": 1\r\n}\r\n"
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: a0ec71-tools
---
apiVersion: v1
data:
  crunchy_grafana_datasource.yml: |
    ###
    #
    # Copyright 2017-2022 Crunchy Data Solutions, Inc. All Rights Reserved.
    #
    ###

    # config file version
    apiVersion: 1

    # list of datasources to insert/update depending
    # what's available in the database
    datasources:
      # <string, required> name of the datasource. Required
    - name: PROMETHEUS
      # <string, required> datasource type. Required
      type: prometheus
      # <string, required> access mode. proxy or direct (Server or Browser in the UI). Required
      access: proxy
      # <int> org id. will default to orgId 1 if not specified
      orgId: 1
      # <string> url
      url: http://$PROM_HOST:$PROM_PORT
      # <string> database password, if used
      password:
      # <string> database user, if used
      user:
      # <string> database name, if used
      database:
      # <bool> enable/disable basic auth
      basicAuth:
      # <string> basic auth username
      basicAuthUser:
      # <string> basic auth password
      basicAuthPassword:
      # <bool> enable/disable with credentials headers
      withCredentials:
      # <bool> mark as default datasource. Max one per org
      isDefault: true
      version: 1
      # <bool> allow users to edit datasources from the UI.
      editable: false
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    vendor: crunchydata
  name: grafana-datasources
  namespace: a0ec71-tools
---
apiVersion: v1
data:
  password: YWRtaW4=
  username: YWRtaW4=
kind: Secret
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    vendor: crunchydata
  name: grafana-secret
  namespace: a0ec71-tools
type: Opaque
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    name: crunchy-alertmanager
    vendor: crunchydata
  name: crunchy-alertmanager
  namespace: a0ec71-tools
spec:
  ports:
  - name: alertmanager
    port: 9093
  selector:
    name: crunchy-alertmanager
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    name: crunchy-grafana
    vendor: crunchydata
  name: crunchy-grafana
  namespace: a0ec71-tools
spec:
  ports:
  - name: grafana
    port: 3000
  - name: grafana-proxy
    port: 9091
    protocol: TCP
    targetPort: grafana-proxy
  selector:
    name: crunchy-grafana
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
    name: crunchy-prometheus
    vendor: crunchydata
  name: crunchy-prometheus
  namespace: a0ec71-tools
spec:
  ports:
  - name: prometheus
    port: 9090
  selector:
    name: crunchy-prometheus
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/name: pgo-monitoring
    vendor: crunchydata
  name: alertmanagerdata
  namespace: a0ec71-tools
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/name: pgo-monitoring
    vendor: crunchydata
  name: grafanadata
  namespace: a0ec71-tools
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/name: pgo-monitoring
    vendor: crunchydata
  name: prometheusdata
  namespace: a0ec71-tools
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: netapp-block-standard
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
  name: crunchy-alertmanager
  namespace: a0ec71-tools
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres-operator-monitoring
      name: crunchy-alertmanager
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres-operator-monitoring
        name: crunchy-alertmanager
    spec:
      containers:
      - args:
        - --config.file=/etc/alertmanager/alertmanager.yml
        - --storage.path=/alertmanager
        - --log.level=info
        - --cluster.advertise-address=0.0.0.0:9093
        image: prom/alertmanager:v0.22.2
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /-/healthy
            port: 9093
            scheme: HTTP
          initialDelaySeconds: 25
          periodSeconds: 20
          successThreshold: 1
          timeoutSeconds: 1
        name: alertmanager
        ports:
        - containerPort: 9093
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /-/ready
            port: 9093
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/alertmanager
          name: alertmanagerconf
        - mountPath: /alertmanager
          name: alertmanagerdata
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: alertmanager
      serviceAccountName: alertmanager
      terminationGracePeriodSeconds: 30
      volumes:
      - name: alertmanagerdata
        persistentVolumeClaim:
          claimName: alertmanagerdata
      - configMap:
          defaultMode: 420
          name: alertmanager-config
        name: alertmanagerconf
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
  name: crunchy-grafana
  namespace: a0ec71-tools
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres-operator-monitoring
      name: crunchy-grafana
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres-operator-monitoring
        name: crunchy-grafana
    spec:
      containers:
      - env:
        - name: GF_PATHS_DATA
          value: /data/grafana/data
        - name: GF_SECURITY_ADMIN_USER__FILE
          value: /conf/admin/username
        - name: GF_SECURITY_ADMIN_PASSWORD__FILE
          value: /conf/admin/password
        - name: PROM_HOST
          value: crunchy-prometheus
        - name: PROM_PORT
          value: "9090"
        image: grafana/grafana:7.4.5
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /api/health
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 25
          periodSeconds: 20
          successThreshold: 1
          timeoutSeconds: 1
        name: grafana
        ports:
        - containerPort: 3000
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /api/health
            port: 3000
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/grafana/
          name: grafana-config
        - mountPath: /data
          name: grafanadata
        - mountPath: /conf/admin
          name: grafana-secret
        - mountPath: /etc/grafana/provisioning/datasources
          name: grafana-datasources
        - mountPath: /etc/grafana/provisioning/dashboards
          name: grafana-dashboards
      - args:
        - --provider=openshift
        - --pass-basic-auth=false
        - --https-address=
        - --http-address=:9091
        - --email-domain=*
        - --upstream=http://localhost:3000
        - --cookie-secret=asdf
        - --openshift-service-account=grafana
        - --skip-auth-regex=^/metrics
        - '--openshift-sar={"namespace": "a0ec71-tools", "resource": "services", "verb":
          "get"}'
        image: image-registry.openshift-image-registry.svc:5000/openshift/oauth-proxy:v4.4
        imagePullPolicy: IfNotPresent
        name: grafana-proxy
        ports:
        - containerPort: 9091
          name: grafana-proxy
          protocol: TCP
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: grafana
      serviceAccountName: grafana
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          name: grafana-config
        name: grafana-config
      - name: grafanadata
        persistentVolumeClaim:
          claimName: grafanadata
      - name: grafana-secret
        secret:
          defaultMode: 420
          secretName: grafana-secret
      - configMap:
          defaultMode: 420
          name: grafana-datasources
        name: grafana-datasources
      - configMap:
          defaultMode: 420
          name: grafana-dashboards
        name: grafana-dashboards
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
  labels:
    app.kubernetes.io/name: postgres-operator-monitoring
  name: crunchy-prometheus
  namespace: a0ec71-tools
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres-operator-monitoring
      name: crunchy-prometheus
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app.kubernetes.io/name: postgres-operator-monitoring
        name: crunchy-prometheus
    spec:
      containers:
      - image: prom/prometheus:v2.27.1
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /-/healthy
            port: 9090
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 20
          successThreshold: 1
          timeoutSeconds: 1
        name: prometheus
        ports:
        - containerPort: 9090
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /-/ready
            port: 9090
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/prometheus
          name: prometheusconf
        - mountPath: /prometheus
          name: prometheusdata
        - mountPath: /etc/prometheus/alert-rules.d
          name: alertmanagerrules
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: prometheus-sa
      serviceAccountName: prometheus-sa
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          name: crunchy-prometheus
        name: prometheusconf
      - name: prometheusdata
        persistentVolumeClaim:
          claimName: prometheusdata
      - configMap:
          defaultMode: 420
          name: alertmanager-rules-config
        name: alertmanagerrules
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-grafana-route
  namespace: a0ec71-tools
spec:
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          network.openshift.io/policy-group: ingress
  podSelector:
    matchLabels:
      name: crunchy-grafana
  policyTypes:
  - Ingress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-grafana-to-prometheus
  namespace: a0ec71-tools
spec:
  ingress:
  - from:
    - podSelector:
        matchLabels:
          name: crunchy-grafana
    ports:
    - port: 9090
      protocol: TCP
  podSelector:
    matchLabels:
      name: crunchy-prometheus
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-prometheus-to-alertmanager
  namespace: a0ec71-tools
spec:
  ingress:
  - from:
    - podSelector:
        matchLabels:
          name: crunchy-prometheus
    ports:
    - port: 9093
      protocol: TCP
  podSelector:
    matchLabels:
      name: crunchy-alertmanager
---
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  name: crunchy-grafana
  namespace: a0ec71-tools
spec:
  port:
    targetPort: grafana-proxy
  tls:
    termination: edge
  to:
    kind: Service
    name: crunchy-grafana
    weight: 100
  wildcardPolicy: None
