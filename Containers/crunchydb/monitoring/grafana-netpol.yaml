---
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: allow-grafana-route
spec:
  podSelector:
    matchLabels:
      name: crunchy-grafana
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              network.openshift.io/policy-group: ingress
  policyTypes:
    - Ingress
---
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: allow-grafana-to-prometheus
spec:
  podSelector:
    matchLabels:
      name: crunchy-prometheus
  ingress:
    - from:
        - podSelector:
            matchLabels:
              name: crunchy-grafana
      ports:
        - protocol: TCP
          port: 9090
---
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: allow-prometheus-to-alertmanager
spec:
  podSelector:
    matchLabels:
      name: crunchy-alertmanager
  ingress:
    - from:
        - podSelector:
            matchLabels:
              name: crunchy-prometheus
      ports:
        - protocol: TCP
          port: 9093
---
