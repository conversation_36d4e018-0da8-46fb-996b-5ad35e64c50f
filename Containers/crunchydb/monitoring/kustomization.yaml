---
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: a0ec71-tools

resources:
- "grafana-config.yaml"
- "grafana-route.yaml"
- "grafana-netpol.yaml"
- "github.com/CrunchyData/postgres-operator-examples/kustomize/monitoring"

patches:
- target:
    version: v1
    kind: Deployment
    labelSelector: "app.kubernetes.io/name=postgres-operator-monitoring"
  patch: |-
    - op: remove
      path: "/spec/template/spec/securityContext/fsGroup"
- target:
    version: v1
    kind: PersistentVolumeClaim
    name: prometheusdata
  patch: |-
    - op: add
      path: "/spec/storageClassName"
      value: netapp-block-standard
- target:
    version: v1
    group: rbac.authorization.k8s.io
    kind: ClusterRole
    labelSelector: "vendor=crunchydata"
  patch: |-
    - op: replace
      path: "/kind"
      value: "Role"
- target:
    version: v1
    group: rbac.authorization.k8s.io
    kind: ClusterRoleBinding
    labelSelector: "vendor=crunchydata"
  patch: |-
    - op: replace
      path: "/kind"
      value: "RoleBinding"
    - op: replace
      path: "/roleRef/kind"
      value: "Role"
- target:
    version: v1
    kind: Service
    name: crunchy-grafana
  patch: |-
    - op: add
      path: "/spec/ports/-"
      value:
        name: grafana-proxy
        protocol: TCP
        port: 9091
        targetPort: grafana-proxy
- target:
    version: v1
    kind: ServiceAccount
    name: grafana
  patch: |-
    - op: add
      path: "/metadata/annotations/serviceaccounts.openshift.io~1oauth-redirectreference.primary"
      value: >-
        {"kind":"OAuthRedirectReference","apiVersion":"v1","reference":{"kind":"Route","name":"crunchy-grafana"}}
- path: grafana-oauth.yaml
