kind: Template
apiVersion: template.openshift.io/v1
labels:
  app: "${NAME}"
  template: "${NAME}-dc-template"
metadata:
  name: "metabase"
objects:
  - kind: Secret
    apiVersion: v1
    metadata:
      app: "${NAME}"
      name: "${NAME}-secret"
      annotations:
        template.openshift.io/expose-password: "{.data['database-password']}"
    stringData:
      admin-email: "${ADMIN_EMAIL}"
      admin-password: "${ADMIN_PASSWORD}"
      admin-username: "${NAME}-admin"
      embedding-secret-key: "${EMBEDDING_SECRET_KEY}"
  - kind: Secret
    apiVersion: v1
    metadata:
      app: "${NAME}"
      name: "postgresql-${NAME}-secret"
      annotations:
        template.openshift.io/expose-database_name: "{.data[database-name]}"
        template.openshift.io/expose-password: "{.data[database-password]}"
        template.openshift.io/expose-username: "{.data['database-user]}"
    stringData:
      database-name: ${NAME}
      database-password: ${DATABASE_PASSWORD}
      database-user: ${NAME}
  - kind: Service
    apiVersion: v1
    metadata:
      name: "${NAME}"
    spec:
      ports:
        - name: 3000-tcp
          protocol: TCP
          port: 3000
          targetPort: 3000
      selector:
        app: "${NAME}"
      type: ClusterIP
      sessionAffinity: None
  - kind: Service
    apiVersion: v1
    metadata:
      name: "postgresql-${NAME}"
    spec:
      ports:
        - name: postgresql
          port: 5432
          protocol: TCP
          targetPort: 5432
      selector:
        name: "postgresql-${NAME}"
      sessionAffinity: None
      type: ClusterIP
  - kind: Route
    apiVersion: v1
    metadata:
      name: "${NAME}"
    spec:
      host: "${NAME}-${NAMESPACE}-${TARGET_NS}.apps.silver.devops.gov.bc.ca"
      tls:
        insecureEdgeTerminationPolicy: Redirect
        termination: edge
      path: "/"
      to:
        kind: Service
        name: "${NAME}"
      port:
        targetPort: 3000-tcp
      wildcardPolicy: None
  - apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
      name: "postgresql-${NAME}-data"
      annotations:
        volume.beta.kubernetes.io/storage-class: netapp-file-standard
    spec:
      accessModes:
        - ReadWriteMany
      resources:
        requests:
          storage: "${PVC_SIZE}"
      storageClassName: netapp-file-standard
      volumeMode: Filesystem
  - kind: DeploymentConfig
    apiVersion: v1
    metadata:
      name: "postgresql-${NAME}"
      labels:
        app: metabase
        app.kubernetes.io/part-of: metabase
    spec:
      replicas: 1
      strategy:
        type: Recreate
      selector:
        app: "${NAME}"
        deploymentconfig: "postgresql-${NAME}"
        name: "postgresql-${NAME}"
      template:
        metadata:
          name: "postgresql-${NAME}"
          labels:
            app: "${NAME}"
            deploymentconfig: "postgresql-${NAME}"
            name: "postgresql-${NAME}"
        spec:
          containers:
            - env:
                - name: POSTGRESQL_USER
                  valueFrom:
                    secretKeyRef:
                      key: database-user
                      name: "postgresql-${NAME}-secret"
                - name: POSTGRESQL_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      key: database-password
                      name: "postgresql-${NAME}-secret"
                - name: POSTGRESQL_DATABASE
                  valueFrom:
                    secretKeyRef:
                      key: database-name
                      name: "postgresql-${NAME}-secret"
              image: image-registry.openshift-image-registry.svc:5000/openshift/postgresql:latest
              imagePullPolicy: IfNotPresent
              livenessProbe:
                exec:
                  command:
                    - /usr/libexec/check-container
                    - "--live"
                failureThreshold: 3
                initialDelaySeconds: 120
                periodSeconds: 10
                successThreshold: 1
                timeoutSeconds: 10
              name: postgresql
              ports:
                - containerPort: 5432
                  protocol: TCP
              readinessProbe:
                exec:
                  command:
                    - /usr/libexec/check-container
                failureThreshold: 3
                initialDelaySeconds: 5
                periodSeconds: 10
                successThreshold: 1
                timeoutSeconds: 1
              resources:
                limits:
                  memory: 512Mi
              securityContext:
                capabilities: {}
                privileged: false
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
              volumeMounts:
                - mountPath: /var/lib/pgsql/data
                  name: "postgresql-${NAME}-data"
          dnsPolicy: ClusterFirst
          restartPolicy: Always
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
          volumes:
            - name: "postgresql-${NAME}-data"
              persistentVolumeClaim:
                claimName: "postgresql-${NAME}-data"
      test: false
      triggers:
        - imageChangeParams:
            automatic: true
            containerNames:
              - postgresql
            from:
              kind: ImageStreamTag
              name: "postgresql:latest"
              namespace: openshift
          type: ImageChange
        - type: ConfigChange
  - kind: DeploymentConfig
    apiVersion: v1
    metadata:
      name: "${NAME}"
      labels:
        app: metabase
        app.kubernetes.io/part-of: metabase
        template: metabase-dc-template
    spec:
      replicas: 1
      strategy:
        activeDeadlineSeconds: 21600
        recreateParams:
          timeoutSeconds: 600
        resources: {}
        type: Recreate
      selector:
        app: "${NAME}"
        deploymentconfig: "${NAME}"
      template:
        metadata:
          name: "${NAME}"
          labels:
            app: "${NAME}"
            deploymentconfig: "${NAME}"
        spec:
          containers:
            - env:
                - name: MB_DB_TYPE
                  value: postgres
                - name: MB_DB_HOST
                  value: postgresql-${NAME}
                - name: MB_DB_DBNAME
                  valueFrom:
                    secretKeyRef:
                      name: "postgresql-${NAME}-secret"
                      key: database-name
                - name: MB_DB_PORT
                  value: "5432"
                - name: MB_DB_USER
                  valueFrom:
                    secretKeyRef:
                      name: "postgresql-${NAME}-secret"
                      key: database-user
                - name: MB_DB_PASS
                  valueFrom:
                    secretKeyRef:
                      name: "postgresql-${NAME}-secret"
                      key: database-password
                - name: MB_JETTY_HOST
                  value: 0.0.0.0
                - name: MB_PASSWORD_COMPLEXITY
                  value: strong
              image: artifacts.developer.gov.bc.ca/docker-remote/metabase/metabase:latest
              imagePullPolicy: Always
              livenessProbe:
                failureThreshold: 3
                httpGet:
                  path: "/"
                  port: 3000
                initialDelaySeconds: 120
                periodSeconds: 20
                timeoutSeconds: 10
              name: metabase
              ports:
                - containerPort: 3000
                  protocol: TCP
              readinessProbe:
                failureThreshold: 3
                httpGet:
                  path: "/"
                  port: 3000
                initialDelaySeconds: 120
                periodSeconds: 20
                timeoutSeconds: 10
              resources:
                requests:
                  cpu: "${CPU_REQUEST}"
                  memory: "${MEMORY_REQUEST}"
                limits:
                  cpu: "${CPU_LIMIT}"
                  memory: "${MEMORY_LIMIT}"
              terminationMessagePath: "/dev/termination-log"
              terminationMessagePolicy: File
          dnsPolicy: ClusterFirst
          restartPolicy: Always
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
      test: false
      triggers:
        - type: ConfigChange
parameters:
  - name: NAME
    displayName: Name
    description: The name assigned to all of the objects defined in this template.
    required: true
    value: metabase
  - name: NAMESPACE
    description: Namespace reference (i.e. '9f0fbe') for Metabase installation
    displayName: Namespace
    required: true
  - name: TARGET_NS
    description: Target Area reference (i.e. 'dev, test, prod or tools')
    displayName: Namespace target
    required: true
  - name: CPU_REQUEST
    description: Minimal CPU needed to run
    displayName: CPU Request
    value: 100m
  - name: CPU_LIMIT
    description: Maximum CPU allowed to use
    displayName: CPU Limit
    value: 500m
  - name: MEMORY_REQUEST
    description: Minimal amount of memory needed to run
    displayName: Memory Request
    value: 768Mi
  - name: MEMORY_LIMIT
    description: Maximum amount of memory allowed to use
    displayName: Memory Limit
    value: 1.0Gi
  - name: PVC_SIZE
    description: Amount of disk space needed for persistence
    displayName: PVC Size
    required: true
    value: 1Gi
  - name: ADMIN_EMAIL
    displayName: Admin Email
    description: The email associated with the metabase admin user
    required: true
  - name: ADMIN_PASSWORD
    displayName: Admin Password
    description:
      The password for the metabase admin user. Requires 2 upper, 2 lower,
      1 special, 1 numeric chars and minimum 8 char length
    generate: expression
    from: "[a-zA-Z0-9]{8}!@#$%^&_[a-zA-Z0-9]{8}"
    required: true
  - name: DATABASE_PASSWORD
    displayName: Admin Password
    description:
      The password for the postgres admin user. Requires 2 upper, 2 lower,
      1 special, 1 numeric chars and minimum 8 char length
    generate: expression
    from: "[a-zA-Z0-9]{8}!@#$%^&_[a-zA-Z0-9]{8}"
    required: true
  - name: EMBEDDING_SECRET_KEY
    displayName: Embedding Secret Key
    description: A key that is used to embed metabase screens from within a website
    generate: expression
    from: "[a-zA-Z0-9]{16}"
    required: true
