kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: schemaspy-deployment-template
  annotations:
    description: SchemaSpy deployment template.
    tags: schema-spy
objects:
  - kind: Service
    apiVersion: v1
    metadata:
      name: '${NAME}'
      annotations:
        description: Exposes and load balances the application pods
    spec:
      ports:
        - name: 8080-tcp
          protocol: TCP
          port: 8080
          targetPort: 8080
      selector:
        name: '${NAME}'
  - kind: Route
    apiVersion: v1
    metadata:
      name: '${NAME}'
    spec:
      host: '${APPLICATION_DOMAIN}'
      tls:
        insecureEdgeTerminationPolicy: Redirect
        termination: edge
      to:
        kind: Service
        name: '${NAME}'
  - kind: DeploymentConfig
    apiVersion: v1
    metadata:
      name: '${NAME}'
      annotations:
        description: Defines how to deploy the container.
    spec:
      strategy:
        type: Rolling
        rollingParams:
          updatePeriodSeconds: 1
          intervalSeconds: 1
          timeoutSeconds: 600
          maxUnavailable: 25%
          maxSurge: 25%
      triggers:
        - type: ConfigChange
        - type: ImageChange
          imageChangeParams:
            automatic: true
            containerNames:
              - '${NAME}'
          from:
            kind: ImageStreamTag
            namespace: b1d40d-tools
            name: 'schemaspy:latest'
      replicas: 1
      selector:
        name: '${NAME}'
      template:
        metadata:
          name: '${NAME}'
          labels:
            name: '${NAME}'
        spec:
          containers:
            - name: '${NAME}'
              image: 'artifacts.developer.gov.bc.ca/docker-remote/schemaspy/schemaspy:latest'
              ports:
                - containerPort: 8080
                  protocol: TCP
              env:
                - name: DATABASE_HOST
                  value: '${DATABASE_HOST}'
                - name: DATABASE_NAME
                  value: '${DATABASE_NAME}'
                - name: DATABASE_USER
                  valueFrom:
                    secretKeyRef:
                      name: '${DATABASE_DEPLOYMENT_NAME}'
                      key: database-user
                - name: DATABASE_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: '${DATABASE_DEPLOYMENT_NAME}'
                      key: database-password
              readinessProbe:
                timeoutSeconds: 300
                initialDelaySeconds: 60
                httpGet:
                  path: /
                  port: 8080
              livenessProbe:
                timeoutSeconds: 300
                initialDelaySeconds: 300
                httpGet:
                  path: /
                  port: 8080
              resources:
                limits:
                  cpu: '${CPU_LIMIT}'
                  memory: '${MEMORY_LIMIT}'
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
              imagePullPolicy: Always
          restartPolicy: Always
          terminationGracePeriodSeconds: 30
          dnsPolicy: ClusterFirst
          securityContext: {}
          imagePullSecrets:
            - name: artifacts-pull-default-vltnyk
          schedulerName: default-scheduler
parameters:
  - name: NAME
    displayName: Name
    description: The name assigned to all objects defined in this template.
    value: schemaspy
    required: true
  - name: CPU_LIMIT
    displayName: CPU Limit
    description: Maximum amount of CPU the container can use.
    value: '0.05'
    required: true
  - name: MEMORY_LIMIT
    displayName: Memory Limit
    description: Maximum amount of memory the container can use.
    value: 1Gi
    required: true
  - name: APPLICATION_DOMAIN
    displayName: Application Hostname
    description: >-
      The exposed hostname that will route to the service, if left blank a value
      will be defaulted.
  - name: DATABASE_HOST
    displayName: >-
      The host name (or ip address) for the database server; override to match
      your environment.  In the case of Oracle databases this must be a
      <hostname|ipaddress>:<port> pair even if using the default Oracle port; a
      requirement of the Oracle Thin SchemaSpy process.
    value: postgresql
    required: true
  - name: DATABASE_NAME
    displayName: Database Name
    description: >-
      Name of the deployed database (see environment variables in deployed
      database).
    value: restoration
    required: true
  - name: TAG_NAME
    displayName: Environment TAG name
    description: 'The TAG name for this environment, e.g., dev, test, prod'
    value: dev
    required: true
  - name: DATABASE_DEPLOYMENT_NAME
    displayName: Database Deployment Name
    description: >-
      The name associated to the database deployment resources.  In particular,
      this is used to wrie up the credentials associated to the database.
    value: biohub-platform-db-postgresql-dev-deploy
    required: true
