# FaunaLogic Project Makefile
# Main orchestration makefile for building entire project

.PHONY: help all clean setup build test deploy dev prod logs status
.DEFAULT_GOAL := help

# Colors for output
RED    := \033[31m
GREEN  := \033[32m
YELLOW := \033[33m
BLUE   := \033[34m
RESET  := \033[0m

# Project components
COMPONENTS := database frontend api auth

help: ## Show this help message
	@echo "$(GREEN)FaunaLogic Project Build System$(RESET)"
	@echo ""
	@echo "$(YELLOW)Available targets:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Component-specific targets:$(RESET)"
	@echo "  $(BLUE)database-*$(RESET)      Run database-specific commands"
	@echo "  $(BLUE)frontend-*$(RESET)      Run frontend-specific commands"  
	@echo "  $(BLUE)api-*$(RESET)           Run api-specific commands"
	@echo "  $(BLUE)auth-*$(RESET)          Run auth-specific commands"

all: build ## Build all components
	@echo "$(GREEN)Building entire project...$(RESET)"

build: database-build auth-build api-build frontend-build ## Build all components
	@echo "$(GREEN)All components built successfully$(RESET)"

setup: database-setup auth-setup api-setup frontend-local-setup ## Set up entire project for development
	@echo "$(GREEN)Project setup complete$(RESET)"

dev: setup ## Alias for setup (development environment)

test: database-test auth-test api-test frontend-test ## Run tests for all components
	@echo "$(GREEN)All tests completed$(RESET)"

clean: database-clean auth-clean api-clean frontend-clean ## Clean all build artifacts
	@echo "$(GREEN)All components cleaned$(RESET)"

logs: database-logs auth-logs api-logs frontend-logs ## Show logs from all components

status: database-status auth-status api-status frontend-health ## Show status of all components

# Database component targets
database-%: ## Forward commands to database Makefile
	@if [ -f database/Makefile ]; then \
		echo "$(YELLOW)Running database target: $*$(RESET)"; \
		$(MAKE) -C database $*; \
	else \
		echo "$(RED)Database Makefile not found$(RESET)"; \
		exit 1; \
	fi

# Frontend component targets
frontend-%: ## Forward commands to frontend Makefile
	@if [ -f frontend/Makefile ]; then \
		echo "$(YELLOW)Running frontend target: $*$(RESET)"; \
		$(MAKE) -C frontend $*; \
	else \
		echo "$(RED)Frontend Makefile not found$(RESET)"; \
		exit 1; \
	fi

# API component targets
api-%: ## Forward commands to api Makefile
	@if [ -f api/Makefile ]; then \
		echo "$(YELLOW)Running api target: $*$(RESET)"; \
		$(MAKE) -C api $*; \
	else \
		echo "$(RED)API Makefile not found$(RESET)"; \
		exit 1; \
	fi

# Auth component targets
auth-%: ## Forward commands to auth Makefile
	@if [ -f auth/Makefile ]; then \
		echo "$(YELLOW)Running auth target: $*$(RESET)"; \
		$(MAKE) -C auth $*; \
	else \
		echo "$(RED)Auth Makefile not found$(RESET)"; \
		exit 1; \
	fi

# Production deployment
prod-deploy: database-prod-deploy ## Deploy entire project to production
	@echo "$(GREEN)Production deployment complete$(RESET)"

# Development commands
up: database-up auth-up api-docker-up frontend-up ## Start all development services
	@echo "$(GREEN)All services started$(RESET)"

down: database-down auth-down api-docker-down frontend-down ## Stop all services
	@echo "$(GREEN)All services stopped$(RESET)"

restart: down up ## Restart all services

# Migration commands
migrate: database-migrate ## Run all migrations

# Backup and restore
backup: database-backup ## Create backups for all components

restore: database-restore ## Restore from backups

# Show component status
check-components: ## Show which components have Makefiles
	@echo "$(YELLOW)Component status:$(RESET)"
	@for component in $(COMPONENTS); do \
		if [ -f $$component/Makefile ]; then \
			echo "  $(GREEN)✓$(RESET) $$component - Makefile found"; \
		else \
			echo "  $(RED)✗$(RESET) $$component - Makefile missing"; \
		fi; \
	done

# Auth-specific commands
auth-health: auth-up ## Check health of auth services
	@echo "$(YELLOW)Checking auth service health...$(RESET)"

import-realm: auth-import-realm ## Import Keycloak realm configuration

export-realm: auth-export-realm ## Export Keycloak realm configuration

reset-auth: auth-reset-admin ## Reset Keycloak admin password

# API-specific convenience commands
api-dev: api-docker-up ## Start API in development mode with Docker
	@echo "$(GREEN)API development server started$(RESET)"
	@echo "$(YELLOW)API available at: http://localhost:8000$(RESET)"
	@echo "$(YELLOW)API docs at: http://localhost:8000/docs$(RESET)"

api-test-endpoint: ## Test API health endpoint
	@echo "$(YELLOW)Testing API health endpoint...$(RESET)"
	@curl -f http://localhost:8000/health 2>/dev/null && echo "$(GREEN)✓ API health check passed$(RESET)" || echo "$(RED)✗ API health check failed$(RESET)"

api-test-map: ## Test API map endpoint with BC bounding box
	@echo "$(YELLOW)Testing API map endpoint...$(RESET)"
	@curl -f "http://localhost:8000/api/v1/map/data?min_lng=-139.0&min_lat=48.0&max_lng=-114.0&max_lat=60.0&limit=10" 2>/dev/null && echo "$(GREEN)✓ API map endpoint accessible$(RESET)" || echo "$(RED)✗ API map endpoint failed$(RESET)"

api-logs: api-docker-logs ## Show API container logs

api-shell: api-docker-shell ## Open shell in API container

api-restart: api-docker-down api-docker-up ## Restart API service

# Full stack development commands
dev-stack: database-up auth-up api-docker-up frontend-up ## Start full development stack
	@echo "$(GREEN)Full development stack started$(RESET)"
	@echo "$(YELLOW)Services available:$(RESET)"
	@echo "  Database: localhost:5432"
	@echo "  Auth (Keycloak): http://localhost:8080"
	@echo "  API: http://localhost:8000"
	@echo "  API Docs: http://localhost:8000/docs"
	@echo "  Frontend: http://localhost:3000"
	@echo "  Redis: localhost:6380"

stack-status: ## Check status of all services
	@echo "$(YELLOW)=== FaunaLogic Stack Status ===$(RESET)"
	@make database-status 2>/dev/null || echo "$(RED)Database: Not running$(RESET)"
	@make auth-status 2>/dev/null || echo "$(RED)Auth: Not running$(RESET)"
	@make api-status 2>/dev/null || echo "$(RED)API: Not running$(RESET)"
	@make frontend-health 2>/dev/null || echo "$(RED)Frontend: Not running$(RESET)"

# Frontend-specific convenience commands
frontend-dev: frontend-up-dev ## Start frontend in development mode with hot reload
	@echo "$(GREEN)Frontend development server started$(RESET)"
	@echo "$(YELLOW)Frontend available at: http://localhost:3000$(RESET)"

frontend-build-docker: frontend-docker-build ## Build frontend Docker image
	@echo "$(GREEN)Frontend Docker image built$(RESET)"

frontend-test-endpoint: ## Test frontend health endpoint
	@echo "$(YELLOW)Testing frontend health endpoint...$(RESET)"
	@curl -f http://localhost:3000/health 2>/dev/null && echo "$(GREEN)✓ Frontend health check passed$(RESET)" || echo "$(RED)✗ Frontend health check failed$(RESET)"

stack-test: ## Test all service endpoints
	@echo "$(YELLOW)=== Testing All Service Endpoints ===$(RESET)"
	@make api-test-endpoint
	@make api-test-map
	@make frontend-test-endpoint