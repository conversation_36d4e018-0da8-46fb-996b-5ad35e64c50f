# FaunaLogic Spatial Wildlife Data Management System

A comprehensive monorepo for spatial wildlife data management with PostgreSQL/PostGIS backend, API services, and modern frontend interfaces.

## 🏗️ Architecture

```
faunalogic/
├── database/          # PostgreSQL + PostGIS database service
├── api/              # FastAPI REST API service  
├── frontend/         # Vue.js + TypeScript web application
├── auth/            # Keycloak authentication service
├── submission/      # Data submission service
└── prompts/        # Shared AI prompts and documentation
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Make (recommended)
- Git

### 1. Start Entire Application

```bash
# From project root - starts all services
make up

# Or start individual services
make database-up    # PostgreSQL + PostGIS database
make auth-up       # Keycloak authentication  
make api-up        # FastAPI backend service
make frontend-dev  # Vue.js development server
```

### 2. Stop Application

```bash
# From project root - stops all services
make down

# Check status of all services
make stack-status
```

### 3. Development Setup

```bash
# Complete development environment setup
make setup

# Test all service endpoints
make stack-test
```

## 📊 Services Status

| Service | Status | Port | Description |
|---------|--------|------|-------------|
| **Database** | ✅ **Production Ready** | 5432 | PostgreSQL 15 + PostGIS 3.3 with full migration system |
| **API** | ✅ **Production Ready** | 8000 | FastAPI backend with spatial endpoints and CORS |
| **Frontend** | ✅ **Production Ready** | 3000/3001 | Vue.js + TypeScript with interactive Leaflet maps |
| **Auth** | ✅ **Configured** | 8080 | Keycloak authentication service |
| **Submission** | 🚧 **In Development** | TBD | Data submission processing service |

## 🔐 API Access (CRITICAL)

**⚠️ ALL applications MUST use the dedicated API user:**

```bash
# Database Connection
Host: localhost
Port: 5432
Database: faunalogic
Username: faunalogic_api
Password: flatpass
Schema: faunalogic_dapi_v1
```

**🚫 NEVER use the `postgres` superuser for application connections!**

## 🗃️ Database Features

- **Spatial Data**: Full PostGIS suite with geometry/geography support
- **Multi-tenant**: Tenant-based data isolation
- **Security**: Configurable spatial data transformations
- **Audit System**: Comprehensive logging of all data changes
- **API Layer**: Dedicated schema with functions and views
- **Migration System**: Flyway-based versioned database evolution

## 🛠️ Development Workflow

### Full Stack Development

```bash
# Start entire development stack
make dev-stack

# Individual service management
make database-up     # Start database only
make api-up         # Start API only  
make frontend-dev   # Start frontend dev server
make auth-up        # Start authentication

# View logs from all services
make logs

# Stop everything
make down
```

### Component-Specific Development

**Database:**
```bash
cd database/
make up migrate psql    # Start, migrate, connect
make backup            # Create backup
make reset            # Complete reset
```

**API:**
```bash
cd api/
make dev              # Start development server
make test            # Run test suite
make lint            # Code quality checks
```

**Frontend:**
```bash  
cd frontend/
make dev             # Start with proxy configuration
make build           # Build for production
make test            # Run test suite
```

### Cross-Service Integration

The services are configured to work together:
- Frontend proxies `/api/*` requests to backend (port 8000)
- API has CORS configured for frontend origins
- Database uses dedicated `faunalogic_api` user
- Authentication integrates with Keycloak

### Adding New Services

1. Create service directory following established patterns
2. Add service-specific prompts to `{service}/prompts/`
3. Update main `.gitignore` if needed
4. Follow established patterns from existing services

## 📁 Project Structure

```
faunalogic/
├── Makefile                   # Root project automation (consistent commands)
├── README.md                  # This file - main project documentation
│
├── database/                  # PostgreSQL + PostGIS database service
│   ├── Makefile              # Database commands (up, down, migrate, backup)
│   ├── README.md             # Database-specific documentation
│   ├── migrations/           # 16 Flyway versioned migrations
│   ├── dev-seed/            # Development seed data (7 files)
│   └── scripts/             # Database utility scripts
│
├── api/                      # FastAPI backend service
│   ├── Makefile             # API commands (up, down, dev, test, lint)
│   ├── README.md            # API-specific documentation
│   ├── app/                 # FastAPI application source
│   ├── tests/               # API test suite (unit + integration)
│   └── requirements/        # Python dependencies
│
├── frontend/                 # Vue.js + TypeScript frontend
│   ├── Makefile             # Frontend commands (up, down, dev, build)
│   ├── README.md            # Frontend-specific documentation  
│   ├── src/                 # Vue.js application source
│   ├── vite.config.ts       # Vite configuration with API proxy
│   └── package.json         # Node.js dependencies
│
├── auth/                    # Keycloak authentication service
│   ├── Makefile            # Auth commands (up, down, import, export)
│   ├── README.md           # Auth-specific documentation
│   └── realms/            # Keycloak realm configurations
│
├── submission/             # Data submission service (in development)
│   ├── Makefile           # Submission commands
│   ├── README.md          # Submission-specific documentation
│   └── requirements/      # Python dependencies
│
└── prompts/               # Shared AI prompts and templates
    ├── system/           # System-wide prompts
    ├── features/         # Feature-specific prompts
    ├── tasks/           # Task-specific prompts
    └── templates/       # Reusable templates
```

## 🔧 Environment Configuration

### Database Environment
Copy and customize the environment file:

```bash
cp database/.env.example database/.env
# Edit database/.env with your settings
```

Key variables:
- `POSTGRES_PASSWORD` - Database superuser password
- `API_PASSWORD` - API user password
- `BACKUP_RETENTION_DAYS` - Backup retention period

## 🧪 Testing

### Database Testing

```bash
cd database/

# Validate migrations
./scripts/migrate.sh validate

# Test on clean database
make clean && make setup

# Check spatial functionality
make psql
SELECT postgis_version();
```

## 📚 Documentation

### Service Documentation
- **Database Service**: [database/README.md](database/README.md) - PostgreSQL + PostGIS setup and management
- **API Service**: [api/README.md](api/README.md) - FastAPI backend development and deployment  
- **Frontend Application**: [frontend/README.md](frontend/README.md) - Vue.js frontend development
- **Authentication Service**: [auth/README.md](auth/README.md) - Keycloak configuration and management
- **Submission Service**: [submission/README.md](submission/README.md) - Data submission processing

### Development Documentation
- **System Prompts**: [prompts/system/](prompts/system/) - AI development prompts
- **Feature Prompts**: [prompts/features/](prompts/features/) - Feature-specific guidance
- **Task Templates**: [prompts/templates/](prompts/templates/) - Standardized task templates
- **API Documentation**: [api/docs/API_DOCUMENTATION.md](api/docs/API_DOCUMENTATION.md) - Detailed API reference

## 🔍 Monitoring

### Database Health
```bash
cd database/
make status          # Service status
make logs            # Real-time logs
./scripts/migrate.sh info  # Migration status
```

### Performance Monitoring
```sql
-- Connect with: make psql
SELECT * FROM pg_stat_activity;
SELECT * FROM pg_stat_user_tables;
SELECT postgis_version();
```

## 🚨 Troubleshooting

### Common Issues

1. **Port 5432 already in use**:
   ```bash
   docker ps | grep 5432
   # Stop conflicting services
   ```

2. **Migration failures**:
   ```bash
   cd database/
   ./scripts/migrate.sh info
   ./scripts/migrate.sh repair
   ```

3. **WSL2 connection issues**:
   ```bash
   # Use built-in connection
   make psql
   
   # Or install postgresql-client
   sudo apt install postgresql-client
   psql -h localhost -p 5432 -U postgres -d faunalogic
   ```

4. **Docker Compose version issues**:
   - The system auto-detects V1 (`docker-compose`) vs V2 (`docker compose`)
   - Both are supported

### Recovery Procedures

```bash
cd database/

# Complete reset
make reset

# Restore from backup
make restore

# Clean rebuild
make clean && make setup
```

## 🤝 Contributing

1. Follow service-specific coding standards in `{service}/prompts/system/`
2. Use AI prompts in `prompts/` for consistent development
3. Update documentation when adding features
4. Test changes with `make reset && make setup`
5. Follow git commit conventions

## 📊 Project Status

**Current Phase**: Production-Ready Full Stack Application ✅

**Completed**:
- [x] Database Infrastructure with PostGIS and migrations (16 migrations)
- [x] FastAPI backend with spatial endpoints and CORS
- [x] Vue.js + TypeScript frontend with interactive Leaflet maps  
- [x] Keycloak authentication service configuration
- [x] Docker containerization for all services
- [x] Consistent Makefile commands across all services
- [x] Frontend-backend integration with proxy configuration
- [x] Comprehensive testing infrastructure
- [x] Development seed data and backup systems

**In Progress**:
- [ ] Submission service completion
- [ ] User authentication flow integration
- [ ] Advanced spatial data filtering

**Next Steps**:
- [ ] CI/CD pipeline setup
- [ ] Production deployment automation
- [ ] Advanced map features and data visualization
- [ ] Performance optimization and monitoring

## 📝 License

This project is part of the FaunaLogic spatial data management system.

---

**🔑 Remember**: Always use `faunalogic_api` user and `faunalogic_dapi_v1` schema for application connections!