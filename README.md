# FaunaLogic Spatial Wildlife Data Management System

A comprehensive monorepo for spatial wildlife data management with PostgreSQL/PostGIS backend, API services, and modern frontend interfaces.

## 🏗️ Architecture

```
faunalogic/
├── database/           # PostgreSQL + PostGIS database service
├── api/               # REST API service
├── frontend/          # Vue.js web application interface
└── prompts/           # Shared AI prompts and documentation
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Make (recommended)
- Git

### 1. Database Setup (Ready for Production)

```bash
cd database/
make setup
```

This will:
- ✅ Build PostgreSQL 15 + PostGIS 3.3 Docker image
- ✅ Run 8 Flyway migrations (V1.0.0-V1.0.8)
- ✅ Create spatial data schema with security framework
- ✅ Set up API user (`faunalogic_api`) and schema (`faunalogic_dapi_v1`)
- ✅ Initialize audit logging and multi-tenant architecture

### 2. Verify Database

```bash
cd database/
make status
make psql  # Connect to database
```

## 📊 Services Status

| Service | Status | Description |
|---------|--------|-------------|
| **Database** | ✅ **Production Ready** | PostgreSQL 15 + PostGIS 3.3 with full migration system |
| **API** | ✅ **Implemented** | REST API service with spatial data endpoints |
| **Frontend** | ✅ **Implemented** | Vue.js web application with interactive maps |

## 🔐 API Access (CRITICAL)

**⚠️ ALL applications MUST use the dedicated API user:**

```bash
# Database Connection
Host: localhost
Port: 5432
Database: faunalogic
Username: faunalogic_api
Password: flatpass
Schema: faunalogic_dapi_v1
```

**🚫 NEVER use the `postgres` superuser for application connections!**

## 🗃️ Database Features

- **Spatial Data**: Full PostGIS suite with geometry/geography support
- **Multi-tenant**: Tenant-based data isolation
- **Security**: Configurable spatial data transformations
- **Audit System**: Comprehensive logging of all data changes
- **API Layer**: Dedicated schema with functions and views
- **Migration System**: Flyway-based versioned database evolution

## 🛠️ Development Workflow

### Database Development

```bash
cd database/

# Development commands
make up              # Start database
make migrate         # Run migrations
make logs            # View logs
make psql            # Connect to database
make status          # Check health

# Maintenance commands
make backup          # Create backup
make clean           # Remove containers/volumes
make reset           # Complete reset
```

### Frontend Development

```bash
cd frontend/

# Development commands
make install         # Install dependencies
make dev            # Start development server
make build          # Build for production
make test           # Run tests
make lint           # Run linting

# Docker commands
make docker-build   # Build production image
make docker-run     # Run production container
```

### Adding New Services

1. Create service directory following established patterns
2. Add service-specific prompts to `{service}/prompts/`
3. Update main `.gitignore` if needed
4. Follow established patterns from existing services

## 📁 Key Files

```
.gitignore                     # Comprehensive monorepo exclusions
database/
├── README.md                  # Database service documentation
├── Makefile                   # Development commands
├── docker-compose.yml         # Development configuration
├── .env.example              # Environment template
├── migrations/               # Flyway versioned migrations
│   ├── V1_0_0__Initial_Database_Setup.sql
│   ├── V1_0_1__Create_Spatial_Extensions.sql
│   ├── V1_0_2__Create_API_User.sql
│   ├── V1_0_3__Create_Core_Tables.sql
│   ├── V1_0_4__Create_Spatial_Tables.sql
│   ├── V1_0_5__Create_Security_Tables.sql
│   ├── V1_0_6__Create_API_Functions.sql
│   ├── V1_0_7__Create_Audit_Triggers.sql
│   └── V1_0_8__Insert_Initial_Data.sql
├── prompts/                  # Database-specific AI prompts
│   ├── system/              # System prompts
│   └── templates/           # Task templates
└── scripts/                 # Utility scripts
    ├── migrate.sh           # Migration helper
    ├── backup.sh            # Backup script
    └── restore.sh           # Restore script
```

## 🔧 Environment Configuration

### Database Environment
Copy and customize the environment file:

```bash
cp database/.env.example database/.env
# Edit database/.env with your settings
```

Key variables:
- `POSTGRES_PASSWORD` - Database superuser password
- `API_PASSWORD` - API user password
- `BACKUP_RETENTION_DAYS` - Backup retention period

## 🧪 Testing

### Database Testing

```bash
cd database/

# Validate migrations
./scripts/migrate.sh validate

# Test on clean database
make clean && make setup

# Check spatial functionality
make psql
SELECT postgis_version();
```

## 📚 Documentation

- **Database Service**: [database/README.md](database/README.md)
- **API Development**: [api/prompts/](api/prompts/) (when available)
- **Frontend Development**: [frontend/prompts/](frontend/prompts/) (when available)
- **System Prompts**: [prompts/system/](prompts/system/)

## 🔍 Monitoring

### Database Health
```bash
cd database/
make status          # Service status
make logs            # Real-time logs
./scripts/migrate.sh info  # Migration status
```

### Performance Monitoring
```sql
-- Connect with: make psql
SELECT * FROM pg_stat_activity;
SELECT * FROM pg_stat_user_tables;
SELECT postgis_version();
```

## 🚨 Troubleshooting

### Common Issues

1. **Port 5432 already in use**:
   ```bash
   docker ps | grep 5432
   # Stop conflicting services
   ```

2. **Migration failures**:
   ```bash
   cd database/
   ./scripts/migrate.sh info
   ./scripts/migrate.sh repair
   ```

3. **WSL2 connection issues**:
   ```bash
   # Use built-in connection
   make psql
   
   # Or install postgresql-client
   sudo apt install postgresql-client
   psql -h localhost -p 5432 -U postgres -d faunalogic
   ```

4. **Docker Compose version issues**:
   - The system auto-detects V1 (`docker-compose`) vs V2 (`docker compose`)
   - Both are supported

### Recovery Procedures

```bash
cd database/

# Complete reset
make reset

# Restore from backup
make restore

# Clean rebuild
make clean && make setup
```

## 🤝 Contributing

1. Follow service-specific coding standards in `{service}/prompts/system/`
2. Use AI prompts in `prompts/` for consistent development
3. Update documentation when adding features
4. Test changes with `make reset && make setup`
5. Follow git commit conventions

## 📊 Project Status

**Current Phase**: Full Stack Application Complete ✅

**Completed**:
- [x] Database Infrastructure with PostGIS
- [x] REST API service with spatial endpoints
- [x] Frontend application with interactive maps
- [x] Docker containerization

**Next Steps**:
- [ ] CI/CD pipeline setup
- [ ] Production deployment configuration
- [ ] User authentication integration
- [ ] Advanced map features and filters

## 📝 License

This project is part of the FaunaLogic spatial data management system.

---

**🔑 Remember**: Always use `faunalogic_api` user and `faunalogic_dapi_v1` schema for application connections!