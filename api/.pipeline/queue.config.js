'use strict';

let options = require('pipeline-cli').Util.parseArguments();

// The root config for common values
const config = require('../../.config/config.json');

const appName = config.module.app;
const name = config.module.queue;
const dbName = config.module.db;

const version = config.version;

const changeId = options.pr;

// A static deployment is when the deployment is updating dev, test, or prod (rather than a temporary PR)
// See `--type=static` in the `deployStatic.yml` git workflow
const isStaticDeployment = options.type === 'static';

const deployChangeId = (isStaticDeployment && 'deploy') || changeId;
const branch = (isStaticDeployment && options.branch) || null;
const tag = (branch && `build-${version}-${changeId}-${branch}`) || `build-${version}-${changeId}`;

const staticUrlsAPI = config.staticUrlsAPI;
const staticUrls = config.staticUrls;

const queueDockerfilePath = './Dockerfile.queue';

const processOptions = (options) => {
  const result = { ...options };

  // Check git
  if (!result.git.url.includes('.git')) {
    result.git.url = `${result.git.url}.git`;
  }

  if (!result.git.http_url.includes('.git')) {
    result.git.http_url = `${result.git.http_url}.git`;
  }

  // Fixing repo
  if (result.git.repository.includes('/')) {
    const last = result.git.repository.split('/').pop();
    const final = last.split('.')[0];
    result.git.repository = final;
  }

  return result;
};

options = processOptions(options);

const phases = {
  build: {
    namespace: 'a0ec71-tools',
    name: `${name}`,
    dbName: `${dbName}`,
    phase: 'build',
    changeId: changeId,
    suffix: `-build-${changeId}`,
    instance: `${name}-build-${changeId}`,
    version: `${version}-${changeId}`,
    tag: tag,
    tz: config.timezone.api,
    branch: branch,
    queueDockerfilePath: queueDockerfilePath,
    cpuRequest: '100m',
    cpuLimit: '1250m',
    memoryRequest: '512Mi',
    memoryLimit: '3Gi'
  },
  dev: {
    namespace: 'a0ec71-dev',
    name: `${name}`,
    dbName: `${dbName}`,
    phase: 'dev',
    changeId: deployChangeId,
    suffix: `-dev-${deployChangeId}`,
    instance: `${name}-dev-${deployChangeId}`,
    version: `${deployChangeId}-${changeId}`,
    tag: `dev-${version}-${deployChangeId}`,
    host: (isStaticDeployment && staticUrlsAPI.dev) || `${name}-${changeId}-a0ec71-dev.apps.silver.devops.gov.bc.ca`,
    appHost: (isStaticDeployment && staticUrls.dev) || `${appName}-${changeId}-a0ec71-dev.apps.silver.devops.gov.bc.ca`,
    nodeEnv: 'development',
    s3KeyPrefix: 'faunalogic',
    tz: config.timezone.api,
    logLevel: (isStaticDeployment && 'info') || 'debug',
    logLevelFile: (isStaticDeployment && 'debug') || 'debug',
    logFileDir: 'data/logs',
    logFileName: 'faunalogic-platform-api.log',
    logFileDatePattern: 'YYYY-MM-DD',
    logFileMaxSize: '45m',
    logFileMaxFiles: (isStaticDeployment && '10') || '2',
    volumeCapacity: (isStaticDeployment && '500Mi') || '100Mi',
    queueDockerfilePath: queueDockerfilePath,
    cpuRequest: '50m',
    cpuLimit: '500m',
    memoryRequest: '100Mi',
    memoryLimit: '3Gi',
    replicas: '1',
    replicasMax: '1'
  },
  test: {
    namespace: 'a0ec71-test',
    name: `${name}`,
    dbName: `${dbName}`,
    phase: 'test',
    changeId: deployChangeId,
    suffix: `-test`,
    instance: `${name}-test`,
    version: `${version}`,
    tag: `test-${version}`,
    host: staticUrlsAPI.test,
    appHost: staticUrls.test,
    nodeEnv: 'production',
    s3KeyPrefix: 'faunalogic',
    tz: config.timezone.api,
    logLevel: 'warn',
    logLevelFile: 'debug',
    logFileDir: 'data/logs',
    logFileName: 'faunalogic-platform-api.log',
    logFileDatePattern: 'YYYY-MM-DD',
    logFileMaxSize: '45m',
    logFileMaxFiles: '10',
    volumeCapacity: '500Mi',
    queueDockerfilePath: queueDockerfilePath,
    cpuRequest: '50m',
    cpuLimit: '1000m',
    memoryRequest: '100Mi',
    memoryLimit: '3Gi',
    replicas: '1',
    replicasMax: '1'
  },
  prod: {
    namespace: 'a0ec71-prod',
    name: `${name}`,
    dbName: `${dbName}`,
    phase: 'prod',
    changeId: deployChangeId,
    suffix: `-prod`,
    instance: `${name}-prod`,
    version: `${version}`,
    tag: `prod-${version}`,
    host: staticUrlsAPI.prod,
    appHost: staticUrls.prod,
    nodeEnv: 'production',
    s3KeyPrefix: 'faunalogic',
    tz: config.timezone.api,
    logLevel: 'silent',
    logLevelFile: 'debug',
    logFileDir: 'data/logs',
    logFileName: 'faunalogic-platform-api.log',
    logFileDatePattern: 'YYYY-MM-DD',
    logFileMaxSize: '45m',
    logFileMaxFiles: '10',
    volumeCapacity: '500Mi',
    queueDockerfilePath: queueDockerfilePath,
    cpuRequest: '50m',
    cpuLimit: '1000m',
    memoryRequest: '100Mi',
    memoryLimit: '4Gi',
    replicas: '1',
    replicasMax: '1'
  }
};

module.exports = exports = { phases, options };
