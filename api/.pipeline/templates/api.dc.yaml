kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: faunalogic-platform-api-dc
  labels:
    build: faunalogic-platform-api
parameters:
  - name: NAMESPACE
    description: Openshift namespace name
    value: ''
  - name: BASE_IMAGE_REGISTRY_URL
    description: The base image registry URL
    value: image-registry.openshift-image-registry.svc:5000
  - name: NAME
    value: faunalogic-platform-api
  - name: SUFFIX
    value: '-dev'
  - name: VERSION
    description: Version of the application
    value: '1.0.0'
  - name: HOST
    description: Host name of the application
    required: true
  - name: APP_HOST
    description: APP host for application frontend
    required: true
  - name: CHANGE_ID
    description: Change id of the project. This will help to pull image stream
    required: true
    value: '0'
  # Clamav
  - name: ENABLE_FILE_VIRUS_SCAN
    value: 'true'
  - name: CLAMAV_HOST
    value: 'clamav'
  - name: CLAMAV_PORT
    value: '3310'
  # Volume (for API logs and other persistent data)
  - name: VOLUME_CAPACITY
    required: true
    description: Volume space available for data, e.g. 512Mi, 2Gi.
    value: '500Mi'
  # Node
  - name: NODE_ENV
    description: Application Environment type variable
    required: true
    value: 'development'
  - name: NODE_OPTIONS
  # Database
  - name: DB_SERVICE_NAME
    description: 'Database service name associated with deployment'
    required: true
  - name: DB_PORT
    description: 'Database port'
    required: true
    value: '5432'
  - name: TZ
    description: Application timezone
    required: false
    value: 'America/Vancouver'
  # ITIS SOLR
  - name: ITIS_SOLR_URL
    description: ITIS SOLR API URL
    value: 'https://services.itis.gov'
    required: true
  # Keycloak
  - name: KEYCLOAK_HOST
    description: Key clock login url
    required: true
  - name: KEYCLOAK_REALM
    description: Realm identifier or name
    required: true
  - name: KEYCLOAK_CLIENT_ID
    description: Client Id for application
    required: true
  # Keycloak secret
  - name: KEYCLOAK_SECRET
    description: The name of the keycloak secret
    required: true
  # Keycloak Service Client
  - name: KEYCLOAK_ADMIN_USERNAME
    description: keycloak host admin username
    required: true
  - name: KEYCLOAK_SECRET_ADMIN_PASSWORD_KEY
    description: The key of the admin password in the keycloak secret
    required: true
  # Keycloak CSS API
  - name: KEYCLOAK_API_TOKEN_URL
    description: The url to fetch a css api access token, which is needed to call the css rest api
    required: true
  - name: KEYCLOAK_API_CLIENT_ID
    description: The css api client id
    required: true
  - name: KEYCLOAK_API_CLIENT_SECRET_KEY
    description: The css api client secret
    required: true
  - name: KEYCLOAK_API_HOST
    description: The url of the css rest api
    required: true
  - name: KEYCLOAK_API_ENVIRONMENT
    description: The css api environment to query (dev, test, prod)
    required: true
  - name: API_PORT_DEFAULT
    value: '6100'
  - name: API_PORT_DEFAULT_NAME
    description: Api default port name
    value: '6100-tcp'
  # Object Store (S3)
  - name: S3_KEY_PREFIX
    description: S3 key optional prefix
    required: false
    value: 'faunalogic'
  - name: OBJECT_STORE_SECRETS
    description: Secrets used to read and write to the S3 storage
    value: 'faunalogic-object-store'
  # GC Notify
  - name: GCNOTIFY_API_SECRET
    description: Secret for gcnotify api key
    value: 'gcnotify-api-key'
  - name: GCNOTIFY_ADMIN_EMAIL
    description: admin email for gcnotify api
    value: <EMAIL>
  - name: GCNOTIFY_REQUEST_ACCESS_SECURE_DOCUMENTS
    description: 'GC Notify template ID for secure document access requests'
    value: 4bb42a76-f79b-424f-ad0f-ad3671389ec2
  - name: GCNOTIFY_ONBOARDING_REQUEST_EMAIL_TEMPLATE
    description: gcnotify email template id
    value: 7779a104-b863-40ac-902f-1aa607d2071a
  - name: GCNOTIFY_ONBOARDING_REQUEST_SMS_TEMPLATE
    description: gcnotify sms template id
    value: af2f1e40-bd72-4612-9c5a-567ee5b26ca5
  - name: GCNOTIFY_EMAIL_URL
    value: https://api.notification.canada.ca/v2/notifications/email
  - name: GCNOTIFY_SMS_URL
    value: https://api.notification.canada.ca/v2/notifications/sms
  # Logging
  - name: LOG_LEVEL
    value: 'silent'
    description: Log level for logs written to the console (console transport)
  - name: LOG_LEVEL_FILE
    value: 'debug'
    description: Log level for logs written to log files (file transport)
  - name: LOG_FILE_DIR
    value: data/logs
    description: Directory where log files are stored
  - name: LOG_FILE_NAME
    value: faunalogic-platform-api.log
    description: Name of the log file
  - name: LOG_FILE_DATE_PATTERN
    value: YYYY-MM-DD
    description: Date pattern for log the files
  - name: LOG_FILE_MAX_SIZE
    value: 45m
    description: Maximum size an individual log file can reach before a new file is created
  - name: LOG_FILE_MAX_FILES
    value: '10'
    description: Either the maximum number of log files to keep (10) or the maximum number of days to keep log files (10d)
  # Openshift
  - name: CPU_REQUEST
    value: '100m'
  - name: CPU_LIMIT
    value: '500m'
  - name: MEMORY_REQUEST
    value: '512Mi'
  - name: MEMORY_LIMIT
    value: '2Gi'
  - name: REPLICAS
    value: '1'
  - name: REPLICAS_MAX
    value: '1'
objects:
  - kind: ImageStream
    apiVersion: image.openshift.io/v1
    metadata:
      annotations:
        description: Nodejs Runtime Image
      labels:
        shared: 'true'
      generation: 0
      name: ${NAME}
    spec:
      lookupPolicy:
        local: false

  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: ${NAME}${SUFFIX}
    spec:
      accessModes:
        - ReadWriteMany
      resources:
        requests:
          storage: '${VOLUME_CAPACITY}'

  - kind: Deployment
    apiVersion: apps/v1
    metadata:
      annotations:
        openshift.io/generated-by: OpenShiftWebConsole
      generation: 0
      labels:
        role: api
      name: ${NAME}${SUFFIX}
    spec:
      replicas: ${{REPLICAS}}
      revisionHistoryLimit: 10
      selector:
        matchLabels:
          deployment: ${NAME}${SUFFIX}
      strategy:
        type: RollingUpdate
        rollingUpdate:
          maxSurge: 25%
          maxUnavailable: 25%
      template:
        metadata:
          annotations: null
          labels:
            deployment: ${NAME}${SUFFIX}
            role: api
        spec:
          containers:
            - name: api
              env:
                - name: API_HOST
                  value: ${HOST}
                - name: API_PORT
                  value: ${API_PORT_DEFAULT}
                - name: APP_HOST
                  value: ${APP_HOST}
                - name: VERSION
                  value: ${VERSION}
                # File Virus Scan
                - name: ENABLE_FILE_VIRUS_SCAN
                  value: ${ENABLE_FILE_VIRUS_SCAN}
                - name: CLAMAV_HOST
                  value: ${CLAMAV_HOST}
                - name: CLAMAV_PORT
                  value: ${CLAMAV_PORT}
                # Database
                - name: TZ
                  value: ${TZ}
                - name: DB_HOST
                  value: ${DB_SERVICE_NAME}
                - name: DB_USER_API
                  valueFrom:
                    secretKeyRef:
                      key: database-user-api
                      name: ${DB_SERVICE_NAME}
                - name: DB_USER_API_PASS
                  valueFrom:
                    secretKeyRef:
                      key: database-user-api-password
                      name: ${DB_SERVICE_NAME}
                - name: DB_DATABASE
                  valueFrom:
                    secretKeyRef:
                      key: database-name
                      name: ${DB_SERVICE_NAME}
                - name: DB_PORT
                  value: ${DB_PORT}
                # Keycloak
                - name: KEYCLOAK_HOST
                  value: ${KEYCLOAK_HOST}
                - name: KEYCLOAK_REALM
                  value: ${KEYCLOAK_REALM}
                - name: KEYCLOAK_CLIENT_ID
                  value: ${KEYCLOAK_CLIENT_ID}
                # Keycloak Service Client
                - name: KEYCLOAK_ADMIN_USERNAME
                  value: ${KEYCLOAK_ADMIN_USERNAME}
                - name: KEYCLOAK_ADMIN_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: ${KEYCLOAK_SECRET}
                      key: ${KEYCLOAK_SECRET_ADMIN_PASSWORD_KEY}
                # Keycloak CSS API
                - name: KEYCLOAK_API_TOKEN_URL
                  value: ${KEYCLOAK_API_TOKEN_URL}
                - name: KEYCLOAK_API_CLIENT_ID
                  value: ${KEYCLOAK_API_CLIENT_ID}
                - name: KEYCLOAK_API_CLIENT_SECRET
                  valueFrom:
                    secretKeyRef:
                      name: ${KEYCLOAK_SECRET}
                      key: ${KEYCLOAK_API_CLIENT_SECRET_KEY}
                - name: KEYCLOAK_API_HOST
                  value: ${KEYCLOAK_API_HOST}
                - name: KEYCLOAK_API_ENVIRONMENT
                  value: ${KEYCLOAK_API_ENVIRONMENT}
                - name: CHANGE_VERSION
                  value: ${CHANGE_ID}
                - name: NODE_ENV
                  value: ${NODE_ENV}
                - name: NODE_OPTIONS
                  value: ${NODE_OPTIONS}
                # ITIS SOLR
                - name: ITIS_SOLR_URL
                  value: ${ITIS_SOLR_URL}
                # Object Store (S3)
                - name: OBJECT_STORE_URL
                  valueFrom:
                    secretKeyRef:
                      key: object_store_url
                      name: ${OBJECT_STORE_SECRETS}
                - name: OBJECT_STORE_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      key: object_store_access_key_id
                      name: ${OBJECT_STORE_SECRETS}
                - name: OBJECT_STORE_SECRET_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      key: object_store_secret_key_id
                      name: ${OBJECT_STORE_SECRETS}
                - name: OBJECT_STORE_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      key: object_store_bucket_name
                      name: ${OBJECT_STORE_SECRETS}
                - name: S3_KEY_PREFIX
                  value: ${S3_KEY_PREFIX}
                # Logging
                - name: LOG_LEVEL
                  value: ${LOG_LEVEL}
                - name: LOG_LEVEL_FILE
                  value: ${LOG_LEVEL_FILE}
                - name: LOG_FILE_DIR
                  value: ${LOG_FILE_DIR}
                - name: LOG_FILE_NAME
                  value: ${LOG_FILE_NAME}
                - name: LOG_FILE_DATE_PATTERN
                  value: ${LOG_FILE_DATE_PATTERN}
                - name: LOG_FILE_MAX_SIZE
                  value: ${LOG_FILE_MAX_SIZE}
                - name: LOG_FILE_MAX_FILES
                  value: ${LOG_FILE_MAX_FILES}
                # GC Notify
                - name: GCNOTIFY_SECRET_API_KEY
                  valueFrom:
                    secretKeyRef:
                      key: key
                      name: ${GCNOTIFY_API_SECRET}
                - name: GCNOTIFY_ADMIN_EMAIL
                  value: ${GCNOTIFY_ADMIN_EMAIL}
                - name: GCNOTIFY_REQUEST_ACCESS_SECURE_DOCUMENTS
                  value: ${GCNOTIFY_REQUEST_ACCESS_SECURE_DOCUMENTS}
                - name: GCNOTIFY_ONBOARDING_REQUEST_EMAIL_TEMPLATE
                  value: ${GCNOTIFY_ONBOARDING_REQUEST_EMAIL_TEMPLATE}
                - name: GCNOTIFY_ONBOARDING_REQUEST_SMS_TEMPLATE
                  value: ${GCNOTIFY_ONBOARDING_REQUEST_SMS_TEMPLATE}
                - name: GCNOTIFY_EMAIL_URL
                  value: ${GCNOTIFY_EMAIL_URL}
                - name: GCNOTIFY_SMS_URL
                  value: ${GCNOTIFY_SMS_URL}
              image: ${BASE_IMAGE_REGISTRY_URL}/${NAMESPACE}/${NAME}:${VERSION}
              imagePullPolicy: Always
              ports:
                - containerPort: ${{API_PORT_DEFAULT}}
                  protocol: TCP
              resources:
                requests:
                  cpu: ${CPU_REQUEST}
                  memory: ${MEMORY_REQUEST}
                limits:
                  cpu: ${CPU_LIMIT}
                  memory: ${MEMORY_LIMIT}
              startupProbe:
                httpGet:
                  path: /api/version
                  port: ${{API_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 30
                periodSeconds: 10
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 30
              readinessProbe:
                httpGet:
                  path: /api/version
                  port: ${{API_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              livenessProbe:
                httpGet:
                  path: /api/version
                  port: ${{API_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
              volumeMounts:
                - name: ${NAME}${SUFFIX}
                  mountPath: /opt/app-root/src/data
          dnsPolicy: ClusterFirst
          restartPolicy: Always
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
          volumes:
            - name: ${NAME}${SUFFIX}
              persistentVolumeClaim:
                claimName: ${NAME}${SUFFIX}
      test: false

  - kind: Secret
    apiVersion: v1
    stringData:
      database-name: ''
      database-user-api-password: ''
      database-user-api: ''
    metadata:
      annotations:
        as-copy-of: ${DB_SERVICE_NAME}
      name: ${NAME}${SUFFIX}
    type: Opaque

  - kind: Service
    apiVersion: v1
    metadata:
      annotations: null
      labels: {}
      name: ${NAME}${SUFFIX}
    spec:
      ports:
        - name: ${NAME}-${API_PORT_DEFAULT_NAME}
          port: ${{API_PORT_DEFAULT}}
          protocol: TCP
          targetPort: ${{API_PORT_DEFAULT}}
      selector:
        deployment: ${NAME}${SUFFIX}
      sessionAffinity: None
      type: ClusterIP

  - kind: Route
    apiVersion: route.openshift.io/v1
    metadata:
      annotations: {}
      labels: {}
      name: ${NAME}${SUFFIX}
    spec:
      host: ${HOST}
      tls:
        insecureEdgeTerminationPolicy: Redirect
        termination: edge
      port:
        targetPort: ${NAME}-${API_PORT_DEFAULT_NAME}
      to:
        kind: Service
        name: ${NAME}${SUFFIX}
        weight: 100
      wildcardPolicy: None

  # Disable the HPA for now, as it is preferrable to run an exact number of pods (e.g. min:2, max:2)
  # - kind: HorizontalPodAutoscaler
  #   apiVersion: autoscaling/v2
  #   metadata:
  #     annotations: {}
  #     labels: {}
  #     name: ${NAME}${SUFFIX}
  #   spec:
  #     minReplicas: ${{REPLICAS}}
  #     maxReplicas: ${{REPLICAS_MAX}}
  #     scaleTargetRef:
  #       kind: Deployment
  #       apiVersion: apps/v1
  #       name: ${NAME}${SUFFIX}
  #     metrics:
  #       - type: Resource
  #         resource:
  #           name: cpu
  #           target:
  #             type: Utilization
  #             averageUtilization: 80
