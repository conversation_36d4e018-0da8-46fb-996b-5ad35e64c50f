kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: faunalogic-platform-queue-dc
  labels:
    build: faunalogic-platform-queue-dc
parameters:
  - name: NAMESPACE
    description: Openshift namespace name
    value: ''
  - name: BASE_IMAGE_REGISTRY_URL
    description: The base image registry URL
    value: image-registry.openshift-image-registry.svc:5000
  - name: NAME
    value: faunalogic-platform-queue
  - name: SUFFIX
    value: '-dev'
  - name: VERSION
    description: Version of the application
    value: '1.0.0'
  - name: HOST
    description: Host name of the application
    required: true
  - name: APP_HOST
    description: APP host for application frontend
    required: true
  - name: CHANGE_ID
    description: Change id of the project. This will help to pull image stream
    required: true
    value: '0'
  # Volume (for API logs and other persistent data)
  - name: VOLUME_CAPACITY
    required: true
    description: Volume space available for data, e.g. 512Mi, 2Gi.
    value: '500Mi'
  # Database
  - name: DB_SERVICE_NAME
    description: 'Database service name associated with deployment'
    required: true
  - name: DB_PORT
    description: 'Database port'
    required: true
    value: '5432'
  - name: TZ
    description: Application timezone
    required: false
    value: 'America/Vancouver'
  # Node
  - name: NODE_ENV
    description: Application Environment type variable
    required: true
    value: 'development'
  - name: NODE_OPTIONS
  # Object Store (S3)
  - name: S3_KEY_PREFIX
    description: S3 key optional prefix
    required: false
    value: 'biohub'
  - name: OBJECT_STORE_SECRETS
    description: Secrets used to read and write to the S3 storage
    value: 'faunalogic-object-store'
  # Log Level
  - name: LOG_LEVEL
    value: 'info'
  # Openshift
  - name: CPU_REQUEST
    value: '100m'
  - name: CPU_LIMIT
    value: '500m'
  - name: MEMORY_REQUEST
    value: '512Mi'
  - name: MEMORY_LIMIT
    value: '2Gi'
  - name: REPLICAS
    value: '1'
  - name: REPLICAS_MAX
    value: '1'
objects:
  - kind: ImageStream
    apiVersion: image.openshift.io/v1
    metadata:
      annotations:
        description: Nodejs Runtime Image
      labels:
        shared: 'true'
      generation: 0
      name: ${NAME}
    spec:
      lookupPolicy:
        local: false

  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: ${NAME}${SUFFIX}
    spec:
      accessModes:
        - ReadWriteMany
      resources:
        requests:
          storage: '${VOLUME_CAPACITY}'

  - kind: Deployment
    apiVersion: apps/v1
    metadata:
      annotations:
        openshift.io/generated-by: OpenShiftWebConsole
      generation: 0
      labels:
        role: queue
      name: ${NAME}${SUFFIX}
    spec:
      replicas: ${{REPLICAS}}
      revisionHistoryLimit: 10
      selector:
        matchLabels:
          deployment: ${NAME}${SUFFIX}
      strategy:
        type: RollingUpdate
        rollingUpdate:
          maxSurge: 25%
          maxUnavailable: 25%
      template:
        metadata:
          annotations: null
          labels:
            deployment: ${NAME}${SUFFIX}
            role: queue
        spec:
          containers:
            - name: queue
              env:
                - name: API_HOST
                  value: ${HOST}
                - name: APP_HOST
                  value: ${APP_HOST}
                - name: DB_HOST
                  value: ${DB_SERVICE_NAME}
                - name: DB_USER_API
                  valueFrom:
                    secretKeyRef:
                      key: database-user-api
                      name: ${DB_SERVICE_NAME}
                - name: DB_USER_API_PASS
                  valueFrom:
                    secretKeyRef:
                      key: database-user-api-password
                      name: ${DB_SERVICE_NAME}
                - name: DB_DATABASE
                  valueFrom:
                    secretKeyRef:
                      key: database-name
                      name: ${DB_SERVICE_NAME}
                - name: DB_PORT
                  value: ${DB_PORT}
                - name: CHANGE_VERSION
                  value: ${CHANGE_ID}
                - name: NODE_ENV
                  value: ${NODE_ENV}
                - name: NODE_OPTIONS
                  value: ${NODE_OPTIONS}
                - name: S3_KEY_PREFIX
                  value: ${S3_KEY_PREFIX}
                - name: TZ
                  value: ${TZ}
                - name: VERSION
                  value: ${VERSION}
                - name: OBJECT_STORE_URL
                  valueFrom:
                    secretKeyRef:
                      key: object_store_url
                      name: ${OBJECT_STORE_SECRETS}
                - name: OBJECT_STORE_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      key: object_store_access_key_id
                      name: ${OBJECT_STORE_SECRETS}
                - name: OBJECT_STORE_SECRET_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      key: object_store_secret_key_id
                      name: ${OBJECT_STORE_SECRETS}
                - name: OBJECT_STORE_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      key: object_store_bucket_name
                      name: ${OBJECT_STORE_SECRETS}
                - name: LOG_LEVEL
                  value: ${LOG_LEVEL}
              image: ${BASE_IMAGE_REGISTRY_URL}/${NAMESPACE}/${NAME}:${VERSION}
              imagePullPolicy: Always
              ports:
                - containerPort: ${{API_PORT_DEFAULT}}
                  protocol: TCP
              resources:
                requests:
                  cpu: ${CPU_REQUEST}
                  memory: ${MEMORY_REQUEST}
                limits:
                  cpu: ${CPU_LIMIT}
                  memory: ${MEMORY_LIMIT}
              startupProbe:
                httpGet:
                  path: /api/version
                  port: ${{API_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 30
                periodSeconds: 10
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 30
              readinessProbe:
                httpGet:
                  path: /api/version
                  port: ${{API_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              livenessProbe:
                httpGet:
                  path: /api/version
                  port: ${{API_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
              volumeMounts:
                - name: ${NAME}${SUFFIX}
                  mountPath: /opt/app-root/src/data
          dnsPolicy: ClusterFirst
          restartPolicy: Always
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
          volumes:
            - name: ${NAME}${SUFFIX}
              persistentVolumeClaim:
                claimName: ${NAME}${SUFFIX}
      test: false

  - kind: Secret
    apiVersion: v1
    stringData:
      database-name: ''
      database-user-api-password: ''
      database-user-api: ''
    metadata:
      annotations:
        as-copy-of: ${DB_SERVICE_NAME}
      name: ${NAME}${SUFFIX}
    type: Opaque

  # Disable the HPA for now, as it is preferrable to run an exact number of pods (e.g. min:2, max:2)
  # - kind: HorizontalPodAutoscaler
  #   apiVersion: autoscaling/v2
  #   metadata:
  #     annotations: {}
  #     labels: {}
  #     name: ${NAME}${SUFFIX}
  #   spec:
  #     minReplicas: ${{REPLICAS}}
  #     maxReplicas: ${{REPLICAS_MAX}}
  #     scaleTargetRef:
  #       kind: Deployment
  #       apiVersion: apps/v1
  #       name: ${NAME}${SUFFIX}
  #     metrics:
  #       - type: Resource
  #         resource:
  #           name: cpu
  #           target:
  #             type: Utilization
  #             averageUtilization: 80
