# FaunaLogic API Service Makefile

.PHONY: help setup dev prod test lint clean docker-build docker-up docker-down

# Default target
.DEFAULT_GOAL := help

# Python and pip commands
PYTHON := python3
PIP := pip3
VENV := .venv
VENV_PYTHON := $(VENV)/bin/python
VENV_PIP := $(VENV)/bin/pip

# Docker commands
DOCKER_COMPOSE := docker-compose -f docker/docker-compose.yml

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Set up development environment
	@echo "Setting up FaunaLogic API Service development environment..."
	$(PYTHON) -m venv $(VENV)
	$(VENV_PIP) install --upgrade pip
	$(VENV_PIP) install -r requirements/dev.txt
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from template"; fi
	@echo "Setup complete! Activate virtual environment with: source $(VENV)/bin/activate"

dev: ## Run development server
	@echo "Starting development server..."
	$(VENV_PYTHON) -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

dev-logs: ## Run development server with detailed logs
	@echo "Starting development server with detailed logs..."
	$(VENV_PYTHON) -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --log-level debug

prod: ## Run production server
	@echo "Starting production server..."
	$(VENV_PYTHON) -m gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000

test: ## Run tests
	@echo "Running tests..."
	$(VENV_PYTHON) -m pytest tests/ -v --cov=app --cov-report=html --cov-report=term

test-watch: ## Run tests in watch mode
	@echo "Running tests in watch mode..."
	$(VENV_PYTHON) -m pytest-watch tests/ -- -v --cov=app

test-integration: ## Run integration tests only
	@echo "Running integration tests..."
	$(VENV_PYTHON) -m pytest tests/integration/ -v

test-unit: ## Run unit tests only
	@echo "Running unit tests..."
	$(VENV_PYTHON) -m pytest tests/unit/ -v

lint: ## Run code linting and formatting
	@echo "Running code linting and formatting..."
	$(VENV_PYTHON) -m black app/ tests/
	$(VENV_PYTHON) -m isort app/ tests/
	$(VENV_PYTHON) -m flake8 app/ tests/
	$(VENV_PYTHON) -m mypy app/

format: ## Format code
	@echo "Formatting code..."
	$(VENV_PYTHON) -m black app/ tests/
	$(VENV_PYTHON) -m isort app/ tests/

check: ## Check code quality without making changes
	@echo "Checking code quality..."
	$(VENV_PYTHON) -m black --check app/ tests/
	$(VENV_PYTHON) -m isort --check-only app/ tests/
	$(VENV_PYTHON) -m flake8 app/ tests/
	$(VENV_PYTHON) -m mypy app/

install: ## Install dependencies
	$(VENV_PIP) install -r requirements/dev.txt

install-prod: ## Install production dependencies
	$(VENV_PIP) install -r requirements/prod.txt

clean: ## Clean up temporary files and caches
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/
	rm -rf logs/*.log

clean-all: clean ## Clean everything including virtual environment
	rm -rf $(VENV)

# Docker targets
docker-build: ## Build Docker images
	@echo "Building Docker images..."
	$(DOCKER_COMPOSE) build

docker-up: ## Start services with Docker Compose
	@echo "Starting services with Docker Compose..."
	$(DOCKER_COMPOSE) up -d

docker-up-logs: ## Start services with Docker Compose and show logs
	@echo "Starting services with Docker Compose..."
	$(DOCKER_COMPOSE) up

docker-down: ## Stop Docker Compose services
	@echo "Stopping Docker Compose services..."
	$(DOCKER_COMPOSE) down

docker-logs: ## View Docker Compose logs
	$(DOCKER_COMPOSE) logs -f

docker-ps: ## Show running Docker containers
	$(DOCKER_COMPOSE) ps

docker-shell: ## Open shell in API container
	$(DOCKER_COMPOSE) exec faunalogic-api /bin/bash

docker-rebuild: ## Rebuild and restart services
	@echo "Rebuilding and restarting services..."
	$(DOCKER_COMPOSE) down
	$(DOCKER_COMPOSE) build --no-cache
	$(DOCKER_COMPOSE) up -d

# Database targets
db-check: ## Check database connection
	@echo "Checking database connection..."
	$(VENV_PYTHON) -c "import asyncio; from app.config.database import check_database_connection; asyncio.run(check_database_connection())"

# Development utilities
docs: ## Generate and serve documentation
	@echo "Generating documentation..."
	$(VENV_PYTHON) -m mkdocs serve --dev-addr 0.0.0.0:8001

docs-build: ## Build documentation
	@echo "Building documentation..."
	$(VENV_PYTHON) -m mkdocs build

install-pre-commit: ## Install pre-commit hooks
	$(VENV_PYTHON) -m pre_commit install

# API testing
api-test: ## Test API endpoints manually
	@echo "Testing API endpoints..."
	curl -f http://localhost:8000/health || echo "API health check failed"
	curl -f http://localhost:8000/docs || echo "API docs not accessible"

# Monitoring
status: ## Show service status
	@echo "=== FaunaLogic API Service Status ==="
	@echo "Python version: $(shell $(PYTHON) --version)"
	@echo "Virtual environment: $(VENV)"
	@echo "Environment file: $(shell [ -f .env ] && echo 'Present' || echo 'Missing')"
	@echo "Docker Compose services:"
	@$(DOCKER_COMPOSE) ps || echo "Docker Compose not running"

health: ## Check service health
	@echo "Checking service health..."
	@curl -f http://localhost:8000/health || echo "Service not responding"

# Backup and maintenance
backup-logs: ## Backup log files
	@echo "Backing up log files..."
	@mkdir -p backups/logs/$(shell date +%Y%m%d)
	@cp -r logs/* backups/logs/$(shell date +%Y%m%d)/ 2>/dev/null || echo "No logs to backup"

# Security
security-check: ## Run security checks
	@echo "Running security checks..."
	$(VENV_PYTHON) -m safety check
	$(VENV_PYTHON) -m bandit -r app/

# Performance
benchmark: ## Run performance benchmarks
	@echo "Running performance benchmarks..."
	$(VENV_PYTHON) -m pytest tests/performance/ -v

# Database migration helpers (if using Alembic)
migrate: ## Run database migrations
	@echo "Running database migrations..."
	$(VENV_PYTHON) -m alembic upgrade head

migrate-auto: ## Auto-generate migration
	@echo "Auto-generating migration..."
	$(VENV_PYTHON) -m alembic revision --autogenerate

# Coverage reporting
coverage: ## Generate detailed coverage report
	@echo "Generating coverage report..."
	$(VENV_PYTHON) -m pytest tests/ --cov=app --cov-report=html --cov-report=term-missing
	@echo "Coverage report generated in htmlcov/"

# Quick start for new developers
quickstart: setup docker-up ## Quick setup for new developers
	@echo "=== Quick Start Complete ==="
	@echo "1. API running at: http://localhost:8000"
	@echo "2. API docs at: http://localhost:8000/docs"  
	@echo "3. Redis running at: localhost:6380"
	@echo "4. Run 'make test' to verify everything works"