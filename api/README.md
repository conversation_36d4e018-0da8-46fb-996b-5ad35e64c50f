# FaunaLogic API Service

A Python FastAPI service providing RESTful endpoints for the FaunaLogic spatial wildlife data management system.

## 🏗️ Architecture

```
api/
├── app/                    # Application source code
├── requirements/           # Python dependencies
├── docker/                # Docker configuration
├── scripts/               # Utility scripts
├── tests/                 # Test suite
└── docs/                  # API documentation
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL database (via FaunaLogic database service)
- Redis (for caching)
- Docker and Docker Compose (optional)

### 1. Development Setup

```bash
# Set up development environment
make setup

# Activate virtual environment
source .venv/bin/activate

# Copy environment configuration
cp .env.example .env
# Edit .env with your settings
```

### 2. Start Services

**Option A: Local Development**
```bash
# Start development server
make dev

# In a separate terminal, start Redis
redis-server --port 6380
```

**Option B: Docker Compose**
```bash
# Start all services
make docker-up

# View logs
make docker-logs

# Stop services
make docker-down
```

## 🔐 Database Integration (CRITICAL)

**⚠️ ALL database connections MUST use the dedicated API user:**

```python
# Database Configuration (REQUIRED)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=faunalogic
DB_USER=faunalogic_api     # NEVER use postgres user
DB_PASSWORD=flatpass
```

The service automatically:
- Sets `search_path = faunalogic_dapi_v1, public`
- Uses API functions for all database operations
- Maintains audit trails through database context

## 📊 Service Features

### RESTful API Endpoints
- **Authentication**: JWT-based with Keycloak integration
- **User Management**: CRUD operations with RBAC
- **Spatial Data**: GeoJSON format with PostGIS integration
- **Data Submissions**: File upload and processing endpoints
- **Health Checks**: Service monitoring and diagnostics

### FastAPI Features
- **Automatic Documentation**: OpenAPI/Swagger at `/docs`
- **Type Safety**: Pydantic models for validation
- **Async Support**: Full async/await implementation
- **Error Handling**: Comprehensive exception management
- **Performance**: Connection pooling and caching

### Database Integration
- **API Functions**: All database access via dedicated API functions
- **Tenant Isolation**: Multi-tenant data separation
- **Audit Logging**: Comprehensive operation tracking
- **Security**: Spatial data obfuscation and transformations

## 🛠️ Development Workflow

### Code Quality
```bash
# Format code
make format

# Run linting with ruff (fast Python linter)
make lint

# Check code quality
make check

# Run ruff directly for comprehensive linting
ruff check .
ruff format .
```

### Testing (Test Driven Development)
```bash
# Run all tests with pytest
make test

# Run specific test types
make test-unit          # Unit tests only
make test-integration   # Integration tests only

# Watch mode for TDD development
make test-watch

# Generate coverage report
make coverage

# TDD workflow with pytest
pytest --tb=short       # Short traceback for faster feedback
pytest -v               # Verbose output
pytest -x               # Stop on first failure
pytest --lf             # Run last failed tests
pytest --ff             # Run failures first, then remainder
```

**Test-Driven Development (TDD) Workflow:**
1. **Red**: Write a failing test first
2. **Green**: Write minimal code to make the test pass
3. **Refactor**: Improve code while keeping tests green
4. Use `pytest --watch` for continuous testing during development

### Database Operations
```bash
# Check database connection
make db-check

# The service uses the database service API functions
# No separate migrations needed
```

## 📁 Project Structure

### Application Structure
```python
# app/main.py - FastAPI application entry point
from fastapi import FastAPI
from app.config.settings import settings
from app.api.routes import auth, users, submissions, spatial

app = FastAPI(
    title="FaunaLogic API",
    description="Spatial Wildlife Data Management API",
    version="1.0.0"
)

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth")
app.include_router(users.router, prefix="/api/v1/users")
app.include_router(submissions.router, prefix="/api/v1/submissions")
app.include_router(spatial.router, prefix="/api/v1/spatial")
```

### Service Architecture
```
FastAPI App → Pydantic Models → Service Layer → Database API Functions
     ↓             ↓                ↓                    ↓
  Routing    Validation       Business Logic      PostgreSQL
  Security   Serialization    Error Handling      API Schema
```

## 🔧 Configuration

### Environment Variables
Key configuration options in `.env`:

```bash
# Application Configuration
ENV=development
DEBUG=true
API_PREFIX=/api/v1

# Database Configuration (CRITICAL)
DB_USER=faunalogic_api      # ALWAYS use API user
DB_PASSWORD=flatpass

# Security Configuration
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Cache Configuration
REDIS_URL=redis://localhost:6380/0
CACHE_TTL=300
```

### API Endpoints
- **Base URL**: `http://localhost:8000`
- **Documentation**: `http://localhost:8000/docs`
- **Health Check**: `http://localhost:8000/health`
- **Root**: `http://localhost:8000/`

## 🧪 Testing with TDD

### Test-Driven Development Approach
The API service follows TDD practices using pytest:
- **Write tests first** before implementing features
- **Use descriptive test names** that explain behavior
- **Keep tests isolated** and independent
- **Mock external dependencies** for unit tests
- **Use fixtures** for test data setup
- **Aim for high test coverage** (>90%)

### Test Structure
```
tests/
├── unit/                  # Unit tests
│   ├── test_services/
│   ├── test_models/
│   └── test_utils/
├── integration/           # Integration tests
│   ├── test_database/
│   ├── test_api/
│   └── test_auth/
├── fixtures/             # Test data
└── conftest.py          # Test configuration
```

### Sample Test
```python
# tests/integration/test_health.py
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_health_check():
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
```

## 📚 API Documentation

### Pydantic Models
```python
# app/models/user.py
from pydantic import BaseModel, EmailStr
from uuid import UUID

class UserCreate(BaseModel):
    first_name: str
    last_name: str
    email_address: EmailStr
    password: str

class User(BaseModel):
    id: UUID
    first_name: str
    last_name: str
    email_address: EmailStr
    active: bool
```

### Endpoint Examples
```bash
# Create user
curl -X POST "http://localhost:8000/api/v1/users/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"first_name": "John", "last_name": "Doe", "email_address": "<EMAIL>", "password": "SecurePass123!"}'

# Get user
curl "http://localhost:8000/api/v1/users/123" \
  -H "Authorization: Bearer <token>"

# List users
curl "http://localhost:8000/api/v1/users/?skip=0&limit=10" \
  -H "Authorization: Bearer <token>"
```

## 🔍 Monitoring

### Service Health
```bash
# Check service status
make status

# Health check endpoint
make health

# View service logs
make docker-logs
```

### Performance Monitoring
- Prometheus metrics endpoint: `/metrics`
- Structured logging with request tracing
- Database connection pool monitoring
- Response time tracking

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failures**:
   ```bash
   # Check database connectivity
   make db-check
   
   # Verify API user permissions
   psql -h localhost -U faunalogic_api -d faunalogic
   ```

2. **Import Errors**:
   ```bash
   # Check virtual environment
   source .venv/bin/activate
   
   # Reinstall dependencies
   make install
   ```

3. **Redis Connection Issues**:
   ```bash
   # Check Redis status
   redis-cli -p 6380 ping
   
   # Restart Redis
   make docker-down && make docker-up
   ```

### Development Tips
- Use `make dev-logs` for detailed development logging
- Run `make check` before committing code
- Use `make test-watch` during development
- Check `make status` for service overview

## 🤝 Development Guidelines

### Code Style
- **Formatter**: Black (primary) / Ruff (alternative)
- **Import Sorting**: isort
- **Linting**: Ruff (fast Python linter) + Flake8
- **Type Checking**: MyPy
- **Line Length**: 88 characters

**Ruff Configuration**: Use ruff for fast linting and formatting:
```bash
# Lint with ruff
ruff check .

# Format with ruff
ruff format .

# Fix auto-fixable issues
ruff check --fix .
```

### FastAPI Patterns
- Use Pydantic models for all request/response data
- Implement proper dependency injection
- Follow async/await patterns consistently
- Use structured logging throughout
- Implement comprehensive error handling

### Database Integration
- Always use `faunalogic_api` user for connections
- Call API functions instead of direct table access
- Set proper database context for audit trails
- Handle database errors gracefully

## 📈 Performance Considerations

### API Optimization
- Connection pooling for database access
- Redis caching for frequently accessed data
- Request/response compression
- Rate limiting for API endpoints

### Scalability
- Async request handling
- Horizontal scaling via load balancers
- Database connection pooling
- Stateless design for easy scaling

---

**🔑 Remember**: Always use `faunalogic_api` user for database connections and follow the established API patterns from the database service!

## 📝 License

This service is part of the FaunaLogic spatial data management system.