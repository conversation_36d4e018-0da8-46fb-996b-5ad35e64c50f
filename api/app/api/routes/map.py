"""
Homepage map API routes
Provides spatial data for the application homepage map
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import Optional
import structlog

from app.models.spatial import MapDataQuery, MapDataResponse, BoundingBox, ErrorResponse
from app.services.spatial_service import SpatialService

logger = structlog.get_logger(__name__)

router = APIRouter()


def get_spatial_service() -> SpatialService:
    """Dependency injection for SpatialService"""
    return SpatialService()


@router.get(
    "/data",
    response_model=MapDataResponse,
    summary="Get map data for homepage",
    description="Retrieve spatial data within a bounding box for the homepage map display",
    responses={
        200: {
            "description": "Successful response with GeoJSON data",
            "model": MapDataResponse
        },
        422: {
            "description": "Validation error - invalid bounding box parameters",
            "model": ErrorResponse
        },
        500: {
            "description": "Internal server error",
            "model": ErrorResponse
        }
    },
    tags=["map"]
)
async def get_map_data(
    min_lng: float = Query(
        ...,
        ge=-180.0,
        le=180.0,
        description="Minimum longitude in decimal degrees",
        example=-125.0
    ),
    min_lat: float = Query(
        ...,
        ge=-90.0,
        le=90.0,
        description="Minimum latitude in decimal degrees",
        example=49.0
    ),
    max_lng: float = Query(
        ...,
        ge=-180.0,
        le=180.0,
        description="Maximum longitude in decimal degrees",
        example=-120.0
    ),
    max_lat: float = Query(
        ...,
        ge=-90.0,
        le=90.0,
        description="Maximum latitude in decimal degrees",
        example=52.0
    ),
    limit: Optional[int] = Query(
        1000,
        ge=1,
        le=5000,
        description="Maximum number of features to return",
        example=1000
    ),
    spatial_service: SpatialService = Depends(get_spatial_service)
) -> MapDataResponse:
    """
    Get spatial data for homepage map within specified bounding box
    
    This endpoint retrieves spatial data from the unsecure_spatial_component table
    within the specified bounding box coordinates. The data is returned as a GeoJSON
    FeatureCollection suitable for map display.
    
    Args:
        min_lng: Minimum longitude (western boundary)
        min_lat: Minimum latitude (southern boundary)
        max_lng: Maximum longitude (eastern boundary)
        max_lat: Maximum latitude (northern boundary)
        limit: Maximum number of features to return (default 1000)
        spatial_service: Injected spatial service
    
    Returns:
        MapDataResponse: Contains GeoJSON FeatureCollection with spatial data
    
    Raises:
        HTTPException: 422 for validation errors, 500 for server errors
    """
    try:
        # Create and validate bounding box
        bbox = BoundingBox(
            min_lng=min_lng,
            min_lat=min_lat,
            max_lng=max_lng,
            max_lat=max_lat,
            limit=limit
        )
        
        logger.info(
            "Processing map data request",
            bbox=bbox.dict(),
            limit=limit
        )
        
        # Get spatial data from service
        feature_collection = await spatial_service.get_map_data(bbox)
        
        # Get total count for metadata
        total_count = await spatial_service.get_map_data_count(bbox)
        
        response = MapDataResponse(
            success=True,
            data=feature_collection,
            total_count=total_count,
            returned_count=len(feature_collection.features)
        )
        
        logger.info(
            "Map data request completed",
            returned_count=response.returned_count,
            total_count=response.total_count
        )
        
        return response
        
    except ValueError as e:
        logger.warning("Validation error in map data request", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail={
                "success": False,
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": str(e),
                    "details": {}
                }
            }
        )
    
    except Exception as e:
        logger.error("Unexpected error in map data request", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "An unexpected error occurred while processing the request",
                    "details": {}
                }
            }
        )