"""
Database configuration for FaunaLogic API
CRITICAL: Uses faunalogic_api user as required
"""

import asyncpg
from contextlib import asynccontextmanager
from typing import AsyncGenerator
import os


class DatabaseConfig:
    """Database configuration following FaunaLogic requirements"""
    
    def __init__(self):
        # CRITICAL: Always use faunalogic_api user
        self.host = os.getenv("DB_HOST", "localhost")
        self.port = int(os.getenv("DB_PORT", "5432"))
        self.database = os.getenv("DB_NAME", "faunalogic")
        self.user = os.getenv("DB_USER", "faunalogic_api")  # NEVER use postgres user
        self.password = os.getenv("DB_PASSWORD", "flatpass")
        
        # Connection pool settings
        self.min_connections = int(os.getenv("DB_MIN_CONNECTIONS", "5"))
        self.max_connections = int(os.getenv("DB_MAX_CONNECTIONS", "20"))
    
    @property
    def connection_url(self) -> str:
        """Get database connection URL"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"


# Global database configuration
db_config = DatabaseConfig()

# Global connection pool
_connection_pool = None


async def initialize_database_pool() -> None:
    """Initialize database connection pool"""
    global _connection_pool
    
    if _connection_pool is None:
        _connection_pool = await asyncpg.create_pool(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password,
            min_size=db_config.min_connections,
            max_size=db_config.max_connections,
            command_timeout=30
        )


async def close_database_pool() -> None:
    """Close database connection pool"""
    global _connection_pool
    
    if _connection_pool:
        await _connection_pool.close()
        _connection_pool = None


@asynccontextmanager
async def get_db_connection() -> AsyncGenerator[asyncpg.Connection, None]:
    """
    Get database connection with proper schema context
    CRITICAL: Sets search_path to faunalogic_dapi_v1
    """
    global _connection_pool
    
    if _connection_pool is None:
        await initialize_database_pool()
    
    async with _connection_pool.acquire() as connection:
        # Set proper schema context as required
        await connection.execute("SET search_path = faunalogic_dapi_v1, public")
        yield connection