"""
FaunaLogic API Service
Main application entry point
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import structlog

from app.config.database import initialize_database_pool, close_database_pool
from app.api.routes import map as map_routes

# Note: These imports will be available once the full application structure is created
# from app.config.settings import settings
# from app.api.routes import auth, users, submissions, spatial
# from app.core.exceptions import setup_exception_handlers
# from app.core.middleware import setup_middleware

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting FaunaLogic API service")
    await initialize_database_pool()
    yield
    # Shutdown
    logger.info("Shutting down FaunaLogic API service")
    await close_database_pool()


def create_application() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="FaunaLogic API",
        description="Spatial Wildlife Data Management API",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # Basic CORS setup for development
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure properly in production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Setup will be completed when full structure is implemented
    # setup_middleware(app)
    # setup_exception_handlers(app)
    
    # Include routers
    app.include_router(map_routes.router, prefix="/api/v1/map", tags=["map"])
    
    # To be implemented
    # app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
    # app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
    # app.include_router(submissions.router, prefix="/api/v1/submissions", tags=["submissions"])
    # app.include_router(spatial.router, prefix="/api/v1/spatial", tags=["spatial"])
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy", 
            "service": "faunalogic-api",
            "version": "1.0.0"
        }
    
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "message": "FaunaLogic API Service",
            "docs": "/docs",
            "health": "/health"
        }
    
    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )