"""
Spatial data models for FaunaLogic API
Pydantic models for spatial queries and responses
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, validator
from geojson_pydantic import FeatureCollection, Feature


class BoundingBox(BaseModel):
    """Bounding box for spatial queries"""
    
    min_lng: float = Field(
        ..., 
        ge=-180.0, 
        le=180.0,
        description="Minimum longitude in decimal degrees"
    )
    min_lat: float = Field(
        ..., 
        ge=-90.0, 
        le=90.0,
        description="Minimum latitude in decimal degrees"
    )
    max_lng: float = Field(
        ..., 
        ge=-180.0, 
        le=180.0,
        description="Maximum longitude in decimal degrees"
    )
    max_lat: float = Field(
        ..., 
        ge=-90.0, 
        le=90.0,
        description="Maximum latitude in decimal degrees"
    )
    limit: Optional[int] = Field(
        1000,
        ge=1,
        le=5000,
        description="Maximum number of features to return"
    )
    
    @validator('max_lng')
    def validate_longitude_order(cls, v, values):
        """Ensure max_lng is greater than min_lng"""
        if 'min_lng' in values and v <= values['min_lng']:
            raise ValueError('max_lng must be greater than min_lng')
        return v
    
    @validator('max_lat')
    def validate_latitude_order(cls, v, values):
        """Ensure max_lat is greater than min_lat"""
        if 'min_lat' in values and v <= values['min_lat']:
            raise ValueError('max_lat must be greater than min_lat')
        return v


class SpatialFeatureProperties(BaseModel):
    """Properties for spatial features"""
    
    id: int = Field(..., description="Unique identifier for the spatial component")
    submission_observation_id: int = Field(..., description="Related observation ID")
    create_date: str = Field(..., description="Creation timestamp")
    
    class Config:
        extra = "allow"  # Allow additional properties from database


class MapDataQuery(BaseModel):
    """Query parameters for map data endpoint"""
    
    min_lng: float = Field(..., ge=-180.0, le=180.0)
    min_lat: float = Field(..., ge=-90.0, le=90.0)
    max_lng: float = Field(..., ge=-180.0, le=180.0)
    max_lat: float = Field(..., ge=-90.0, le=90.0)
    limit: Optional[int] = Field(1000, ge=1, le=5000)
    
    @validator('max_lng')
    def validate_longitude_order(cls, v, values):
        if 'min_lng' in values and v <= values['min_lng']:
            raise ValueError('max_lng must be greater than min_lng')
        return v
    
    @validator('max_lat')
    def validate_latitude_order(cls, v, values):
        if 'min_lat' in values and v <= values['min_lat']:
            raise ValueError('max_lat must be greater than min_lat')
        return v


class MapDataResponse(BaseModel):
    """Response model for map data"""
    
    success: bool = Field(True, description="Operation success status")
    data: FeatureCollection = Field(..., description="GeoJSON FeatureCollection")
    total_count: Optional[int] = Field(None, description="Total features in bounding box")
    returned_count: int = Field(..., description="Number of features returned")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "type": "FeatureCollection",
                    "features": [
                        {
                            "type": "Feature",
                            "id": 1,
                            "geometry": {
                                "type": "Point",
                                "coordinates": [-123.1207, 49.2827]
                            },
                            "properties": {
                                "id": 1,
                                "submission_observation_id": 123,
                                "create_date": "2024-01-01T00:00:00Z"
                            }
                        }
                    ]
                },
                "total_count": 150,
                "returned_count": 1
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response model"""
    
    success: bool = Field(False, description="Operation success status")
    error: Dict[str, Any] = Field(..., description="Error details")
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": "Invalid bounding box parameters",
                    "details": {}
                }
            }
        }