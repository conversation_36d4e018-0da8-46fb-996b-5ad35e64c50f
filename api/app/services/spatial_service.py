"""
Spatial data service for FaunaLogic API
Handles spatial queries and data retrieval from unsecure_spatial_component table
"""

import json
from typing import Dict, List, Optional, Any
from geojson_pydantic import FeatureCollection, Feature
import structlog

from app.config.database import get_db_connection
from app.models.spatial import BoundingBox

logger = structlog.get_logger(__name__)


class SpatialService:
    """Service for handling spatial data operations"""
    
    async def get_map_data(self, bbox: BoundingBox) -> FeatureCollection:
        """
        Retrieve spatial data within bounding box from unsecure_spatial_component table
        
        Args:
            bbox: Bounding box parameters for spatial query
            
        Returns:
            FeatureCollection: GeoJSON feature collection with spatial data
        """
        logger.info(
            "Fetching map data",
            min_lng=bbox.min_lng,
            min_lat=bbox.min_lat,
            max_lng=bbox.max_lng,
            max_lat=bbox.max_lat,
            limit=bbox.limit
        )
        
        async with get_db_connection() as conn:
            # Query unsecure_spatial_component table with bounding box filter
            # Note: Geometry data is stored in SRID 3005 (BC Albers)
            # Input coordinates are in WGS84 (SRID 4326), so we transform the bounding box
            query = """
                SELECT 
                    usc.unsecure_spatial_component_id,
                    usc.submission_observation_id,
                    usc.spatial_component,
                    usc.create_date,
                    ST_AsGeoJSON(ST_Transform(usc.geometry, 4326)) as geometry_geojson
                FROM unsecure_spatial_component usc
                WHERE usc.geometry && ST_Transform(ST_MakeEnvelope($1, $2, $3, $4, 4326), 3005)
                    AND ST_Intersects(
                        usc.geometry, 
                        ST_Transform(ST_MakeEnvelope($1, $2, $3, $4, 4326), 3005)
                    )
                ORDER BY usc.create_date DESC
                LIMIT $5
            """
            
            rows = await conn.fetch(
                query,
                bbox.min_lng,
                bbox.min_lat, 
                bbox.max_lng,
                bbox.max_lat,
                bbox.limit
            )
            
            # Convert database rows to GeoJSON features
            features = []
            for row in rows:
                try:
                    # Parse geometry from PostGIS
                    geometry = json.loads(row['geometry_geojson'])
                    
                    # Create feature with properties
                    feature = Feature(
                        type="Feature",
                        id=row['unsecure_spatial_component_id'],
                        geometry=geometry,
                        properties={
                            "id": row['unsecure_spatial_component_id'],
                            "submission_observation_id": row['submission_observation_id'],
                            "create_date": row['create_date'].isoformat(),
                            "spatial_component": row['spatial_component']  # Original jsonb data
                        }
                    )
                    features.append(feature)
                    
                except (json.JSONDecodeError, KeyError) as e:
                    logger.warning(
                        "Failed to process spatial component",
                        component_id=row['unsecure_spatial_component_id'],
                        error=str(e)
                    )
                    continue
            
            logger.info("Retrieved map data", feature_count=len(features))
            
            # Create proper FeatureCollection with explicit type
            return FeatureCollection(type="FeatureCollection", features=features)
    
    async def get_map_data_count(self, bbox: BoundingBox) -> int:
        """
        Get total count of features within bounding box
        
        Args:
            bbox: Bounding box parameters for spatial query
            
        Returns:
            int: Total number of features in bounding box
        """
        async with get_db_connection() as conn:
            query = """
                SELECT COUNT(*) as total
                FROM unsecure_spatial_component usc
                WHERE usc.geometry && ST_Transform(ST_MakeEnvelope($1, $2, $3, $4, 4326), 3005)
                    AND ST_Intersects(
                        usc.geometry, 
                        ST_Transform(ST_MakeEnvelope($1, $2, $3, $4, 4326), 3005)
                    )
            """
            
            result = await conn.fetchrow(
                query,
                bbox.min_lng,
                bbox.min_lat,
                bbox.max_lng, 
                bbox.max_lat
            )
            
            return result['total'] if result else 0