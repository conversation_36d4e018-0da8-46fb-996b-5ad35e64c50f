# FaunaLogic API Service - Docker Compose
version: '3.8'

services:
  faunalogic-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: faunalogic-api
    ports:
      - "8000:8000"
    environment:
      - ENV=development
      - DEBUG=true
      - DB_HOST=faunalogic-postgres  # Connect to database container
      - DB_PORT=5432
      - DB_NAME=faunalogic
      - DB_USER=faunalogic_api
      - DB_PASSWORD=flatpass
      - SECRET_KEY=dev-secret-key-change-in-production
      - CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
    volumes:
      - ../app:/app/app
      - ../tests:/app/tests
      - ../logs:/app/logs
    networks:
      - faunalogic-network
    restart: unless-stopped
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    container_name: faunalogic-api-redis
    ports:
      - "6380:6379"  # Different port to avoid conflict with submission service
    volumes:
      - redis_data:/data
    networks:
      - faunalogic-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Optional: API documentation service
  docs:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: faunalogic-api-docs
    ports:
      - "8001:8001"
    environment:
      - ENV=development
    volumes:
      - ../:/app
    networks:
      - faunalogic-network
    command: ["mkdocs", "serve", "--dev-addr", "0.0.0.0:8001"]
    profiles:
      - docs

volumes:
  redis_data:

networks:
  faunalogic-network:
    external: true