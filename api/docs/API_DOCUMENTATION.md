# FaunaLogic API Documentation

## Overview

The FaunaLogic API is a FastAPI-based REST service providing endpoints for spatial wildlife data management. It features comprehensive CORS support, database connection pooling, and structured error handling.

**Base URL:** `http://localhost:8000`  
**Interactive Documentation:** `http://localhost:8000/docs`  
**ReDoc Documentation:** `http://localhost:8000/redoc`

## Authentication

Currently, the API operates without authentication for development. Authentication integration with Keycloak is planned.

## Common Response Format

All API responses follow a consistent structure:

### Success Response
```json
{
  "success": true,
  "data": { /* endpoint-specific data */ },
  "message": "Optional success message"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": { /* additional error context */ }
  }
}
```

## API Endpoints

### Health Check

#### GET /health
**Description:** Service health check endpoint

**Response:**
```json
{
  "status": "healthy",
  "service": "faunalogic-api",
  "version": "1.0.0"
}
```

#### GET /
**Description:** Root endpoint with service information

**Response:**
```json
{
  "message": "FaunaLogic API Service",
  "docs": "/docs",
  "health": "/health"
}
```

## Spatial Data API

### Homepage Map API

### Overview
The homepage map API provides spatial data for the application's main map interface. It retrieves data from the `unsecure_spatial_component` table based on client-provided bounding box coordinates.

### Endpoint

#### GET /api/v1/map/data

**Description:** Retrieve spatial data within a bounding box for homepage map display

**Base URL:** `http://localhost:8000`

**Full URL:** `http://localhost:8000/api/v1/map/data`

### Parameters

| Parameter | Type | Required | Min | Max | Default | Description |
|-----------|------|----------|-----|-----|---------|-------------|
| `min_lng` | float | Yes | -180.0 | 180.0 | - | Minimum longitude (western boundary) in decimal degrees |
| `min_lat` | float | Yes | -90.0 | 90.0 | - | Minimum latitude (southern boundary) in decimal degrees |
| `max_lng` | float | Yes | -180.0 | 180.0 | - | Maximum longitude (eastern boundary) in decimal degrees |
| `max_lat` | float | Yes | -90.0 | 90.0 | - | Maximum latitude (northern boundary) in decimal degrees |
| `limit` | integer | No | 1 | 5000 | 1000 | Maximum number of features to return |

### Request Examples

#### Basic Request
```bash
curl "http://localhost:8000/api/v1/map/data?min_lng=-125.0&min_lat=49.0&max_lng=-120.0&max_lat=52.0"
```

#### Request with Limit
```bash
curl "http://localhost:8000/api/v1/map/data?min_lng=-125.0&min_lat=49.0&max_lng=-120.0&max_lat=52.0&limit=100"
```

#### JavaScript/Fetch Example
```javascript
const bbox = {
  min_lng: -125.0,
  min_lat: 49.0,
  max_lng: -120.0,
  max_lat: 52.0,
  limit: 1000
};

const params = new URLSearchParams(bbox);
const response = await fetch(`http://localhost:8000/api/v1/map/data?${params}`);
const data = await response.json();
```

### Response Format

#### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "type": "FeatureCollection",
    "features": [
      {
        "type": "Feature",
        "id": 1,
        "geometry": {
          "type": "Point",
          "coordinates": [-123.1207, 49.2827]
        },
        "properties": {
          "id": 1,
          "submission_observation_id": 123,
          "create_date": "2024-01-01T00:00:00Z",
          "spatial_component": {
            "type": "Point",
            "coordinates": [-123.1207, 49.2827]
          }
        }
      }
    ]
  },
  "total_count": 150,
  "returned_count": 1
}
```

#### Error Response (422 Validation Error)
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "max_lng must be greater than min_lng",
    "details": {}
  }
}
```

#### Error Response (500 Internal Server Error)
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_ERROR",
    "message": "An unexpected error occurred while processing the request",
    "details": {}
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Indicates if the request was successful |
| `data` | FeatureCollection | GeoJSON FeatureCollection containing spatial features |
| `total_count` | integer | Total number of features available in the bounding box |
| `returned_count` | integer | Number of features returned in this response |

#### Feature Properties

Each feature in the collection includes:

| Property | Type | Description |
|----------|------|-------------|
| `id` | integer | Unique identifier for the spatial component |
| `submission_observation_id` | integer | Related observation identifier |
| `create_date` | string (ISO 8601) | Timestamp when the record was created |
| `spatial_component` | object | Original GeoJSON spatial data from database |

### Data Source

- **Table:** `unsecure_spatial_component`
- **Key Column:** `spatial_component` (jsonb containing GeoJSON objects)
- **Spatial Indexing:** Uses PostGIS geometry indexes for efficient spatial queries
- **Coordinate System:** WGS84 (EPSG:4326) for input/output, BC Albers (EPSG:3005) for storage

### Performance Notes

- Queries are optimized using spatial indexes on the `geometry` column
- Maximum limit of 5000 features per request prevents excessive payload sizes
- Results are ordered by `create_date DESC` to return most recent data first
- Spatial intersection uses `ST_Intersects` with envelope optimization

### Error Handling

#### Validation Errors (422)
- Invalid coordinate ranges (longitude: -180 to 180, latitude: -90 to 90)
- max_lng must be greater than min_lng
- max_lat must be greater than min_lat
- limit must be between 1 and 5000

#### Server Errors (500)
- Database connection failures
- Spatial query execution errors
- GeoJSON parsing errors

### Integration Notes

#### Frontend Map Integration
```javascript
// Example Leaflet.js integration
async function loadMapData(bounds) {
  const bbox = {
    min_lng: bounds.getWest(),
    min_lat: bounds.getSouth(),
    max_lng: bounds.getEast(),
    max_lat: bounds.getNorth(),
    limit: 1000
  };
  
  const params = new URLSearchParams(bbox);
  const response = await fetch(`/api/v1/map/data?${params}`);
  const result = await response.json();
  
  if (result.success) {
    // Add GeoJSON to map
    L.geoJSON(result.data).addTo(map);
  }
}
```

#### Bounding Box Calculation
For map applications, the bounding box typically corresponds to the current map viewport:
- `min_lng`: Western boundary of visible area
- `min_lat`: Southern boundary of visible area  
- `max_lng`: Eastern boundary of visible area
- `max_lat`: Northern boundary of visible area

### Testing

The API includes comprehensive test coverage:
- Valid bounding box queries
- Invalid parameter validation
- Response structure validation
- Error handling scenarios
- Performance with various limits

Run tests:
```bash
pytest tests/integration/test_homepage_map.py -v
```

## CORS Configuration

The API is configured for cross-origin requests to support frontend integration:

**Allowed Origins:**
- `http://localhost:3000` (Frontend development)
- `http://localhost:3001` (Frontend alternate port)
- `http://localhost:8080` (Keycloak authentication)

**Allowed Methods:** `GET`, `POST`, `PUT`, `DELETE`, `OPTIONS`  
**Allowed Headers:** All headers (`*`)  
**Credentials:** Supported (`true`)

## Database Integration

### Critical Requirements

⚠️ **MANDATORY:** All database operations must use the dedicated API user:

```python
# Required database configuration
DB_USER = "faunalogic_api"
DB_SCHEMA = "faunalogic_dapi_v1"
```

### Database Context

Before any database operations, the API automatically sets the database context:

```sql
SELECT faunalogic.api_set_context(
    'faunalogic_api',    -- api_user_name
    'faunalogic_api'     -- application_name
);
```

This ensures:
- Proper audit logging
- Multi-tenant data isolation  
- Security policy enforcement

## Error Handling

### HTTP Status Codes

| Status Code | Description | Usage |
|-------------|-------------|-------|
| 200 | OK | Successful requests |
| 400 | Bad Request | Invalid request format |
| 422 | Validation Error | Invalid parameter values |
| 500 | Internal Server Error | Database or application errors |

### Common Error Codes

| Error Code | Description |
|------------|-------------|
| `VALIDATION_ERROR` | Invalid request parameters |
| `DATABASE_ERROR` | Database connection or query errors |
| `INTERNAL_ERROR` | Unexpected application errors |
| `SPATIAL_ERROR` | Spatial data processing errors |

## Development and Testing

### Running Tests

```bash
# Run all tests
make test

# Run specific test types
make test-unit          # Unit tests only
make test-integration   # Integration tests only

# Watch mode for development
make test-watch
```

### OpenAPI Documentation

Interactive API documentation is available at:
- **Swagger UI:** http://localhost:8000/docs
- **ReDoc:** http://localhost:8000/redoc

## Integration Examples

### Frontend Integration (Vue.js/TypeScript)

```typescript
// api/mapService.ts
import axios from 'axios'

const API_BASE = '/api/v1'  // Uses Vite proxy in development

export const mapService = {
  async getMapData(bounds: BoundingBox) {
    const response = await axios.get(`${API_BASE}/map/data`, {
      params: bounds
    })
    return response.data?.data || { type: 'FeatureCollection', features: [] }
  }
}
```

### cURL Examples

```bash
# Health check
curl http://localhost:8000/health

# Map data with bounding box
curl "http://localhost:8000/api/v1/map/data?min_lng=-125.0&min_lat=49.0&max_lng=-120.0&max_lat=52.0&limit=100"
```

## Performance Considerations

- **Connection Pooling:** Database connections are pooled for efficiency
- **Spatial Indexing:** PostGIS spatial indexes optimize geographic queries  
- **Response Limits:** Maximum 5000 features per request prevents payload overflow
- **CORS Optimization:** Pre-flight request caching reduces latency

## Security Notes

### Development vs Production

**Current (Development):**
- Open CORS policy (`allow_origins=["*"]`)
- No authentication required
- Debug logging enabled

**Production Recommendations:**
- Restrict CORS to specific domains
- Enable authentication with Keycloak
- Configure HTTPS with proper certificates
- Implement rate limiting
- Enable audit logging