{"name": "faunalogic-api", "version": "0.0.0", "description": "API for FaunaLogic Web App", "license": "Apache-2.0", "main": "app", "repository": {"type": "git", "url": "https://github.com/bcgov/faunalogic-platform.git"}, "scripts": {"start": "ts-node src/app", "build": "tsc --project tsconfig.production.json", "start-reload": "./node_modules/.bin/nodemon src/app.ts --exec ts-node", "queue": "ts-node src/queue", "queue-reload": "./node_modules/.bin/nodemon src/queue.ts --exec ts-node", "test": "mocha", "test-watch": "mocha --watch", "coverage": "nyc mocha", "lint": "eslint . --ignore-pattern 'node_modules' --ext .ts", "lint-fix": "eslint . --fix --ignore-pattern 'node_modules' --ext .ts", "format": "prettier --loglevel=warn --check \"./src/**/*.{js,jsx,ts,tsx,css,scss}\"", "format-fix": "prettier --loglevel=warn --write \"./src/**/*.{js,jsx,ts,tsx,json,css,scss}\"", "fix": "npm-run-all -l -s lint-fix format-fix"}, "engines": {"node": ">= 20.0.0", "npm": ">= 10.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.583.0", "@aws-sdk/lib-storage": "^3.621.0", "@aws-sdk/s3-request-presigner": "^3.583.0", "adm-zip": "^0.5.5", "ajv": "^8.12.0", "axios": "^1.6.7", "clamscan": "^2.2.1", "dayjs": "^1.11.10", "db-migrate": "^0.11.11", "db-migrate-pg": "^1.2.2", "express": "^4.17.1", "express-openapi": "^9.3.0", "fast-xml-parser": "~4.1.3", "fastq": "^1.15.0", "jsonpath-plus": "^7.2.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "knex": "^2.4.2", "lodash": "^4.17.21", "logform": "^2.3.2", "mime": "^3.0.0", "multer": "^1.4.5-lts.1", "pg": "^8.7.1", "qs": "^6.10.1", "sql-template-strings": "^2.2.2", "swagger-ui-express": "^4.3.0", "typescript": "^4.7.4", "uuid": "^11.0.5", "winston": "^3.3.3", "winston-daily-rotate-file": "^5.0.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zod": "^3.23.8"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/adm-zip": "^0.4.34", "@types/chai": "^4.3.14", "@types/clamscan": "^2.0.8", "@types/express": "^4.17.13", "@types/geojson": "^7946.0.8", "@types/jsonwebtoken": "^8.5.5", "@types/lodash": "^4.14.176", "@types/mime": "^3.0.4", "@types/mocha": "^10.0.10", "@types/multer": "^1.4.11", "@types/node": "^20.17.16", "@types/pg": "^8.11.4", "@types/sinon": "^17.0.3", "@types/sinon-chai": "^3.2.12", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^8.3.1", "@types/yamljs": "^0.2.31", "@typescript-eslint/eslint-plugin": "~7.6.0", "@typescript-eslint/parser": "~7.6.0", "chai": "^4.3.4", "del": "~6.0.0", "eslint": "~8.56.0", "eslint-config-prettier": "~8.10.0", "eslint-plugin-prettier": "~4.2.1", "mocha": "^11.1.0", "nodemon": "^3.1.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "sinon": "^17.0.1", "sinon-chai": "^3.7.0", "ts-mocha": "^11.1.0", "ts-node": "^10.9.2"}}