# API Service Coding Standards

**Version**: 1.0.1  
**Last Updated**: 2025-06-29  
**Status**: 🚧 Development Ready  
**Service**: REST API Layer

## 🔐 MANDATORY: Database Access Standards

### Critical Connection Requirements
**ALL database connections MUST use the dedicated API user:**

```python
# Database Configuration (REQUIRED)
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'faunalogic',
    'user': 'faunalogic_api',  # NEVER use postgres user
    'password': 'flatpass',
    'options': '-c search_path=faunalogic_dapi_v1,public'
}

# Connection Pattern (MANDATORY)
async def get_db_connection():
    """Get database connection with proper API user setup"""
    conn = await asyncpg.connect(**DATABASE_CONFIG)
    
    # ALWAYS set schema context first
    await conn.execute("SET search_path = faunalogic_dapi_v1, public")
    
    return conn
```

### API-Only Database Access Pattern
```python
# ✅ CORRECT: Use API functions through database connection
async def get_user_data(user_id: str) -> dict:
    async with get_db_connection() as conn:
        # Set user context for audit trail
        await conn.execute("SELECT api_set_context($1, $2)", user_id, tenant_id)
        
        # Use API functions for data access
        result = await conn.fetchrow(
            "SELECT api_get_user_profile($1)", user_id
        )
        return dict(result)

# ❌ WRONG: Direct table access (not allowed for API user)
# await conn.fetchrow("SELECT * FROM application_user WHERE id = $1", user_id)
```

## Python Code Style Standards

### General Principles
- **Type Hints**: All functions must include comprehensive type hints
- **Async/Await**: Use async/await for all I/O operations
- **Pydantic Models**: Use Pydantic for data validation and serialization
- **Error Handling**: Comprehensive exception handling with specific error types
- **Security**: Input validation and sanitization with Pydantic
- **Testing**: Unit tests for all core functionality

### Project Structure
```
api/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI application entry
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py           # Application settings
│   │   └── database.py           # Database configuration
│   ├── core/
│   │   ├── __init__.py
│   │   ├── security.py           # Authentication/authorization
│   │   ├── dependencies.py       # FastAPI dependencies
│   │   └── exceptions.py         # Custom exceptions
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py              # User Pydantic models
│   │   ├── submission.py        # Submission models
│   │   └── spatial.py           # Spatial data models
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── auth.py          # Authentication endpoints
│   │   │   ├── users.py         # User management
│   │   │   ├── submissions.py   # Data submission endpoints
│   │   │   └── spatial.py       # Spatial query endpoints
│   │   └── dependencies.py      # Route dependencies
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py      # Authentication logic
│   │   ├── user_service.py      # User management logic
│   │   ├── spatial_service.py   # Spatial data operations
│   │   └── submission_service.py # Submission handling
│   └── utils/
│       ├── __init__.py
│       ├── database.py          # Database utilities
│       ├── spatial.py           # Spatial utilities
│       └── validation.py        # Validation helpers
├── tests/
├── requirements/
├── docker/
└── scripts/
```

### Naming Conventions

#### File and Directory Naming
```python
# Files: lowercase with underscores
user_service.py              # ✅ Clear and descriptive
spatial_controller.py        # ✅ Purpose-based naming
auth_middleware.py          # ✅ Functionality clear

# Avoid camelCase for files
userService.py              # ❌ Not Python convention
spatialController.py        # ❌ Mixed case not standard
```

#### Variable and Function Naming
```python
# Variables and functions: snake_case
async def authenticate_user(credentials: LoginCredentials) -> AuthResult:
    """Authenticate user with credentials"""
    user_auth_token = await generate_token(credentials.user_id)
    spatial_query_results = await perform_spatial_query(bounds)
    return AuthResult(token=user_auth_token)

# Constants: UPPER_CASE
MAX_UPLOAD_SIZE = 10 * 1024 * 1024  # 10MB
DEFAULT_PAGE_LIMIT = 50
JWT_EXPIRATION_TIME = 3600  # seconds

# Boolean variables: is/has/can/should prefixes
is_user_authenticated = await check_auth_status(token)
has_valid_permissions = check_permissions(user, required_permission)
can_access_resource = validate_resource_access(user, resource_id)
```

#### Class and Model Naming
```python
# Classes: PascalCase with descriptive names
class UserService:
    """Handle user management operations"""
    pass

class SpatialDataProcessor:
    """Process spatial data submissions"""
    pass

class DatabaseConnectionManager:
    """Manage database connections and context"""
    pass

# Pydantic Models: PascalCase
class UserProfile(BaseModel):
    """User profile data model"""
    id: UUID
    first_name: str
    last_name: str
    email_address: EmailStr

class SpatialQuery(BaseModel):
    """Spatial query parameters"""
    bounds: BoundingBox
    filters: Optional[Dict[str, Any]] = None
    limit: int = Field(default=1000, le=10000)
```

### Type Hints and Documentation

#### Function Signatures
```python
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from datetime import datetime

async def process_user_authentication(
    credentials: LoginCredentials,
    request_ip: str,
    user_agent: Optional[str] = None,
    remember_me: bool = False
) -> AuthenticationResult:
    """
    Authenticate user and create session.
    
    Args:
        credentials: User login credentials
        request_ip: IP address of the request
        user_agent: Browser user agent string
        remember_me: Whether to create persistent session
    
    Returns:
        Authentication result with token and user profile
    
    Raises:
        AuthenticationError: If credentials are invalid
        SecurityError: If account is locked or suspicious activity
        ValidationError: If input validation fails
    """
    pass

async def get_spatial_data_within_bounds(
    bounds: BoundingBox,
    user_context: UserContext,
    filters: Optional[SpatialFilters] = None,
    pagination: PaginationParams = PaginationParams()
) -> PaginatedResponse[SpatialFeature]:
    """
    Retrieve spatial data within specified geographic bounds.
    
    Applies security transformations and tenant isolation.
    
    Args:
        bounds: Geographic bounding box for query
        user_context: Current user context for permissions
        filters: Optional filters for data selection
        pagination: Pagination parameters
    
    Returns:
        Paginated spatial features as GeoJSON
    """
    pass
```

#### Pydantic Model Documentation
```python
class UserCreate(BaseModel):
    """
    User creation request model.
    
    Validates user input for creating new user accounts.
    Enforces business rules and security requirements.
    """
    
    first_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="User's first name"
    )
    last_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="User's last name"
    )
    email_address: EmailStr = Field(
        ...,
        description="Valid email address for user account"
    )
    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="Strong password meeting security requirements"
    )
    
    @validator('password')
    def validate_password_strength(cls, v):
        """Validate password meets security requirements"""
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

class SpatialSubmission(BaseModel):
    """
    Spatial data submission model.
    
    Validates spatial data submissions including geometry,
    metadata, and business context.
    """
    
    submission_type: SubmissionType = Field(
        ...,
        description="Type of spatial data submission"
    )
    geometry: GeoJSONGeometry = Field(
        ...,
        description="Valid GeoJSON geometry"
    )
    properties: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional properties for the spatial feature"
    )
    metadata: SubmissionMetadata = Field(
        ...,
        description="Submission metadata including collector and date"
    )
    
    @validator('geometry')
    def validate_geometry(cls, v):
        """Validate geometry is valid GeoJSON"""
        try:
            # Use shapely to validate geometry
            from shapely.geometry import shape
            geom = shape(v.dict())
            if not geom.is_valid:
                raise ValueError(f"Invalid geometry: {geom.is_valid_reason}")
        except Exception as e:
            raise ValueError(f"Invalid GeoJSON geometry: {str(e)}")
        return v
```

### FastAPI Application Patterns

#### Application Structure
```python
# app/main.py
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import structlog

from app.config.settings import settings
from app.config.database import database_manager
from app.api.routes import auth, users, submissions, spatial
from app.core.exceptions import setup_exception_handlers
from app.core.middleware import setup_middleware
from app.core.security import setup_security_middleware

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting FaunaLogic API service")
    await database_manager.initialize()
    yield
    # Shutdown
    logger.info("Shutting down FaunaLogic API service")
    await database_manager.close()


def create_application() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="FaunaLogic API",
        description="Spatial Wildlife Data Management API",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan,
        openapi_url="/openapi.json" if settings.DEBUG else None
    )
    
    # Setup middleware
    setup_middleware(app)
    setup_security_middleware(app)
    
    # Setup exception handlers
    setup_exception_handlers(app)
    
    # Include routers
    app.include_router(
        auth.router, 
        prefix="/api/v1/auth", 
        tags=["authentication"]
    )
    app.include_router(
        users.router, 
        prefix="/api/v1/users", 
        tags=["users"]
    )
    app.include_router(
        submissions.router, 
        prefix="/api/v1/submissions", 
        tags=["submissions"]
    )
    app.include_router(
        spatial.router, 
        prefix="/api/v1/spatial", 
        tags=["spatial"]
    )
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy", 
            "service": "faunalogic-api",
            "version": "1.0.0"
        }
    
    return app


app = create_application()
```

#### Route Definition Patterns
```python
# app/api/routes/users.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from uuid import UUID

from app.models.user import User, UserCreate, UserUpdate, UserProfile
from app.models.responses import APIResponse, PaginatedResponse
from app.services.user_service import UserService
from app.core.security import get_current_user, require_permission
from app.core.dependencies import get_user_service

router = APIRouter()


@router.get(
    "/",
    response_model=APIResponse[PaginatedResponse[User]],
    summary="List users",
    description="Retrieve a paginated list of users with optional filtering"
)
async def list_users(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum records to return"),
    search: Optional[str] = Query(None, description="Search term for user filtering"),
    current_user: User = Depends(require_permission("read_users")),
    user_service: UserService = Depends(get_user_service)
):
    """List users with pagination and filtering"""
    
    filters = UserFilters(
        search=search,
        tenant_id=current_user.tenant_id
    )
    
    pagination = PaginationParams(skip=skip, limit=limit)
    
    result = await user_service.list_users(filters, pagination)
    
    return APIResponse(success=True, data=result)


@router.get(
    "/{user_id}",
    response_model=APIResponse[UserProfile],
    summary="Get user by ID",
    description="Retrieve detailed user information by user ID"
)
async def get_user(
    user_id: UUID,
    current_user: User = Depends(require_permission("read_user")),
    user_service: UserService = Depends(get_user_service)
):
    """Get user by ID"""
    
    user = await user_service.get_user_by_id(
        str(user_id),
        current_user.tenant_id
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return APIResponse(success=True, data=user)


@router.post(
    "/",
    response_model=APIResponse[User],
    status_code=status.HTTP_201_CREATED,
    summary="Create user",
    description="Create a new user account"
)
async def create_user(
    user_create: UserCreate,
    current_user: User = Depends(require_permission("create_user")),
    user_service: UserService = Depends(get_user_service)
):
    """Create new user"""
    
    try:
        user = await user_service.create_user(
            user_create,
            current_user.tenant_id,
            current_user.id
        )
        return APIResponse(success=True, data=user)
        
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put(
    "/{user_id}",
    response_model=APIResponse[User],
    summary="Update user",
    description="Update user information"
)
async def update_user(
    user_id: UUID,
    user_update: UserUpdate,
    current_user: User = Depends(require_permission("update_user")),
    user_service: UserService = Depends(get_user_service)
):
    """Update user information"""
    
    user = await user_service.update_user(
        str(user_id),
        user_update,
        current_user.tenant_id
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return APIResponse(success=True, data=user)


@router.delete(
    "/{user_id}",
    response_model=APIResponse[None],
    summary="Delete user",
    description="Soft delete user account"
)
async def delete_user(
    user_id: UUID,
    current_user: User = Depends(require_permission("delete_user")),
    user_service: UserService = Depends(get_user_service)
):
    """Delete user (soft delete)"""
    
    success = await user_service.delete_user(
        str(user_id),
        current_user.tenant_id
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return APIResponse(success=True, data=None)
```

### Service Layer Patterns

#### Database Service Implementation
```python
# app/services/user_service.py
from typing import Optional, List
from uuid import UUID
import structlog
from passlib.context import CryptContext

from app.config.database import get_db_connection
from app.models.user import User, UserCreate, UserUpdate, UserFilters
from app.models.responses import PaginatedResponse, PaginationParams
from app.core.exceptions import ConflictError, NotFoundError, ValidationError

logger = structlog.get_logger(__name__)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserService:
    """User management service with database integration"""
    
    async def create_user(
        self,
        user_create: UserCreate,
        tenant_id: UUID,
        created_by: UUID
    ) -> User:
        """Create new user with proper validation and audit trail"""
        
        logger.info("Creating new user", email=user_create.email_address)
        
        # Check for existing user
        existing_user = await self.get_user_by_email(
            user_create.email_address,
            tenant_id
        )
        
        if existing_user:
            raise ConflictError("User with this email already exists")
        
        # Hash password
        password_hash = pwd_context.hash(user_create.password)
        
        async with get_db_connection() as conn:
            # Set database context
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            await conn.execute(
                "SELECT api_set_context($1, $2)", 
                str(created_by), str(tenant_id)
            )
            
            # Create user via API function
            result = await conn.fetchrow(
                """
                SELECT api_create_user($1, $2, $3, $4, $5) as user_id
                """,
                user_create.first_name,
                user_create.last_name,
                user_create.email_address,
                password_hash,
                str(tenant_id)
            )
            
            if not result or not result['user_id']:
                raise ValidationError("Failed to create user")
            
            user_id = result['user_id']
            
        logger.info("User created successfully", user_id=user_id)
        
        # Return created user
        return await self.get_user_by_id(user_id, tenant_id)
    
    async def get_user_by_id(
        self,
        user_id: str,
        tenant_id: UUID
    ) -> Optional[User]:
        """Get user by ID with tenant isolation"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                """
                SELECT api_get_user_profile($1, $2)
                """,
                user_id, str(tenant_id)
            )
            
            if result and result['api_get_user_profile']:
                user_data = result['api_get_user_profile']
                return User.parse_obj(user_data)
                
            return None
    
    async def get_user_by_email(
        self,
        email: str,
        tenant_id: UUID
    ) -> Optional[User]:
        """Get user by email address with tenant isolation"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                """
                SELECT api_get_user_by_email($1, $2)
                """,
                email, str(tenant_id)
            )
            
            if result and result['api_get_user_by_email']:
                user_data = result['api_get_user_by_email']
                return User.parse_obj(user_data)
                
            return None
    
    async def list_users(
        self,
        filters: UserFilters,
        pagination: PaginationParams
    ) -> PaginatedResponse[User]:
        """List users with filtering and pagination"""
        
        logger.debug("Listing users", filters=filters.dict(), pagination=pagination.dict())
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            # Build filter parameters
            filter_params = {
                'tenant_id': str(filters.tenant_id),
                'search': filters.search,
                'active_only': filters.active_only
            }
            
            # Get users and total count
            result = await conn.fetchrow(
                """
                SELECT api_list_users_paginated($1, $2, $3)
                """,
                json.dumps(filter_params),
                pagination.limit,
                pagination.skip
            )
            
            if not result:
                return PaginatedResponse(
                    items=[],
                    total=0,
                    page=pagination.page,
                    limit=pagination.limit
                )
            
            data = result['api_list_users_paginated']
            users = [User.parse_obj(user) for user in data['users']]
            
            return PaginatedResponse(
                items=users,
                total=data['total'],
                page=pagination.page,
                limit=pagination.limit
            )
    
    async def update_user(
        self,
        user_id: str,
        user_update: UserUpdate,
        tenant_id: UUID
    ) -> Optional[User]:
        """Update user information"""
        
        logger.info("Updating user", user_id=user_id)
        
        # Get current user to verify existence
        current_user = await self.get_user_by_id(user_id, tenant_id)
        if not current_user:
            return None
        
        # Build update data (only include non-None fields)
        update_data = user_update.dict(exclude_unset=True)
        
        if not update_data:
            return current_user  # No changes to make
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                """
                SELECT api_update_user($1, $2, $3)
                """,
                user_id,
                json.dumps(update_data),
                str(tenant_id)
            )
            
            if result and result['api_update_user']:
                logger.info("User updated successfully", user_id=user_id)
                return await self.get_user_by_id(user_id, tenant_id)
        
        return None
    
    async def delete_user(
        self,
        user_id: str,
        tenant_id: UUID
    ) -> bool:
        """Soft delete user"""
        
        logger.info("Deleting user", user_id=user_id)
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                """
                SELECT api_delete_user($1, $2)
                """,
                user_id, str(tenant_id)
            )
            
            if result and result['api_delete_user']:
                logger.info("User deleted successfully", user_id=user_id)
                return True
                
            return False
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(plain_password, hashed_password)
```

### Error Handling Standards

#### Custom Exception Classes
```python
# app/core/exceptions.py
from typing import Any, Optional, Dict
from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
import structlog

logger = structlog.get_logger(__name__)


class AppError(Exception):
    """Base application error"""
    
    def __init__(
        self,
        message: str,
        details: Optional[Any] = None,
        error_code: Optional[str] = None
    ):
        self.message = message
        self.details = details
        self.error_code = error_code or self.__class__.__name__
        super().__init__(self.message)


class ValidationError(AppError):
    """Raised when data validation fails"""
    pass


class AuthenticationError(AppError):
    """Raised when authentication fails"""
    pass


class AuthorizationError(AppError):
    """Raised when user lacks required permissions"""
    pass


class NotFoundError(AppError):
    """Raised when requested resource is not found"""
    pass


class ConflictError(AppError):
    """Raised when resource conflict occurs"""
    pass


class ServiceError(AppError):
    """Raised when service operation fails"""
    pass


class DatabaseError(AppError):
    """Raised when database operation fails"""
    pass


def setup_exception_handlers(app: FastAPI):
    """Setup global exception handlers"""
    
    @app.exception_handler(ValidationError)
    async def validation_error_handler(request: Request, exc: ValidationError):
        logger.warning("Validation error", error=exc.message, details=exc.details)
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details
                }
            }
        )
    
    @app.exception_handler(AuthenticationError)
    async def auth_error_handler(request: Request, exc: AuthenticationError):
        logger.warning("Authentication error", error=exc.message)
        return JSONResponse(
            status_code=401,
            content={
                "success": False,
                "error": {
                    "code": exc.error_code,
                    "message": exc.message
                }
            }
        )
    
    @app.exception_handler(AuthorizationError)
    async def authorization_error_handler(request: Request, exc: AuthorizationError):
        logger.warning("Authorization error", error=exc.message)
        return JSONResponse(
            status_code=403,
            content={
                "success": False,
                "error": {
                    "code": exc.error_code,
                    "message": exc.message
                }
            }
        )
    
    @app.exception_handler(NotFoundError)
    async def not_found_error_handler(request: Request, exc: NotFoundError):
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "error": {
                    "code": exc.error_code,
                    "message": exc.message
                }
            }
        )
    
    @app.exception_handler(ConflictError)
    async def conflict_error_handler(request: Request, exc: ConflictError):
        logger.warning("Conflict error", error=exc.message)
        return JSONResponse(
            status_code=409,
            content={
                "success": False,
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger.error(
            "Unhandled exception",
            error=str(exc),
            path=request.url.path,
            method=request.method
        )
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "An unexpected error occurred"
                }
            }
        )
```

### Testing Standards

#### Unit Test Structure
```python
# tests/unit/test_user_service.py
import pytest
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from app.services.user_service import UserService
from app.models.user import UserCreate, User
from app.core.exceptions import ConflictError, NotFoundError


class TestUserService:
    """Test suite for UserService"""
    
    @pytest.fixture
    def user_service(self):
        """Create UserService instance for testing"""
        return UserService()
    
    @pytest.fixture
    def sample_user_create(self):
        """Sample user creation data"""
        return UserCreate(
            first_name="John",
            last_name="Doe",
            email_address="<EMAIL>",
            password="SecurePass123!"
        )
    
    @pytest.fixture
    def sample_user(self):
        """Sample user instance"""
        return User(
            id=uuid4(),
            first_name="John",
            last_name="Doe",
            email_address="<EMAIL>",
            active=True,
            tenant_id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.mark.asyncio
    async def test_create_user_success(self, user_service, sample_user_create, sample_user):
        """Test successful user creation"""
        
        tenant_id = uuid4()
        created_by = uuid4()
        
        with patch('app.services.user_service.get_db_connection') as mock_db:
            # Mock database connection
            mock_conn = AsyncMock()
            mock_db.return_value.__aenter__.return_value = mock_conn
            
            # Mock no existing user found
            with patch.object(user_service, 'get_user_by_email', return_value=None):
                # Mock successful user creation
                mock_conn.fetchrow.return_value = {'user_id': str(sample_user.id)}
                
                # Mock get_user_by_id returning created user
                with patch.object(user_service, 'get_user_by_id', return_value=sample_user):
                    result = await user_service.create_user(
                        sample_user_create, tenant_id, created_by
                    )
                    
                    assert result == sample_user
                    mock_conn.execute.assert_called()
                    mock_conn.fetchrow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(self, user_service, sample_user_create, sample_user):
        """Test user creation with duplicate email"""
        
        tenant_id = uuid4()
        created_by = uuid4()
        
        # Mock existing user found
        with patch.object(user_service, 'get_user_by_email', return_value=sample_user):
            with pytest.raises(ConflictError) as exc_info:
                await user_service.create_user(sample_user_create, tenant_id, created_by)
            
            assert "already exists" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_user_by_id_not_found(self, user_service):
        """Test get user by ID when user doesn't exist"""
        
        user_id = str(uuid4())
        tenant_id = uuid4()
        
        with patch('app.services.user_service.get_db_connection') as mock_db:
            mock_conn = AsyncMock()
            mock_db.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = None
            
            result = await user_service.get_user_by_id(user_id, tenant_id)
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_update_user_success(self, user_service, sample_user):
        """Test successful user update"""
        
        user_update = UserUpdate(first_name="Jane")
        tenant_id = uuid4()
        
        with patch.object(user_service, 'get_user_by_id', return_value=sample_user):
            with patch('app.services.user_service.get_db_connection') as mock_db:
                mock_conn = AsyncMock()
                mock_db.return_value.__aenter__.return_value = mock_conn
                mock_conn.fetchrow.return_value = {'api_update_user': True}
                
                # Mock updated user
                updated_user = sample_user.copy(update={'first_name': 'Jane'})
                with patch.object(user_service, 'get_user_by_id', return_value=updated_user):
                    result = await user_service.update_user(
                        str(sample_user.id), user_update, tenant_id
                    )
                    
                    assert result.first_name == "Jane"
```

#### Integration Test Structure
```python
# tests/integration/test_user_api.py
import pytest
from httpx import AsyncClient
from fastapi import status

from app.main import app
from tests.helpers import create_test_user, create_auth_headers


@pytest.mark.asyncio
class TestUserAPI:
    """Integration tests for user API endpoints"""
    
    async def test_create_user_success(self):
        """Test successful user creation via API"""
        
        user_data = {
            "first_name": "Test",
            "last_name": "User",
            "email_address": "<EMAIL>",
            "password": "SecurePassword123!"
        }
        
        headers = await create_auth_headers(permissions=["create_user"])
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/users/",
                json=user_data,
                headers=headers
            )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"]["email_address"] == user_data["email_address"]
        assert "password" not in response_data["data"]
    
    async def test_get_user_unauthorized(self):
        """Test user access without authentication"""
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/api/v1/users/123")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_list_users_with_pagination(self):
        """Test user listing with pagination"""
        
        headers = await create_auth_headers(permissions=["read_users"])
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/users/?skip=0&limit=10",
                headers=headers
            )
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["success"] is True
        assert "data" in response_data
        assert "items" in response_data["data"]
        assert "total" in response_data["data"]
```

---

**Important**: These coding standards ensure consistency, security, and maintainability across the FaunaLogic API service. All development should follow these Python/FastAPI patterns to maintain code quality and system integration.