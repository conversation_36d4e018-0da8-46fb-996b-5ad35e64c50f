# API Service System Prompt

**Version**: 1.0.1  
**Last Updated**: 2025-06-29  
**Status**: 🚧 Development Ready  
**Service**: REST API Layer

## Service Overview

### API Service Description
The FaunaLogic API service provides RESTful endpoints for accessing the spatial wildlife data management system. Built with Python and FastAPI, it serves as the primary interface between frontend applications and the PostgreSQL/PostGIS database, handling authentication, authorization, data transformation, and spatial operations.

### Service Architecture
- **Framework**: FastAPI with async/await support
- **Language**: Python 3.11+ with type hints
- **Database**: PostgreSQL with PostGIS via asyncpg
- **Authentication**: JWT-based with Keycloak integration
- **Authorization**: Role-based access control (RBAC)
- **Spatial Processing**: Integration with PostGIS functions
- **API Standards**: RESTful API with OpenAPI/Swagger documentation
- **Performance**: Async processing with connection pooling

## 🔐 CRITICAL: Database Integration Requirements

**⚠️ ALL DATABASE CONNECTIONS MUST USE THE DEDICATED API USER:**

```python
# Database Configuration (REQUIRED)
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'faunalogic',
    'user': 'faunalogic_api',  # NEVER use postgres user
    'password': 'flatpass',
    'options': '-c search_path=faunalogic_dapi_v1,public'
}
```

**Database Access Pattern:**
```python
# ✅ CORRECT: Use API functions through database connection
async def get_user_data(user_id: str) -> dict:
    async with get_db_connection() as conn:
        # Set proper schema context
        await conn.execute("SET search_path = faunalogic_dapi_v1, public")
        
        # Use API functions for data access
        result = await conn.fetchrow(
            "SELECT api_get_user_profile($1)", user_id
        )
        return dict(result)

# ❌ WRONG: Direct table access (not allowed for API user)
# await conn.fetchrow("SELECT * FROM application_user WHERE id = $1", user_id)
```

## Technology Stack

### Core Dependencies
```python
# requirements/base.txt
fastapi>=0.104.0,<0.105.0
uvicorn[standard]>=0.24.0,<0.25.0
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# Database
asyncpg>=0.29.0,<0.30.0
databases[postgresql]>=0.8.0,<0.9.0

# Authentication  
python-jose[cryptography]>=3.3.0,<4.0.0
python-multipart>=0.0.6,<0.1.0
passlib[bcrypt]>=1.7.0,<2.0.0

# Spatial Data
shapely>=2.0.0,<3.0.0
geojson>=3.1.0,<4.0.0

# HTTP and Middleware
httpx>=0.25.0,<0.26.0
python-multipart>=0.0.6,<0.1.0

# Monitoring and Logging
structlog>=23.2.0,<24.0.0
prometheus-fastapi-instrumentator>=6.1.0,<7.0.0
```

### Development Dependencies
```python
# requirements/dev.txt
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<0.22.0
pytest-cov>=4.1.0,<5.0.0
httpx>=0.25.0,<0.26.0  # For testing async clients

# Code Quality
ruff>=0.1.0,<0.2.0          # Fast Python linter and formatter
black>=23.12.0,<24.0.0
isort>=5.13.0,<6.0.0
flake8>=6.1.0,<7.0.0        # Alternative to ruff
mypy>=1.8.0,<2.0.0
```

## Development Constraints

### API-Specific Requirements
- [ ] All endpoints must include proper authentication and authorization
- [ ] Spatial data must be returned in GeoJSON format
- [ ] API responses must follow consistent Pydantic models
- [ ] All inputs must be validated with Pydantic schemas
- [ ] Rate limiting must be implemented for public endpoints
- [ ] API documentation must be auto-generated with OpenAPI
- [ ] Error handling must provide useful but secure error messages
- [ ] Async/await must be used for all I/O operations
- [ ] Test-Driven Development (TDD) must be followed for all new features
- [ ] All functions must have comprehensive pytest test coverage (>90%)

### Security Requirements
- [ ] JWT tokens must be properly validated on all protected endpoints
- [ ] User permissions must be checked against database policies
- [ ] Spatial data must respect security transformations
- [ ] All user inputs must be validated with Pydantic
- [ ] CORS must be properly configured
- [ ] Security headers must be implemented
- [ ] Audit logging must track all API operations

### Performance Requirements
- [ ] API response times must be <500ms for simple queries
- [ ] Spatial queries must be optimized with proper indexing
- [ ] Connection pooling must be configured for database access
- [ ] Caching must be implemented for frequently accessed data
- [ ] Large datasets must be paginated
- [ ] File uploads must be handled efficiently with streaming

## Context Files

### Always Include These Files
When working on API tasks, include these files for context:

**Database Integration:**
- `database/migrations/V1_0_6__create_api_functions.sql` - API functions
- `database/prompts/system/main-system.md` - Database service context

**API Configuration:**
- `api/app/config/settings.py` - Application settings
- `api/app/config/database.py` - Database connection setup
- `api/app/core/security.py` - Authentication and security

### Project Structure
```
api/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI application entry
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py           # Application settings
│   │   └── database.py           # Database configuration
│   ├── core/
│   │   ├── __init__.py
│   │   ├── security.py           # Authentication/authorization
│   │   ├── dependencies.py       # FastAPI dependencies
│   │   └── exceptions.py         # Custom exceptions
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py              # User Pydantic models
│   │   ├── submission.py        # Submission models
│   │   └── spatial.py           # Spatial data models
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── auth.py          # Authentication endpoints
│   │   │   ├── users.py         # User management
│   │   │   ├── submissions.py   # Data submission endpoints
│   │   │   └── spatial.py       # Spatial query endpoints
│   │   └── dependencies.py      # Route dependencies
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py      # Authentication logic
│   │   ├── user_service.py      # User management logic
│   │   ├── spatial_service.py   # Spatial data operations
│   │   └── submission_service.py # Submission handling
│   └── utils/
│       ├── __init__.py
│       ├── database.py          # Database utilities
│       ├── spatial.py           # Spatial utilities
│       └── validation.py        # Validation helpers
├── tests/
│   ├── __init__.py
│   ├── conftest.py             # Test configuration
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── fixtures/               # Test data
├── requirements/
│   ├── base.txt               # Base requirements
│   ├── dev.txt                # Development requirements
│   └── prod.txt               # Production requirements
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
└── alembic/                   # Database migrations (if needed)
```

## Development Guidelines

### FastAPI Application Structure
```python
# app/main.py
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager

from app.config.settings import settings
from app.config.database import database
from app.api.routes import auth, users, submissions, spatial
from app.core.exceptions import setup_exception_handlers
from app.core.middleware import setup_middleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    await database.connect()
    yield
    # Shutdown
    await database.disconnect()


app = FastAPI(
    title="FaunaLogic API",
    description="Spatial Wildlife Data Management API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Setup middleware
setup_middleware(app)

# Setup exception handlers
setup_exception_handlers(app)

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
app.include_router(submissions.router, prefix="/api/v1/submissions", tags=["submissions"])
app.include_router(spatial.router, prefix="/api/v1/spatial", tags=["spatial"])

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "faunalogic-api"}
```

### Pydantic Models and Validation
```python
# app/models/user.py
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field
from uuid import UUID


class UserBase(BaseModel):
    """Base user model"""
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    email_address: EmailStr


class UserCreate(UserBase):
    """User creation model"""
    password: str = Field(..., min_length=8, max_length=100)


class UserUpdate(BaseModel):
    """User update model"""
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    email_address: Optional[EmailStr] = None


class User(UserBase):
    """User response model"""
    id: UUID
    active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserProfile(User):
    """Extended user profile"""
    tenant_id: UUID
    permissions: list[str] = []
    last_login_at: Optional[datetime] = None
```

### Authentication and Authorization
```python
# app/core/security.py
from datetime import datetime, timedelta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext

from app.config.settings import settings
from app.models.user import User
from app.services.user_service import UserService

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token handling
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    
    return encoded_jwt


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_service: UserService = Depends()
) -> User:
    """Get current authenticated user"""
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    user = await user_service.get_user_by_id(user_id)
    if user is None:
        raise credentials_exception
        
    return user


async def require_permission(permission: str):
    """Dependency to require specific permission"""
    def permission_checker(current_user: User = Depends(get_current_user)):
        if permission not in current_user.permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission required: {permission}"
            )
        return current_user
    
    return permission_checker
```

### Database Service Layer
```python
# app/services/user_service.py
from typing import Optional, List
from uuid import UUID

from app.config.database import get_db_connection
from app.models.user import User, UserCreate, UserUpdate
from app.core.security import get_password_hash


class UserService:
    """User management service"""
    
    async def create_user(self, user_create: UserCreate, tenant_id: UUID) -> User:
        """Create new user"""
        
        # Hash password
        hashed_password = get_password_hash(user_create.password)
        
        async with get_db_connection() as conn:
            # Set database context
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            # Create user via API function
            result = await conn.fetchrow(
                "SELECT api_create_user($1, $2, $3, $4, $5)",
                user_create.first_name,
                user_create.last_name,
                user_create.email_address,
                hashed_password,
                str(tenant_id)
            )
            
            if not result:
                raise ValueError("Failed to create user")
                
            return await self.get_user_by_id(result['user_id'])
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                "SELECT api_get_user_profile($1)", user_id
            )
            
            if result:
                return User(**dict(result))
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                "SELECT api_get_user_by_email($1)", email
            )
            
            if result:
                return User(**dict(result))
            return None
    
    async def update_user(self, user_id: str, user_update: UserUpdate) -> Optional[User]:
        """Update user information"""
        
        # Build update data
        update_data = user_update.dict(exclude_unset=True)
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                "SELECT api_update_user($1, $2)",
                user_id,
                json.dumps(update_data)
            )
            
            if result:
                return await self.get_user_by_id(user_id)
            return None
    
    async def list_users(
        self, 
        skip: int = 0, 
        limit: int = 100,
        tenant_id: Optional[UUID] = None
    ) -> List[User]:
        """List users with pagination"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            results = await conn.fetch(
                "SELECT api_list_users($1, $2, $3)",
                skip, limit, str(tenant_id) if tenant_id else None
            )
            
            return [User(**dict(row)) for row in results]
```

### Spatial Data Handling
```python
# app/services/spatial_service.py
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from geojson import FeatureCollection, Feature
import json

from app.config.database import get_db_connection
from app.models.spatial import BoundingBox, SpatialQuery


class SpatialService:
    """Spatial data operations service"""
    
    async def get_points_within_bounds(
        self,
        bounds: BoundingBox,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 1000,
        offset: int = 0
    ) -> FeatureCollection:
        """Get spatial points within bounding box"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            # Use API function for spatial query
            results = await conn.fetch(
                """
                SELECT api_get_spatial_points_within_bounds($1, $2, $3, $4, $5, $6)
                """,
                bounds.min_lng,
                bounds.min_lat,
                bounds.max_lng,
                bounds.max_lat,
                limit,
                offset
            )
            
            features = []
            for row in results:
                feature_data = dict(row)
                
                # Parse geometry from PostGIS
                geometry = json.loads(feature_data['geometry'])
                
                feature = Feature(
                    id=feature_data['id'],
                    geometry=geometry,
                    properties=feature_data['properties']
                )
                features.append(feature)
            
            return FeatureCollection(features)
    
    async def perform_spatial_query(
        self,
        query: SpatialQuery
    ) -> FeatureCollection:
        """Perform complex spatial query"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            # Convert query to database parameters
            query_params = {
                'geometry': json.dumps(query.geometry.dict()) if query.geometry else None,
                'distance': query.distance,
                'filters': json.dumps(query.filters) if query.filters else None,
                'limit': query.limit or 1000,
                'offset': query.offset or 0
            }
            
            results = await conn.fetch(
                "SELECT api_perform_spatial_query($1)",
                json.dumps(query_params)
            )
            
            return self._format_spatial_results(results)
    
    async def get_spatial_statistics(self, tenant_id: str) -> Dict[str, Any]:
        """Get spatial data statistics for tenant"""
        
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                "SELECT api_get_spatial_statistics($1)", tenant_id
            )
            
            return dict(result) if result else {}
    
    def _format_spatial_results(self, results) -> FeatureCollection:
        """Format database results as GeoJSON FeatureCollection"""
        features = []
        
        for row in results:
            row_data = dict(row)
            geometry = json.loads(row_data['geometry'])
            
            feature = Feature(
                id=row_data['id'],
                geometry=geometry,
                properties=row_data.get('properties', {})
            )
            features.append(feature)
        
        return FeatureCollection(features)
```

### API Route Definitions
```python
# app/api/routes/users.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from uuid import UUID

from app.models.user import User, UserCreate, UserUpdate, UserProfile
from app.services.user_service import UserService
from app.core.security import get_current_user, require_permission
from app.core.responses import APIResponse

router = APIRouter()


@router.get("/", response_model=APIResponse[List[User]])
async def list_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(require_permission("read_users")),
    user_service: UserService = Depends()
):
    """List users with pagination"""
    
    users = await user_service.list_users(
        skip=skip, 
        limit=limit,
        tenant_id=current_user.tenant_id
    )
    
    return APIResponse(success=True, data=users)


@router.get("/{user_id}", response_model=APIResponse[UserProfile])
async def get_user(
    user_id: UUID,
    current_user: User = Depends(require_permission("read_user")),
    user_service: UserService = Depends()
):
    """Get user by ID"""
    
    user = await user_service.get_user_by_id(str(user_id))
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return APIResponse(success=True, data=user)


@router.post("/", response_model=APIResponse[User], status_code=status.HTTP_201_CREATED)
async def create_user(
    user_create: UserCreate,
    current_user: User = Depends(require_permission("create_user")),
    user_service: UserService = Depends()
):
    """Create new user"""
    
    try:
        user = await user_service.create_user(user_create, current_user.tenant_id)
        return APIResponse(success=True, data=user)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{user_id}", response_model=APIResponse[User])
async def update_user(
    user_id: UUID,
    user_update: UserUpdate,
    current_user: User = Depends(require_permission("update_user")),
    user_service: UserService = Depends()
):
    """Update user information"""
    
    user = await user_service.update_user(str(user_id), user_update)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return APIResponse(success=True, data=user)
```

### Standard API Response Model
```python
# app/core/responses.py
from typing import TypeVar, Generic, Optional, Any
from pydantic import BaseModel


T = TypeVar('T')


class PaginationInfo(BaseModel):
    """Pagination information"""
    page: int
    limit: int
    total: int
    total_pages: int


class APIError(BaseModel):
    """API error structure"""
    code: str
    message: str
    details: Optional[Any] = None


class APIResponse(BaseModel, Generic[T]):
    """Standard API response wrapper"""
    success: bool
    data: Optional[T] = None
    error: Optional[APIError] = None
    pagination: Optional[PaginationInfo] = None


def success_response(data: T, pagination: Optional[PaginationInfo] = None) -> APIResponse[T]:
    """Create success response"""
    return APIResponse(success=True, data=data, pagination=pagination)


def error_response(code: str, message: str, details: Any = None) -> APIResponse[None]:
    """Create error response"""
    return APIResponse(
        success=False,
        error=APIError(code=code, message=message, details=details)
    )
```

## Testing Standards (Test-Driven Development)

### TDD Workflow
Follow strict Test-Driven Development practices:

1. **Red Phase**: Write a failing test that describes the desired behavior
2. **Green Phase**: Write the minimal code to make the test pass
3. **Refactor Phase**: Improve the code while maintaining test coverage

### TDD Best Practices
- **Write tests first** before any implementation code
- **Use descriptive test names** that explain the expected behavior
- **Keep tests small and focused** on a single behavior
- **Use pytest fixtures** for consistent test data setup
- **Mock external dependencies** to isolate units under test
- **Aim for >90% test coverage** with meaningful tests
- **Use pytest-watch** for continuous testing during development

### Unit Test Structure
```python
# tests/unit/test_user_service.py
import pytest
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from app.services.user_service import UserService
from app.models.user import UserCreate


class TestUserService:
    """Test suite for UserService"""
    
    @pytest.fixture
    def user_service(self):
        """Create UserService instance for testing"""
        return UserService()
    
    @pytest.fixture
    def sample_user_create(self):
        """Sample user creation data"""
        return UserCreate(
            first_name="John",
            last_name="Doe",
            email_address="<EMAIL>",
            password="SecurePass123!"
        )
    
    @pytest.mark.asyncio
    async def test_create_user_success(self, user_service, sample_user_create):
        """Test successful user creation"""
        
        tenant_id = uuid4()
        user_id = uuid4()
        
        with patch('app.services.user_service.get_db_connection') as mock_db:
            # Mock database connection
            mock_conn = AsyncMock()
            mock_db.return_value.__aenter__.return_value = mock_conn
            
            # Mock API function response
            mock_conn.fetchrow.return_value = {'user_id': str(user_id)}
            
            # Mock get_user_by_id for the return value
            with patch.object(user_service, 'get_user_by_id') as mock_get_user:
                mock_user = {
                    'id': user_id,
                    'first_name': 'John',
                    'last_name': 'Doe',
                    'email_address': '<EMAIL>',
                    'active': True
                }
                mock_get_user.return_value = mock_user
                
                result = await user_service.create_user(sample_user_create, tenant_id)
                
                # Verify API function was called
                mock_conn.fetchrow.assert_called_once()
                assert result == mock_user
    
    @pytest.mark.asyncio
    async def test_get_user_by_email_not_found(self, user_service):
        """Test get user by email when user doesn't exist"""
        
        with patch('app.services.user_service.get_db_connection') as mock_db:
            mock_conn = AsyncMock()
            mock_db.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = None
            
            result = await user_service.get_user_by_email("<EMAIL>")
            
            assert result is None
```

### Integration Test Structure
```python
# tests/integration/test_user_api.py
import pytest
from httpx import AsyncClient
from fastapi import status

from app.main import app


@pytest.mark.asyncio
class TestUserAPI:
    """Integration tests for user API endpoints"""
    
    async def test_create_user_success(self, auth_headers, test_tenant_id):
        """Test successful user creation via API"""
        
        user_data = {
            "first_name": "Test",
            "last_name": "User",
            "email_address": "<EMAIL>",
            "password": "SecurePassword123!"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/users/",
                json=user_data,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"]["email_address"] == user_data["email_address"]
        assert "password" not in response_data["data"]  # Password should not be returned
    
    async def test_get_user_unauthorized(self):
        """Test user access without authentication"""
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/api/v1/users/123")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_list_users_with_pagination(self, auth_headers):
        """Test user listing with pagination"""
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/users/?skip=0&limit=10",
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["success"] is True
        assert isinstance(response_data["data"], list)
        assert len(response_data["data"]) <= 10
```

---

**Important**: This API service serves as the primary interface for the FaunaLogic system. All spatial data operations, security policies, and user interactions must be carefully implemented with proper authentication, authorization, and performance optimization using Python/FastAPI best practices.