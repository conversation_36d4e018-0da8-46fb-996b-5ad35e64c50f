# FaunaLogic API Service - Base Requirements
# Python 3.11+ required

# Core Web Framework
fastapi>=0.104.0,<0.105.0
uvicorn[standard]>=0.24.0,<0.25.0

# Data Validation and Settings
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0
email-validator>=2.1.0,<3.0.0

# Database
asyncpg>=0.29.0,<0.30.0
databases[postgresql]>=0.8.0,<0.9.0

# Authentication and Security
python-jose[cryptography]>=3.3.0,<4.0.0
passlib[bcrypt]>=1.7.0,<2.0.0
python-multipart>=0.0.6,<0.1.0

# Spatial Data
shapely>=2.0.0,<3.0.0
geojson>=3.1.0,<4.0.0
geojson-pydantic>=1.0.0,<2.0.0

# HTTP Client
httpx>=0.25.0,<0.26.0

# Environment and Configuration
python-dotenv>=1.0.0,<2.0.0

# Logging and Monitoring
structlog>=23.2.0,<24.0.0
prometheus-fastapi-instrumentator>=6.1.0,<7.0.0

# Date and Time
python-dateutil>=2.8.0,<3.0.0

# Utilities
click>=8.1.0,<9.0.0
rich>=13.7.0,<14.0.0