# FaunaLogic API Service - Development Requirements
-r base.txt

# Testing
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<0.22.0
pytest-cov>=4.1.0,<5.0.0
pytest-mock>=3.12.0,<4.0.0
pytest-xdist>=3.5.0,<4.0.0
factory-boy>=3.3.0,<4.0.0
faker>=20.1.0,<21.0.0

# Code Quality
black>=23.12.0,<24.0.0
isort>=5.13.0,<6.0.0
flake8>=6.1.0,<7.0.0
mypy>=1.8.0,<2.0.0
pre-commit>=3.6.0,<4.0.0

# Development Tools
ipython>=8.18.0,<9.0.0
ipdb>=0.13.0,<0.14.0
watchfiles>=0.21.0,<0.22.0

# Documentation
mkdocs>=1.5.0,<2.0.0
mkdocs-material>=9.5.0,<10.0.0

# Local Development
psycopg2-binary>=2.9.0,<3.0.0  # For direct PostgreSQL access during development