import express, { NextFunction, Request, Response } from 'express';
import { initialize } from 'express-openapi';
import multer from 'multer';
import { OpenAPIV3 } from 'openapi-types';
import swaggerUIExperss from 'swagger-ui-express';
import { defaultPoolConfig, initDBPool } from './database/db';
import { initDBConstants } from './database/db-constants';
import { ensureHTTPError, HTTP400, HTTP500 } from './errors/http-error';
import { rootAPIDoc } from './openapi/root-api-doc';
import { authenticateRequest, authenticateRequestOptional } from './request-handlers/security/authentication';
import { initRequestStorage } from './utils/async-request-storage';
import { scanFileForVirus } from './utils/file-utils';
import { getLogger } from './utils/logger';

const defaultLog = getLogger('app');

const HOST = process.env.API_HOST;
const PORT = Number(process.env.API_PORT);

// Max size of the body of the request (bytes)
const MAX_REQ_BODY_SIZE = Number(process.env.MAX_REQ_BODY_SIZE) || 52428800;
// Max number of files in a single request
const MAX_UPLOAD_NUM_FILES = Number(process.env.MAX_UPLOAD_NUM_FILES) || 10;
// Max size of a single file (bytes)
const MAX_UPLOAD_FILE_SIZE = Number(process.env.MAX_UPLOAD_FILE_SIZE) || 52428800;

// Get initial express app
const app: express.Express = express();

// Enable CORS
app.use(function (req: Request, res: Response, next: NextFunction) {
  res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization, responseType');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, PATCH, DELETE, HEAD');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Cache-Control', 'no-store');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  defaultLog.debug({ message: 'request', label: 'api-middleware', method: req.method, url: req.url });

  next();
});

// Initialize express-openapi framework
const openAPIFramework = initialize({
  apiDoc: {
    ...(rootAPIDoc as OpenAPIV3.Document), // base open api spec
    'x-express-openapi-additional-middleware': getAdditionalMiddleware(),
    'x-express-openapi-validation-strict': true
  },
  app: app, // express app to initialize
  paths: './src/paths', // base folder for endpoint routes
  pathsIgnore: new RegExp('.(spec|test)$'), // ignore test files in paths
  routesGlob: '**/*.{ts,js}', // updated default to allow .ts
  routesIndexFileRegExp: /(?:index)?\.[tj]s$/, // updated default to allow .ts
  promiseMode: true, // allow endpoint handlers to return promises
  docsPath: '/raw-api-docs', // path to view raw openapi spec
  consumesMiddleware: {
    'application/json': express.json({ limit: MAX_REQ_BODY_SIZE }),
    'multipart/form-data': function (req, res, next) {
      const multerRequestHandler = multer({
        storage: multer.memoryStorage(), // TODO change to local/PVC storage and stream file uploads to S3?
        limits: { fileSize: MAX_UPLOAD_FILE_SIZE }
      }).array('media', MAX_UPLOAD_NUM_FILES);

      /**
       * Multer transforms and moves the incoming files from `req.body.media` --> `req.files`.
       *
       * OpenAPI only allows validation on specific parts of the request object (requestBody / parameters...) this excludes the contents of `req.files`.
       * To get around this we re-assign `req.body.media` to the Multer transformed files stored in `req.files`.
       *
       * Files can be accessed via `req.body.media` OR `req.files`.
       *
       * @see https://www.npmjs.com/package/express-openapi#argsconsumesmiddleware
       */
      multerRequestHandler(req, res, async (error?: any) => {
        if (error) {
          return next(error);
        }

        // Scan files for malicious content, if enabled
        const virusScanPromises = (req.files as Express.Multer.File[]).map(async function (file) {
          const isSafe = await scanFileForVirus(file);

          if (!isSafe) {
            throw new HTTP400('Malicious file content detected.', [{ file_name: file.originalname }]);
          }
        });

        try {
          await Promise.all(virusScanPromises);
        } catch (error) {
          // If a virus is detected, return error and do not continue
          return next(error);
        }

        // Ensure `req.files` or `req.body.media` is always set to an array
        const multerFiles = req.files ?? [];

        req.files = multerFiles;
        req.body = { ...req.body, media: multerFiles };

        return next();
      });
    },
    'application/x-www-form-urlencoded': express.urlencoded({ limit: MAX_REQ_BODY_SIZE, extended: true })
  },
  securityHandlers: {
    Bearer: async function (req: any) {
      // authenticates the request bearer token, for endpoints that specify `Bearer` security
      return authenticateRequest(req);
    },
    OptionalBearer: async function (req: any) {
      // authenticates the request bearer token, if one exists, for endpoints that specify `OptionalBearer` security
      return authenticateRequestOptional(req);
    }
  },
  errorTransformer: function (_, ajvError: object): object {
    // Transform openapi-request-validator or openapi-response-validator errors
    return ajvError;
  },
  // If `next` is not included express will silently skip calling the `errorMiddleware` entirely.
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  errorMiddleware: function (error, req, res, next) {
    defaultLog.error({
      label: 'errorMiddleware',
      message: 'error',
      error,
      req_url: `${req.method} ${req.url}`,
      req_params: req.params,
      req_body: req.body
    });

    // Ensure all errors (intentionally thrown or not) are in the same format as specified by the schema
    const httpError = ensureHTTPError(error);

    if (res.headersSent) {
      // response has already been sent
      return;
    }

    res
      .status(httpError.status)
      .json({ name: httpError.name, status: httpError.status, message: httpError.message, errors: httpError.errors });
  }
});

// Path to view beautified openapi spec
app.use('/api-docs', swaggerUIExperss.serve, swaggerUIExperss.setup(openAPIFramework.apiDoc));

// Start api
try {
  initDBPool(defaultPoolConfig);
  initDBConstants();

  app.listen(PORT, () => {
    defaultLog.info({ label: 'start api', message: `started api on ${HOST}:${PORT}/api` });
  });
} catch (error) {
  defaultLog.error({ label: 'start api', message: 'error', error });
  process.exit(1);
}

/**
 * Get additional middleware to apply to all routes.
 *
 * @return {*}  {express.RequestHandler[]}
 */
function getAdditionalMiddleware(): express.RequestHandler[] {
  const additionalMiddleware = [];

  // Initialize the request storage for each request
  additionalMiddleware.push(initRequestStorage);

  if (process.env.API_RESPONSE_VALIDATION_ENABLED === 'true') {
    // Validate endpoint responses against openapi spec
    additionalMiddleware.push(validateAllResponses);
  }

  return additionalMiddleware;
}

/**
 * Middleware to apply openapi response validation to all routes.
 *
 * Note: validates `<data>` sent via `res.status(<status>).json(<data>)` against the matching openapi response schema
 * for `<status>`.
 *
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 */
function validateAllResponses(req: Request, res: Response, next: NextFunction) {
  const isStrictValidation = !!req['apiDoc']['x-express-openapi-validation-strict'] || false;

  if (typeof res['validateResponse'] === 'function') {
    const json = res.json;

    res.json = (...args) => {
      if (res.get('x-express-openapi-validation-error-for')) {
        // Already validated this response once, skip validation and return
        return json.apply(res, args);
      }

      const reqBody = args[0];

      // Run openapi response validation function
      const validationResult: { message: any; errors: any[] } | undefined = res['validateResponse'](
        res.statusCode,
        reqBody
      );

      let validationMessage = '';
      let errorList = [];

      if (validationResult?.errors) {
        validationMessage = `Invalid response for status code ${res.statusCode}`;

        errorList = Array.from(validationResult.errors);

        // Set to avoid a loop, and to provide the original status code
        res.set('x-express-openapi-validation-error-for', res.statusCode.toString());
      }

      if (!isStrictValidation || !validationResult?.errors) {
        return json.apply(res, args);
      } else {
        defaultLog.debug({
          label: 'validateAllResponses',
          message: validationMessage,
          error: errorList,
          req_url: `${req.method} ${req.url}`,
          req_params: req.params,
          req_body: req.body,
          res_body: reqBody
        });

        throw new HTTP500(validationMessage, errorList);
      }
    };
  }

  next();
}
