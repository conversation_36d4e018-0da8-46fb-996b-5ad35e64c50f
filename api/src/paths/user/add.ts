import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { Operation } from 'express-openapi';
import { SYSTEM_IDENTITY_SOURCE } from '../../constants/database';
import { SYSTEM_ROLE } from '../../constants/roles';
import { getDBConnection } from '../../database/db';
import { HTTP400 } from '../../errors/http-error';
import { defaultErrorResponses } from '../../openapi/schemas/http-responses';
import { authorizeRequestHandler } from '../../request-handlers/security/authorization';
import { UserService } from '../../services/user-service';
import { getLogger } from '../../utils/logger';

const defaultLog = getLogger('paths/user/add');

export const POST: Operation = [
  authorizeRequestHandler(() => {
    return {
      and: [
        {
          validSystemRoles: [SYSTEM_ROLE.SYSTEM_ADMIN],
          discriminator: 'SystemRole'
        }
      ]
    };
  }),
  addSystemRoleUser()
];

POST.apiDoc = {
  description: 'Add a new system user with role.',
  tags: ['user'],
  security: [
    {
      Bearer: []
    }
  ],
  requestBody: {
    description: 'Add system user request object.',
    content: {
      'application/json': {
        schema: {
          title: 'User Response Object',
          type: 'object',
          required: ['userIdentifier', 'identitySource', 'roleId'],
          properties: {
            userGuid: {
              type: 'string',
              description: 'The GUID for the user.'
            },
            userIdentifier: {
              type: 'string',
              description: 'The identifier for the user.'
            },
            identitySource: {
              type: 'string',
              enum: [
                SYSTEM_IDENTITY_SOURCE.IDIR,
                SYSTEM_IDENTITY_SOURCE.BCEID_BASIC,
                SYSTEM_IDENTITY_SOURCE.BCEID_BUSINESS
              ]
            },
            roleId: {
              type: 'number',
              minimum: 1
            }
          }
        }
      }
    }
  },
  responses: {
    200: {
      description: 'Add system user OK.'
    },
    ...defaultErrorResponses
  }
};

/**
 * Add a system user by its user identifier and role.
 *
 * @returns {RequestHandler}
 */
export function addSystemRoleUser(): RequestHandler {
  return async (req, res) => {
    const connection = getDBConnection(req['keycloak_token']);

    const userGuid = req.body?.userGuid || null;
    const userIdentifier = req.body?.userIdentifier || null;
    const identitySource = req.body?.identitySource || null;

    const roleId = req.body?.roleId || null;

    if (!userGuid) {
      throw new HTTP400('Missing required body param: userGuid');
    }

    if (!userIdentifier) {
      throw new HTTP400('Missing required body param: userIdentifier');
    }

    if (!identitySource) {
      throw new HTTP400('Missing required body param: identitySource');
    }

    if (!roleId) {
      throw new HTTP400('Missing required body param: roleId');
    }

    try {
      await connection.open();

      const userService = new UserService(connection);

      const userObject = await userService.ensureSystemUser(userGuid, userIdentifier, identitySource);

      if (userObject) {
        await userService.addUserSystemRoles(userObject.system_user_id, [roleId]);
      }

      await connection.commit();

      return res.status(200).send();
    } catch (error) {
      defaultLog.error({ label: 'getUser', message: 'error', error });
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  };
}
