import chai, { expect } from 'chai';
import { describe } from 'mocha';
import sinon from 'sinon';
import sinon<PERSON>hai from 'sinon-chai';
import * as db from '../../../database/db';
import { ApiGeneralError } from '../../../errors/api-error';
import { UserService } from '../../../services/user-service';
import { getMockDBConnection, getRequestHandlerMocks } from '../../../__mocks__/db';
import * as list from './list';

chai.use(sinonChai);

describe('roles', () => {
  describe('getRoleList', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should rollback on error', async () => {
      const dbConnectionObj = getMockDBConnection({
        commit: sinon.stub(),
        rollback: sinon.stub(),
        release: sinon.stub()
      });

      sinon.stub(db, 'getDBConnection').returns(dbConnectionObj);

      const { mockReq, mockRes, mockNext } = getRequestHandlerMocks();

      sinon.stub(UserService.prototype, 'getRoles').throws('error' as unknown as ApiGeneralError);

      try {
        const requestHandler = list.getRoleList();

        await requestHandler(mockReq, mockRes, mockNext);
        expect.fail();
      } catch (actualError) {
        expect(dbConnectionObj.commit).to.not.be.called;
        expect(dbConnectionObj.rollback).to.be.calledOnce;
        expect(dbConnectionObj.release).to.be.calledOnce;
      }
    });

    it('should return rows on success', async () => {
      const mockDBConnection = getMockDBConnection();

      const { mockReq, mockRes, mockNext } = getRequestHandlerMocks();

      sinon.stub(db, 'getDBConnection').returns(mockDBConnection);

      const mockResponse = [
        {
          system_role_id: 1,
          name: 'admin'
        }
      ];

      sinon.stub(UserService.prototype, 'getRoles').resolves(mockResponse);

      const requestHandler = list.getRoleList();

      await requestHandler(mockReq, mockRes, mockNext);

      expect(mockRes.jsonValue).to.eql(mockResponse);
    });
  });
});
