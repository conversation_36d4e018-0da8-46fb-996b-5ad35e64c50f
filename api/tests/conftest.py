"""
Test configuration and fixtures for FaunaLogic API Service
"""

import pytest
import asyncio
from typing import Async<PERSON>enerator
from httpx import Async<PERSON>lient
from unittest.mock import AsyncMock

from app.main import app


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def client() -> AsyncGenerator[AsyncClient, None]:
    """Create test client for API testing"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_db_connection():
    """Mock database connection for testing"""
    mock_conn = AsyncMock()
    mock_conn.execute = AsyncMock()
    mock_conn.fetch = AsyncMock()
    mock_conn.fetchrow = AsyncMock()
    mock_conn.fetchval = AsyncMock()
    return mock_conn


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "first_name": "Test",
        "last_name": "User", 
        "email_address": "<EMAIL>",
        "password": "SecurePass123!"
    }