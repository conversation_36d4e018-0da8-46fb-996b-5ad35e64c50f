"""
Integration tests for homepage map API endpoint
Following TDD - Red phase: Write failing tests first
"""

import pytest
from httpx import AsyncClient
from fastapi import status

from app.main import app


@pytest.mark.asyncio
class TestHomepageMapAPI:
    """Test suite for homepage map API endpoint"""
    
    async def test_get_map_data_with_valid_bounding_box(self):
        """Test successful retrieval of map data with valid bounding box"""
        
        # Valid bounding box for BC region
        bounding_box = {
            "min_lng": -125.0,
            "min_lat": 49.0,
            "max_lng": -120.0,
            "max_lat": 52.0
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/map/data",
                params=bounding_box
            )
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["success"] is True
        assert "data" in response_data
        assert response_data["data"]["type"] == "FeatureCollection"
        assert "features" in response_data["data"]
        assert isinstance(response_data["data"]["features"], list)
    
    async def test_get_map_data_with_invalid_bounding_box(self):
        """Test error handling with invalid bounding box parameters"""
        
        # Invalid bounding box - missing required parameters
        invalid_bbox = {
            "min_lng": -125.0,
            "min_lat": 49.0
            # Missing max_lng and max_lat
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/map/data",
                params=invalid_bbox
            )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    async def test_get_map_data_with_out_of_bounds_coordinates(self):
        """Test error handling with coordinates outside valid range"""
        
        # Invalid coordinates (longitude out of range)
        invalid_bbox = {
            "min_lng": -200.0,  # Invalid longitude
            "min_lat": 49.0,
            "max_lng": -120.0,
            "max_lat": 52.0
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/map/data",
                params=invalid_bbox
            )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    async def test_get_map_data_response_structure(self):
        """Test that response follows expected GeoJSON structure"""
        
        bounding_box = {
            "min_lng": -125.0,
            "min_lat": 49.0,
            "max_lng": -120.0,
            "max_lat": 52.0
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/map/data",
                params=bounding_box
            )
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        geojson_data = response_data["data"]
        
        # Validate GeoJSON structure
        assert geojson_data["type"] == "FeatureCollection"
        assert "features" in geojson_data
        
        # If features exist, validate feature structure
        if geojson_data["features"]:
            feature = geojson_data["features"][0]
            assert feature["type"] == "Feature"
            assert "geometry" in feature
            assert "properties" in feature
            assert "id" in feature
    
    async def test_get_map_data_with_limit_parameter(self):
        """Test that limit parameter controls number of results"""
        
        bounding_box = {
            "min_lng": -125.0,
            "min_lat": 49.0,
            "max_lng": -120.0,
            "max_lat": 52.0,
            "limit": 5
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/map/data",
                params=bounding_box
            )
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        features = response_data["data"]["features"]
        assert len(features) <= 5