"""
Integration tests for homepage map API endpoint with mocked database
Testing the full API flow without requiring database connection
"""

import pytest
from unittest.mock import AsyncMock, patch
from httpx import AsyncClient
from fastapi import status
import json
from datetime import datetime

from app.main import app


@pytest.mark.asyncio
class TestHomepageMapAPIMocked:
    """Test suite for homepage map API endpoint with mocked database"""
    
    async def test_get_map_data_with_mocked_database(self):
        """Test successful retrieval of map data with mocked database response"""
        
        # Mock database response
        mock_db_rows = [
            {
                'unsecure_spatial_component_id': 1,
                'submission_observation_id': 123,
                'spatial_component': {"type": "Point", "coordinates": [-123.1207, 49.2827]},
                'create_date': datetime(2024, 1, 1, 12, 0, 0),
                'geometry_geojson': json.dumps({
                    "type": "Point", 
                    "coordinates": [-123.1207, 49.2827]
                })
            },
            {
                'unsecure_spatial_component_id': 2,
                'submission_observation_id': 124,
                'spatial_component': {"type": "Point", "coordinates": [-123.1208, 49.2828]},
                'create_date': datetime(2024, 1, 2, 12, 0, 0),
                'geometry_geojson': json.dumps({
                    "type": "Point", 
                    "coordinates": [-123.1208, 49.2828]
                })
            }
        ]
        
        # Mock the database connection and query result
        with patch('app.config.database.get_db_connection') as mock_db_conn:
            mock_conn = AsyncMock()
            mock_db_conn.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = mock_db_rows
            mock_conn.fetchrow.return_value = {'total': 2}
            
            # Valid bounding box for BC region
            bounding_box = {
                "min_lng": -125.0,
                "min_lat": 49.0,
                "max_lng": -120.0,
                "max_lat": 52.0
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    "/api/v1/map/data",
                    params=bounding_box
                )
            
            assert response.status_code == status.HTTP_200_OK
            
            response_data = response.json()
            assert response_data["success"] is True
            assert "data" in response_data
            assert response_data["data"]["type"] == "FeatureCollection"
            assert "features" in response_data["data"]
            assert len(response_data["data"]["features"]) == 2
            assert response_data["total_count"] == 2
            assert response_data["returned_count"] == 2
            
            # Verify feature structure
            feature = response_data["data"]["features"][0]
            assert feature["type"] == "Feature"
            assert "geometry" in feature
            assert "properties" in feature
            assert feature["geometry"]["type"] == "Point"
            assert feature["properties"]["id"] == 1
    
    async def test_get_map_data_empty_result(self):
        """Test API response when no spatial data is found"""
        
        # Mock empty database response
        with patch('app.config.database.get_db_connection') as mock_db_conn:
            mock_conn = AsyncMock()
            mock_db_conn.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = []  # No results
            mock_conn.fetchrow.return_value = {'total': 0}
            
            bounding_box = {
                "min_lng": -125.0,
                "min_lat": 49.0,
                "max_lng": -120.0,
                "max_lat": 52.0
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    "/api/v1/map/data",
                    params=bounding_box
                )
            
            assert response.status_code == status.HTTP_200_OK
            
            response_data = response.json()
            assert response_data["success"] is True
            assert response_data["data"]["type"] == "FeatureCollection"
            assert len(response_data["data"]["features"]) == 0
            assert response_data["total_count"] == 0
            assert response_data["returned_count"] == 0
    
    async def test_get_map_data_with_limit(self):
        """Test that limit parameter works correctly"""
        
        # Create more mock data than the limit
        mock_db_rows = [
            {
                'unsecure_spatial_component_id': i,
                'submission_observation_id': 100 + i,
                'spatial_component': {"type": "Point", "coordinates": [-123.1207 + i * 0.001, 49.2827]},
                'create_date': datetime(2024, 1, 1, 12, 0, 0),
                'geometry_geojson': json.dumps({
                    "type": "Point", 
                    "coordinates": [-123.1207 + i * 0.001, 49.2827]
                })
            }
            for i in range(3)  # Create 3 features but limit to 2
        ]
        
        with patch('app.config.database.get_db_connection') as mock_db_conn:
            mock_conn = AsyncMock()
            mock_db_conn.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = mock_db_rows[:2]  # Return only 2 due to limit
            mock_conn.fetchrow.return_value = {'total': 3}  # But total count is 3
            
            bounding_box = {
                "min_lng": -125.0,
                "min_lat": 49.0,
                "max_lng": -120.0,
                "max_lat": 52.0,
                "limit": 2
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    "/api/v1/map/data",
                    params=bounding_box
                )
            
            assert response.status_code == status.HTTP_200_OK
            
            response_data = response.json()
            assert response_data["success"] is True
            assert len(response_data["data"]["features"]) == 2
            assert response_data["total_count"] == 3
            assert response_data["returned_count"] == 2
    
    async def test_database_error_handling(self):
        """Test API error handling when database connection fails"""
        
        with patch('app.config.database.get_db_connection') as mock_db_conn:
            # Mock database connection error
            mock_db_conn.side_effect = Exception("Database connection failed")
            
            bounding_box = {
                "min_lng": -125.0,
                "min_lat": 49.0,
                "max_lng": -120.0,
                "max_lat": 52.0
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    "/api/v1/map/data",
                    params=bounding_box
                )
            
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            
            response_data = response.json()
            assert "success" in response_data
            assert response_data["success"] is False
            assert "error" in response_data
            assert response_data["error"]["code"] == "INTERNAL_ERROR"