"""
Unit tests for spatial models
Testing Pydantic model validation without database dependencies
"""

import pytest
from pydantic import ValidationError
from geojson_pydantic import FeatureCollection

from app.models.spatial import BoundingBox, MapDataQuery, MapDataResponse


class TestBoundingBox:
    """Test BoundingBox model validation"""
    
    def test_valid_bounding_box(self):
        """Test valid bounding box creation"""
        bbox = BoundingBox(
            min_lng=-125.0,
            min_lat=49.0,
            max_lng=-120.0,
            max_lat=52.0,
            limit=1000
        )
        
        assert bbox.min_lng == -125.0
        assert bbox.min_lat == 49.0
        assert bbox.max_lng == -120.0
        assert bbox.max_lat == 52.0
        assert bbox.limit == 1000
    
    def test_invalid_longitude_order(self):
        """Test validation error when max_lng <= min_lng"""
        with pytest.raises(ValidationError) as exc_info:
            BoundingBox(
                min_lng=-120.0,
                min_lat=49.0,
                max_lng=-125.0,  # Invalid: less than min_lng
                max_lat=52.0
            )
        
        assert "max_lng must be greater than min_lng" in str(exc_info.value)
    
    def test_invalid_latitude_order(self):
        """Test validation error when max_lat <= min_lat"""
        with pytest.raises(ValidationError) as exc_info:
            BoundingBox(
                min_lng=-125.0,
                min_lat=52.0,   # Invalid: greater than max_lat
                max_lng=-120.0,
                max_lat=49.0
            )
        
        assert "max_lat must be greater than min_lat" in str(exc_info.value)
    
    def test_coordinate_range_validation(self):
        """Test coordinate range validation"""
        # Test longitude out of range
        with pytest.raises(ValidationError):
            BoundingBox(
                min_lng=-200.0,  # Invalid: < -180
                min_lat=49.0,
                max_lng=-120.0,
                max_lat=52.0
            )
        
        # Test latitude out of range
        with pytest.raises(ValidationError):
            BoundingBox(
                min_lng=-125.0,
                min_lat=-100.0,  # Invalid: < -90
                max_lng=-120.0,
                max_lat=52.0
            )
    
    def test_limit_validation(self):
        """Test limit parameter validation"""
        # Test valid limit
        bbox = BoundingBox(
            min_lng=-125.0,
            min_lat=49.0,
            max_lng=-120.0,
            max_lat=52.0,
            limit=500
        )
        assert bbox.limit == 500
        
        # Test limit too small
        with pytest.raises(ValidationError):
            BoundingBox(
                min_lng=-125.0,
                min_lat=49.0,
                max_lng=-120.0,
                max_lat=52.0,
                limit=0  # Invalid: < 1
            )
        
        # Test limit too large
        with pytest.raises(ValidationError):
            BoundingBox(
                min_lng=-125.0,
                min_lat=49.0,
                max_lng=-120.0,
                max_lat=52.0,
                limit=10000  # Invalid: > 5000
            )


class TestMapDataResponse:
    """Test MapDataResponse model"""
    
    def test_valid_response_creation(self):
        """Test creating valid MapDataResponse"""
        feature_collection = FeatureCollection(type="FeatureCollection", features=[])
        
        response = MapDataResponse(
            success=True,
            data=feature_collection,
            total_count=100,
            returned_count=0
        )
        
        assert response.success is True
        assert response.data.type == "FeatureCollection"
        assert response.total_count == 100
        assert response.returned_count == 0
    
    def test_response_with_features(self):
        """Test response with actual features"""
        from geojson_pydantic import Feature, Point
        
        # Create a sample feature
        point = Point(type="Point", coordinates=[-123.1207, 49.2827])
        feature = Feature(
            type="Feature",
            geometry=point,
            properties={"id": 1, "name": "Test Point"}
        )
        
        feature_collection = FeatureCollection(type="FeatureCollection", features=[feature])
        
        response = MapDataResponse(
            success=True,
            data=feature_collection,
            total_count=1,
            returned_count=1
        )
        
        assert len(response.data.features) == 1
        assert response.data.features[0].geometry.type == "Point"
        assert response.data.features[0].properties["name"] == "Test Point"