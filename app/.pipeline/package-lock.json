{"name": "pipeline", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "pipeline", "version": "1.0.0", "license": "Apache-2.0", "dependencies": {"debug": "^4.2.0", "lodash.isempty": "^4.0.1", "lodash.isfunction": "^3.0.9", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "pipeline-cli": "git+https://github.com/bcgov/faunalogic-pipeline-cli.git"}, "devDependencies": {"prettier": "~2.3.2"}, "engines": {"node": ">= 20.0.0", "npm": ">= 10.0.0"}}, "node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/lodash.isempty": {"version": "4.4.0", "license": "MIT"}, "node_modules/lodash.isfunction": {"version": "3.0.9", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "license": "MIT"}, "node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/pipeline-cli": {"name": "@bcgov/pipeline-cli", "version": "1.0.1", "resolved": "git+ssh://**************/bcgov/faunalogic-pipeline-cli.git#61b44543041da3697d189beb2472539fe2e099c1", "license": "Apache-2.0", "dependencies": {"debug": "^4.2.0", "lodash.isempty": "^4.0.1", "lodash.isfunction": "^3.0.9", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1"}}, "node_modules/prettier": {"version": "2.3.2", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}}}}