kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: faunalogic-platform-app-dc
  labels:
    build: faunalogic-platform-app
parameters:
  - name: NAMESPACE
    description: Openshift namespace name
    value: ''
  - name: BASE_IMAGE_REGISTRY_URL
    description: The base image registry URL
    value: image-registry.openshift-image-registry.svc:5000
  - name: NAME
    value: faunalogic-platform-app
  - name: SUFFIX
    value: 'dev'
  - name: VERSION
    value: '1.0'
  - name: HOST
  - name: CHANGE_ID
    value: '0'
  - name: REACT_APP_API_HOST
    description: API host for application backend
    value: ''
  - name: REACT_APP_SITEMINDER_LOGOUT_URL
    description: Siteminder URL to log out and clear the session for the logged in user
    value: ''
  - name: REACT_APP_MAX_UPLOAD_NUM_FILES
    description: Default maximum number of files that can be uploaded at a time vai the upload component UI.
    value: '10'
  - name: REACT_APP_MAX_UPLOAD_FILE_SIZE
    description: Default maximum size of a single file that can be uploaded by the upload component UI.
    value: '52428800'
  - name: NODE_ENV
    description: NODE_ENV specification variable
    value: 'development'
  - name: REACT_APP_NODE_ENV
    description: NODE_ENV specification variable for React app
    value: 'development'
  - name: APP_PORT_DEFAULT
    description: Application default port
    value: '7100'
  - name: APP_PORT_DEFAULT_NAME
    description: Default port resource name
    value: '7100-tcp'
  - name: REACT_APP_KEYCLOAK_HOST
    description: Key clock login url
    required: true
  - name: REACT_APP_KEYCLOAK_REALM
    description: Realm identifier or name
    required: true
  - name: REACT_APP_KEYCLOAK_CLIENT_ID
    description: Client Id for application
    required: true
  - name: CPU_REQUEST
    value: '50m'
  - name: CPU_LIMIT
    value: '200m'
  - name: MEMORY_REQUEST
    value: '50Mi'
  - name: MEMORY_LIMIT
    value: '200Mi'
  - name: REPLICAS
    value: '1'
  - name: REPLICAS_MAX
    value: '1'
objects:
  - kind: ImageStream
    apiVersion: image.openshift.io/v1
    metadata:
      annotations:
        description: Nodejs Runtime Image
      labels:
        shared: 'true'
      generation: 0
      name: ${NAME}
    spec:
      lookupPolicy:
        local: false

  - kind: Deployment
    apiVersion: apps/v1
    metadata:
      annotations:
        openshift.io/generated-by: OpenShiftWebConsole
      generation: 0
      labels:
        role: app
      name: ${NAME}${SUFFIX}
    spec:
      replicas: ${{REPLICAS}}
      revisionHistoryLimit: 10
      selector:
        matchLabels:
          deployment: ${NAME}${SUFFIX}
      strategy:
        type: RollingUpdate
        rollingUpdate:
          maxSurge: 25%
          maxUnavailable: 25%
      template:
        metadata:
          annotations: null
          labels:
            deployment: ${NAME}${SUFFIX}
            role: app
        spec:
          containers:
            - name: app
              env:
                - name: CHANGE_VERSION
                  value: ${CHANGE_ID}
                - name: REACT_APP_API_HOST
                  value: ${REACT_APP_API_HOST}
                - name: REACT_APP_SITEMINDER_LOGOUT_URL
                  value: ${REACT_APP_SITEMINDER_LOGOUT_URL}
                - name: REACT_APP_MAX_UPLOAD_NUM_FILES
                  value: ${REACT_APP_MAX_UPLOAD_NUM_FILES}
                - name: REACT_APP_MAX_UPLOAD_FILE_SIZE
                  value: ${REACT_APP_MAX_UPLOAD_FILE_SIZE}
                - name: NODE_ENV
                  value: ${NODE_ENV}
                - name: REACT_APP_NODE_ENV
                  value: ${REACT_APP_NODE_ENV}
                - name: VERSION
                  value: ${VERSION}
                - name: REACT_APP_KEYCLOAK_HOST
                  value: ${REACT_APP_KEYCLOAK_HOST}
                - name: REACT_APP_KEYCLOAK_CLIENT_ID
                  value: ${REACT_APP_KEYCLOAK_CLIENT_ID}
                - name: REACT_APP_KEYCLOAK_REALM
                  value: ${REACT_APP_KEYCLOAK_REALM}
              image: ${BASE_IMAGE_REGISTRY_URL}/${NAMESPACE}/${NAME}:${VERSION}
              imagePullPolicy: Always
              ports:
                - containerPort: ${{APP_PORT_DEFAULT}}
                  protocol: TCP
              resources:
                requests:
                  cpu: ${CPU_REQUEST}
                  memory: ${MEMORY_REQUEST}
                limits:
                  cpu: ${CPU_LIMIT}
                  memory: ${MEMORY_LIMIT}
              startupProbe:
                httpGet:
                  path: /
                  port: ${{APP_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 30
                periodSeconds: 10
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 30
              readinessProbe:
                httpGet:
                  path: /
                  port: ${{APP_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              livenessProbe:
                httpGet:
                  path: /
                  port: ${{APP_PORT_DEFAULT}}
                  scheme: HTTP
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
          dnsPolicy: ClusterFirst
          restartPolicy: Always
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
          volumes:
            - emptyDir: {}
              name: ${NAME}${SUFFIX}
      test: false

  - kind: Service
    apiVersion: v1
    metadata:
      annotations: null
      labels: {}
      name: ${NAME}${SUFFIX}
    spec:
      ports:
        - name: ${NAME}-${APP_PORT_DEFAULT_NAME}
          port: ${{APP_PORT_DEFAULT}}
          protocol: TCP
          targetPort: ${{APP_PORT_DEFAULT}}
      selector:
        deployment: ${NAME}${SUFFIX}
      sessionAffinity: None
      type: ClusterIP

  - kind: Route
    apiVersion: route.openshift.io/v1
    metadata:
      annotations: {}
      labels: {}
      name: ${NAME}${SUFFIX}
    spec:
      host: ${HOST}
      tls:
        insecureEdgeTerminationPolicy: Redirect
        termination: edge
      port:
        targetPort: ${NAME}-${APP_PORT_DEFAULT_NAME}
      to:
        kind: Service
        name: ${NAME}${SUFFIX}
        weight: 100
      wildcardPolicy: None

  # Disable the HPA for now, as it is preferrable to run an exact number of pods (e.g. min:2, max:2)
  # - kind: HorizontalPodAutoscaler
  #   apiVersion: autoscaling/v2
  #   metadata:
  #     annotations: {}
  #     labels: {}
  #     name: ${NAME}${SUFFIX}
  #   spec:
  #     minReplicas: ${{REPLICAS}}
  #     maxReplicas: ${{REPLICAS_MAX}}
  #     scaleTargetRef:
  #       kind: Deployment
  #       apiVersion: apps/v1
  #       name: ${NAME}${SUFFIX}
  #     metrics:
  #       - type: Resource
  #         resource:
  #           name: cpu
  #           target:
  #             type: Utilization
  #             averageUtilization: 80
