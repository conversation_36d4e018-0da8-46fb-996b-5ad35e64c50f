{"name": "faunalogic-app", "version": "0.0.0", "description": "FaunaLogic Web App", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/bcgov/faunalogic-platform.git"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-scripts test --ci --watchAll=false --runInBand", "test-watch": "react-scripts test", "coverage": "react-scripts test --ci --coverage --testResultsProcessor jest-sonar-reporter --watchAll=false --runInBand", "update-snapshots": "react-scripts test --ci --watchAll=false --updateSnapshot", "lint": "eslint src/ --ext .jsx,.js,.ts,.tsx", "lint-fix": "npm run lint -- --fix", "format": "prettier --loglevel=warn --check \"./src/**/*.{js,jsx,ts,tsx,css,scss}\"", "format-fix": "prettier --loglevel=warn --write \"./src/**/*.{js,jsx,ts,tsx,json,css,scss}\"", "fix": "npm-run-all -l -s lint-fix format-fix"}, "engines": {"node": ">= 20.0.0", "npm": ">= 10.0.0"}, "dependencies": {"@babel/core": "^7.21.8", "@bcgov/bc-sans": "^1.0.1", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@mui/icons-material": "^5.15.15", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.15", "@mui/system": "^5.15.15", "@mui/x-data-grid": "^6.19.4", "@react-keycloak/web": "^3.4.0", "@react-leaflet/core": "^2.1.0", "@swc/core": "^1.11.7", "@tmcw/togeojson": "^4.2.0", "@turf/bbox": "^6.5.0", "@turf/boolean-equal": "^6.5.0", "@turf/intersect": "^6.5.0", "@turf/simplify": "^6.5.0", "axios": "^1.6.8", "buffer": "^6.0.3", "clsx": "^1.1.1", "dayjs": "^1.11.10", "dompurify": "^2.4.0", "express": "^4.19.2", "formik": "^2.4.1", "fuse.js": "^7.0.0", "jest-watch-typeahead": "^2.2.2", "jszip": "^3.10.1", "keycloak-js": "^20.0.2", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-fullscreen": "^1.0.2", "leaflet.locatecontrol": "^0.79.0", "lodash-es": "^4.17.21", "oidc-client-ts": "^2.4.0", "qs": "^6.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^11.3.2", "react-leaflet": "^4.2.1", "react-number-format": "^4.5.2", "react-oidc-context": "^2.3.1", "react-router": "^5.3.3", "react-router-dom": "^5.3.3", "react-window": "^1.8.6", "reproj-helper": "^1.3.1", "shapefile": "^0.6.6", "shpjs": "^5.0.2", "request": "^2.88.2", "typescript": "^4.7.4", "uuid": "^11.0.5", "yup": "^0.32.9"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-typescript": "^7.23.3", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.2", "@testing-library/user-event": "^14.5.2", "@types/dompurify": "^2.3.4", "@types/geojson": "^7946.0.14", "@types/jest": "^29.5.12", "@types/leaflet": "^1.9.7", "@types/leaflet-draw": "^1.0.9", "@types/leaflet-fullscreen": "^1.0.8", "@types/lodash-es": "^4.17.4", "@types/node": "^20.17.16", "@types/qs": "^6.9.5", "@types/react": "^18.2.74", "@types/react-dom": "^18.2.23", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@types/react-window": "^1.8.2", "@types/shapefile": "^0.6.4", "@types/shpjs": "^3.4.0", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "~7.6.0", "@typescript-eslint/parser": "~7.6.0", "assert": "^2.1.0", "axios-mock-adapter": "^1.22.0", "eslint": "~8.56.0", "eslint-config-prettier": "~8.10.0", "eslint-plugin-prettier": "~4.2.1", "eslint-plugin-react-hooks": "~4.6.0", "fs-constants": "^1.0.0", "fs-extra": "^11.1.1", "jest": "^29.7.0", "jest-sonar-reporter": "^2.0.0", "npm-run-all": "^4.1.5", "path-browserify": "^1.0.1", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "react-app-rewired": "^2.2.1", "react-scripts": "^5.0.1", "sass": "^1.83.4", "stream-browserify": "^3.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!lodash-es)/"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!<rootDir>/src/themes/**", "!<rootDir>/src/constants/**", "!<rootDir>/src/App.tsx", "!<rootDir>/src/types/**", "!<rootDir>/src/interfaces/**", "!<rootDir>/src/AppRouter.tsx", "!<rootDir>/src/react-app-env.d.ts", "!<rootDir>/src/index.tsx", "!<rootDir>/node_modules/**", "!<rootDir>/coverage/**", "!<rootDir>/public/**", "!<rootDir>/build/**", "!<rootDir>/src/serviceWorker.**", "!<rootDir>/src/setupTests.*"], "coverageThreshold": {"global": {"branches": 0, "functions": 0, "lines": 0, "statements": 0}}, "coverageReporters": ["text", "lcov"]}}