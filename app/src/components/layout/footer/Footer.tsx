import Box from '@mui/material/Box';
import useTheme from '@mui/material/styles/useTheme';
import Toolbar from '@mui/material/Toolbar';
import React from 'react';

const useStyles = () => {
  const theme = useTheme();

  return {
    appFooter: {
      backgroundColor: theme.palette.primary.main
    },
    appFooterToolbar: {
      minHeight: '46px',
      '& ul': {
        display: 'flex',
        flexWrap: 'wrap',
        alignItems: 'center',
        margin: 0,
        padding: 0,
        listStyleType: 'none'
      },
      '& li + li ': {
        marginLeft: theme.spacing(2),
        paddingLeft: theme.spacing(2),
        borderLeft: '1px solid #4b5e7e'
      },
      '& a': {
        color: '#ffffff',
        textDecoration: 'none'
      },
      '& a:hover': {
        textDecoration: 'underline'
      }
    }
  };
};

const Footer: React.FC = () => {
  const classes = useStyles();

  return (
    <footer>
      <Box sx={classes.appFooter}>
        <Toolbar component={'nav'} sx={classes.appFooterToolbar} role="navigation" aria-label="Footer">
          <ul>
            <li>
              <a href="https://www.gov.bc.ca/gov/content/home/<USER>">Disclaimer</a>
            </li>
            <li>
              <a href="https://www.gov.bc.ca/gov/content/home/<USER>">Privacy</a>
            </li>
            <li>
              <a href="https://www.gov.bc.ca/gov/content/home/<USER>">Accessibility</a>
            </li>
            <li>
              <a href="https://www.gov.bc.ca/gov/content/home/<USER>">Copyright</a>
            </li>
          </ul>
        </Toolbar>
      </Box>
    </footer>
  );
};

export default Footer;
