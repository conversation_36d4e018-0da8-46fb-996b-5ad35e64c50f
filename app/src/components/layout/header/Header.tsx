import { mdiMenu } from '@mdi/js';
import Icon from '@mdi/react';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import headerImageLarge from 'assets/images/gov-bc-logo-horiz.png';
import headerImageSmall from 'assets/images/gov-bc-logo-vert.png';
import { SYSTEM_ROLE } from 'constants/roles';
import { AuthGuard, SystemRoleGuard, UnAuthGuard } from 'guards/Guards';
import { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { LoggedInUser, PublicViewUser } from './UserControls';

const Header: React.FC<React.PropsWithChildren> = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const [open, setOpen] = useState(false);
  const menuOpen = Boolean(anchorEl);

  // Support Dialog
  const showSupportDialog = () => {
    setOpen(true);
    hideMobileMenu();
  };

  const hideSupportDialog = () => {
    setOpen(false);
  };

  // Responsive Menu
  const showMobileMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const hideMobileMenu = () => {
    setAnchorEl(null);
  };

  const BetaLabel = () => {
    return <span aria-label="This application is currently in beta phase of development">Beta</span>;
  };

  // Unauthenticated public view
  const AppBrand = () => {
    return (
      <Box
        sx={{
          '& a': {
            display: 'flex',
            alignItems: 'center',
            overflow: 'hidden',
            color: '#fff',
            fontSize: { xs: '16px', lg: '18px' },
            fontWeight: '400',
            textDecoration: 'none'
          },
          '& img': {
            mr: 2
          }
        }}>
        <RouterLink to="/" aria-label="Go to FaunaLogic Home">
          <picture>
            <source srcSet={headerImageLarge} media="(min-width: 1200px)"></source>
            <source srcSet={headerImageSmall} media="(min-width: 600px)"></source>
            <img src={headerImageSmall} alt={'Government of British Columbia'} />
          </picture>
          <Typography component="span" sx={{ fontSize: '1.25rem', fontWeight: 700 }}>
            FaunaLogic BC
            <Box
              component="sup"
              sx={{
                marginLeft: '4px',
                color: '#fcba19',
                fontSize: '0.75rem',
                fontWeight: 400,
                textTransform: 'uppercase'
              }}>
              <BetaLabel />
            </Box>
          </Typography>
        </RouterLink>
      </Box>
    );
  };

  return (
    <>
      <AppBar
        position="relative"
        elevation={0}
        sx={{
          fontFamily: 'BCSans, Verdana, Arial, sans-serif',
          backgroundColor: '#003366',
          borderBottom: '3px solid #fcba19'
        }}>
        <Toolbar
          sx={{
            height: { xs: '60px', lg: '80px' }
          }}>
          {/* Responsive Menu */}
          <Box display={{ sm: 'flex', lg: 'none' }} justifyContent="space-between" alignItems="center" flex="1 1 auto">
            <Box
              sx={{
                '& a': {
                  display: 'flex',
                  color: '#fff',
                  fontSize: '18px',
                  fontWeight: '400'
                }
              }}>
              <AppBrand></AppBrand>
            </Box>

            <Box>
              <UnAuthGuard>
                <PublicViewUser />
              </UnAuthGuard>
              <Button
                color="inherit"
                startIcon={<Icon path={mdiMenu} size={1.25}></Icon>}
                sx={{
                  ml: 2,
                  fontSize: '16px',
                  fontWeight: 700,
                  textTransform: 'none'
                }}
                aria-controls={menuOpen ? 'mobileMenu' : undefined}
                aria-haspopup="true"
                aria-expanded={menuOpen ? 'true' : undefined}
                onClick={showMobileMenu}>
                Menu
              </Button>
              <Menu
                id="mobileMenu"
                anchorEl={anchorEl}
                open={menuOpen}
                onClose={hideMobileMenu}
                MenuListProps={{
                  'aria-labelledby': 'basic-button'
                }}
                sx={{
                  '& a': {
                    color: '#1a5a96',
                    borderRadius: 0,
                    fontWeight: 700,
                    textDecoration: 'none',
                    outline: 'none'
                  },
                  '& button': {
                    color: '#1a5a96',
                    fontWeight: 700
                  }
                }}>
                <MenuItem
                  tabIndex={1}
                  component={RouterLink}
                  to="/"
                  id="menu_home_sm"
                  data-testid="collapsed_home-header-item">
                  Home
                </MenuItem>
                <SystemRoleGuard validSystemRoles={[SYSTEM_ROLE.SYSTEM_ADMIN]}>
                  <MenuItem
                    tabIndex={1}
                    component={RouterLink}
                    to="/admin/dashboard"
                    id="menu_dashboard_sm"
                    onClick={hideMobileMenu}
                    data-testid="collapsed_submissions-header-item">
                    Submissions
                  </MenuItem>
                </SystemRoleGuard>
                <SystemRoleGuard validSystemRoles={[SYSTEM_ROLE.SYSTEM_ADMIN]}>
                  <MenuItem
                    id="menu_admin_users_sm"
                    component={RouterLink}
                    to="/admin/users"
                    onClick={hideMobileMenu}
                    data-testid="collapsed_manage-users-header-item">
                    Manage Users
                  </MenuItem>
                </SystemRoleGuard>
                <MenuItem
                  component="button"
                  onClick={showSupportDialog}
                  sx={{ width: '100%' }}
                  data-testid="collapsed_support-header-item">
                  Support
                </MenuItem>
                <AuthGuard>
                  <LoggedInUser />
                </AuthGuard>
              </Menu>
            </Box>
          </Box>

          {/* Desktop Menu */}
          <Box
            display={{ xs: 'none', lg: 'flex' }}
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%">
            <Box display="flex" flexDirection="row" alignItems="center">
              <Box
                sx={{
                  '& a': {
                    display: 'flex',
                    alignItems: 'center',
                    color: '#fff',
                    fontSize: '18px',
                    fontWeight: '400',
                    textDecoration: 'none'
                  }
                }}>
                <AppBrand></AppBrand>
              </Box>
              <Box
                ml={4}
                display="flex"
                alignItems="center"
                sx={{
                  '& a': {
                    p: 1,
                    color: 'inherit',
                    fontWeight: 700,
                    lineHeight: 1.75,
                    textDecoration: 'none'
                  },
                  '& a + a': {
                    ml: 1
                  },
                  '& button': {
                    fontSize: '16px',
                    fontWeight: 700,
                    textTransform: 'none'
                  }
                }}>
                <RouterLink to="/" id="menu_home" data-testid="home-header-item">
                  Home
                </RouterLink>
                <SystemRoleGuard validSystemRoles={[SYSTEM_ROLE.SYSTEM_ADMIN]}>
                  <RouterLink to="/admin/dashboard" id="menu_dashboard" data-testid="submissions-header-item">
                    Submissions
                  </RouterLink>
                </SystemRoleGuard>
                <SystemRoleGuard validSystemRoles={[SYSTEM_ROLE.SYSTEM_ADMIN]}>
                  <RouterLink to="/admin/users" id="menu_admin_users" data-testid="manage-users-header-item">
                    Manage Users
                  </RouterLink>
                </SystemRoleGuard>
                <Button
                  color="inherit"
                  variant="text"
                  disableElevation
                  onClick={showSupportDialog}
                  sx={{
                    m: '8px',
                    p: 1
                  }}
                  data-testid="support-header-item">
                  Support
                </Button>
              </Box>
            </Box>
            <Box flex="0 0 auto">
              <UnAuthGuard>
                <PublicViewUser />
              </UnAuthGuard>
              <AuthGuard>
                <LoggedInUser />
              </AuthGuard>
            </Box>
          </Box>
        </Toolbar>
      </AppBar>

      <Dialog open={open}>
        <DialogTitle>Contact Support</DialogTitle>
        <DialogContent>
          <Typography variant="body1" component="div" color="textSecondary">
            For technical support or questions about this application, please email &zwnj;
            <a href="mailto:<EMAIL>?subject=Support Request - FaunaLogic"><EMAIL></a>
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" color="primary" onClick={hideSupportDialog}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default Header;
