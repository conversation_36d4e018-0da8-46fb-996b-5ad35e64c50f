import useFaunaLogicUserWrapper, { IFaunaLogicUserWrapper } from 'hooks/useFaunaLogicUserWrapper';
import React from 'react';
import { AuthContextProps, useAuth } from 'react-oidc-context';

export interface IAuthState {
  /**
   * The logged in user's Keycloak information.
   *
   * @type {AuthContextProps}
   * @memberof IAuthState
   */
  auth: AuthContextProps;
  /**
   * The logged in user's FaunaLogic user information.
   *
   * @type {IFaunaLogicUserWrapper}
   * @memberof IAuthState
   */
  faunalogicUserWrapper: IFaunaLogicUserWrapper;
}

export const AuthStateContext = React.createContext<IAuthState | undefined>(undefined);

/**
 * Provides access to user and authentication (keycloak) data about the logged in user.
 *
 * @param {*} props
 * @return {*}
 */
export const AuthStateContextProvider: React.FC<React.PropsWithChildren> = (props) => {
  const auth = useAuth();

  const faunalogicUserWrapper = useFaunaLogicUserWrapper();

  return (
    <AuthStateContext.Provider
      value={{
        auth,
        faunalogicUserWrapper
      }}>
      {props.children}
    </AuthStateContext.Provider>
  );
};
