import CircularProgress from '@mui/material/CircularProgress';
import { useAuthStateContext } from 'hooks/useAuthStateContext';
import { useRedirectUri } from 'hooks/useRedirect';
import { useEffect } from 'react';
import { hasAuthParams } from 'react-oidc-context';
import { Redirect, Route, RouteProps, useLocation } from 'react-router';
import { buildUrl } from 'utils/Utils';

/**
 * Route guard that requires the user to be authenticated and registered with BioHub.
 *
 * @param {RouteProps} props
 * @return {*}
 */
export const AuthenticatedRouteGuard = (props: RouteProps) => {
  const { children, ...rest } = props;

  const authStateContext = useAuthStateContext();

  const location = useLocation();

  useEffect(() => {
    if (
      !authStateContext.auth.isLoading &&
      !hasAuthParams() &&
      !authStateContext.auth.isAuthenticated &&
      !authStateContext.auth.activeNavigator
    ) {
      // User is not authenticated and has no active authentication navigator, redirect to the keycloak login page
      authStateContext.auth.signinRedirect({ redirect_uri: buildUrl(window.location.origin, location.pathname) });
    }
  }, [authStateContext.auth, location.pathname]);

  if (
    authStateContext.auth.isLoading ||
    authStateContext.faunalogicUserWrapper.isLoading ||
    !authStateContext.auth.isAuthenticated
  ) {
    return <CircularProgress className="pageProgress" data-testid={'authenticated-route-guard-spinner'} />;
  }

  if (!authStateContext.faunalogicUserWrapper.systemUserId) {
    // Redirect to forbidden page
    return <Redirect to="/forbidden" />;
  }

  // The user is a registered system user
  return <Route {...rest}>{children}</Route>;
};

/**
 * Route guard that requires the user to not be authenticated.
 *
 * @param {RouteProps} props
 * @return {*}
 */
export const UnAuthenticatedRouteGuard = (props: RouteProps) => {
  const { children, ...rest } = props;

  const authStateContext = useAuthStateContext();

  const redirectUri = useRedirectUri('/');

  if (authStateContext.auth.isAuthenticated) {
    /**
     * If the user happens to be authenticated, rather than just redirecting them to `/`, we can
     * check if the URL contains a redirect query param, and send them there instead (for
     * example, links to `/login` generated by BioHub will typically include a redirect query param).
     * If there is no redirect query param, they will be sent to `/` as a fallback.
     */
    return <Redirect to={redirectUri} />;
  }

  return <Route {...rest}>{children}</Route>;
};
