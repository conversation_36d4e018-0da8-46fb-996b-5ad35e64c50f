import { SYSTEM_IDENTITY_SOURCE } from 'constants/auth';
import { useApi } from 'hooks/useApi';
import useDataLoader from 'hooks/useDataLoader';
import { useAuth } from 'react-oidc-context';
import { coerceIdentitySource } from 'utils/authUtils';

export interface IFaunaLogicUserWrapper {
  /**
   * Set to `true` if the user's information is still loading, false otherwise.
   */
  isLoading: boolean;
  /**
   * The user's system user id.
   */
  systemUserId: number | undefined;
  /**
   * The user's keycloak guid.
   */
  userGuid: string | null | undefined;
  /**
   * The user's identifier (username).
   */
  userIdentifier: string | undefined;
  /**
   * The user's system roles (by name).
   */
  roleNames: string[] | undefined;
  /**
   * The logged in user's identity source (IDIR, BCEID BASIC, BCEID BUSINESS, etc).
   */
  identitySource: SYSTEM_IDENTITY_SOURCE | null;
}

function useFaunaLogicUserWrapper(): IFaunaLogicUserWrapper {
  const auth = useAuth();

  const faunalogicApi = useApi();

  const faunalogicUserDataLoader = useDataLoader(() => faunalogicApi.user.getUser());

  if (auth.isAuthenticated) {
    faunalogicUserDataLoader.load();
  }

  const isLoading = !faunalogicUserDataLoader.isReady;

  const systemUserId = faunalogicUserDataLoader.data?.system_user_id;

  const userGuid =
    faunalogicUserDataLoader.data?.user_guid ||
    (auth.user?.profile?.idir_user_guid as string)?.toLowerCase() ||
    (auth.user?.profile?.bceid_user_guid as string)?.toLowerCase();

  const userIdentifier =
    faunalogicUserDataLoader.data?.user_identifier ||
    (auth.user?.profile?.idir_username as string) ||
    (auth.user?.profile?.bceid_username as string);

  const roleNames = faunalogicUserDataLoader.data?.role_names;

  const identitySource = coerceIdentitySource(
    faunalogicUserDataLoader.data?.identity_source || (auth.user?.profile?.identity_provider as string)?.toUpperCase()
  );

  return {
    isLoading,
    systemUserId,
    userGuid,
    userIdentifier,
    roleNames,
    identitySource
  };
}

export default useFaunaLogicUserWrapper;
