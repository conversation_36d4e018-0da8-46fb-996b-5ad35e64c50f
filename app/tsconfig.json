{"compilerOptions": {"module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "baseUrl": "src", "outDir": "dist", "jsx": "react-jsx", "target": "es2018", "sourceMap": true, "allowJs": false, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitThis": true, "noImplicitAny": true, "allowSyntheticDefaultImports": true, "noUnusedLocals": false, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "noEmit": true, "strict": true, "typeRoots": ["node_modules/@types", "src/types"]}, "include": ["src"], "ts-node": {"swc": true}}