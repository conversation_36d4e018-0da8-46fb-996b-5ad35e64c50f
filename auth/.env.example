# FaunaLogic Authentication Service Environment Configuration Template
# Copy this file to .env and customize for your deployment

# Keycloak Database Configuration
DB_VENDOR=postgres
DB_ADDR=postgres
DB_DATABASE=keycloak
DB_USER=keycloak
DB_PASSWORD=keycloak_password
DB_SCHEMA=public

# Keycloak Admin Configuration
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin_password

# Service Configuration
KC_HTTP_ENABLED=true
KC_HTTP_PORT=8080
KC_HOSTNAME_STRICT=false
KC_HOSTNAME_STRICT_HTTPS=false

# Development Settings (Change for Production!)
KC_LOG_LEVEL=INFO
KC_DEV_MODE=true

# Database Connection Pool
KC_DB_POOL_INITIAL_SIZE=5
KC_DB_POOL_MAX_SIZE=20
KC_DB_POOL_MIN_SIZE=5

# Security Settings (Production)
# KC_HTTPS_CERTIFICATE_FILE=/path/to/cert
# KC_HTTPS_CERTIFICATE_KEY_FILE=/path/to/key
# KC_PROXY=edge

# Realm Import Configuration
KEYCLOAK_IMPORT=/opt/keycloak/data/import/faunalogic-realm.json

# Cache Configuration
KC_CACHE=local
KC_CACHE_STACK=tcp

# Health Check Configuration
KC_HEALTH_ENABLED=true
KC_METRICS_ENABLED=true

# Session Configuration
KC_SPI_SESSIONS_INFINISPAN_PRELOADOFFLINE_SESSIONS=false

# Production Security (Uncomment and configure for production)
# KC_HTTP_ENABLED=false
# KC_HOSTNAME=your-auth-domain.com
# KC_HOSTNAME_STRICT=true
# KC_PROXY=edge

# Logging Configuration
KC_LOG_CONSOLE_OUTPUT=default
KC_LOG_CONSOLE_FORMAT="%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n"

# Theme Configuration (Optional)
# KC_SPI_THEME_STATIC_MAX_AGE=-1
# KC_SPI_THEME_CACHE_THEMES=false
# KC_SPI_THEME_CACHE_TEMPLATES=false