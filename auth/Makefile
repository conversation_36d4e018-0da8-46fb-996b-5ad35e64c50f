# FaunaLogic Auth Service Makefile
# Keycloak authentication service management

.PHONY: help build up down restart logs status clean setup dev test health import-realm export-realm reset-admin

# Colors for output
RED    := \033[31m
GREEN  := \033[32m
YELLOW := \033[33m
BLUE   := \033[34m
RESET  := \033[0m

# Default target
.DEFAULT_GOAL := help

# Service configuration
COMPOSE_FILE := docker-compose.yml
KEYCLOAK_URL := http://localhost:8080
ADMIN_USER := admin
ADMIN_PASS := admin_password

help: ## Show this help message
	@echo "$(GREEN)FaunaLogic Auth Service (Keycloak)$(RESET)"
	@echo ""
	@echo "$(YELLOW)Available targets:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: build ## Set up the auth service for development
	@echo "$(GREEN)Auth service setup complete$(RESET)"

dev: setup ## Alias for setup (development environment)

build: ## Build the Keycloak service
	@echo "$(YELLOW)Building Keycloak service...$(RESET)"
	docker-compose -f $(COMPOSE_FILE) build

up: ## Start Keycloak and database services
	@echo "$(YELLOW)Starting Keycloak services...$(RESET)"
	docker-compose -f $(COMPOSE_FILE) up -d
	@echo "$(GREEN)Keycloak services started$(RESET)"
	@echo "$(BLUE)Keycloak Admin Console: $(KEYCLOAK_URL)$(RESET)"
	@echo "$(BLUE)Admin credentials: $(ADMIN_USER) / $(ADMIN_PASS)$(RESET)"

down: ## Stop all services
	@echo "$(YELLOW)Stopping Keycloak services...$(RESET)"
	docker-compose -f $(COMPOSE_FILE) down
	@echo "$(GREEN)Services stopped$(RESET)"

restart: down up ## Restart all services

logs: ## Show logs from all services
	docker-compose -f $(COMPOSE_FILE) logs -f

logs-keycloak: ## Show logs from Keycloak service only
	docker-compose -f $(COMPOSE_FILE) logs -f keycloak

logs-db: ## Show logs from database service only
	docker-compose -f $(COMPOSE_FILE) logs -f keycloak-db

status: ## Show status of all services
	@echo "$(YELLOW)Service Status:$(RESET)"
	docker-compose -f $(COMPOSE_FILE) ps

health: up ## Check health of services
	@echo "$(YELLOW)Checking service health...$(RESET)"
	@sleep 5
	@curl -s -o /dev/null -w "Keycloak Health: %{http_code}\n" $(KEYCLOAK_URL)/health/ready || echo "$(RED)Keycloak not ready$(RESET)"
	@echo "$(GREEN)Health check complete$(RESET)"

clean: down ## Clean up containers and volumes
	@echo "$(YELLOW)Cleaning up containers and volumes...$(RESET)"
	docker-compose -f $(COMPOSE_FILE) down -v --remove-orphans
	docker system prune -f --volumes
	@echo "$(GREEN)Cleanup complete$(RESET)"

import-realm: ## Import realm configuration (requires realm file in realms/ directory)
	@echo "$(YELLOW)Importing realm configuration...$(RESET)"
	@if [ ! -f realms/faunalogic-realm.json ]; then \
		echo "$(RED)Realm file not found: realms/faunalogic-realm.json$(RESET)"; \
		exit 1; \
	fi
	docker-compose -f $(COMPOSE_FILE) exec keycloak /opt/keycloak/bin/kc.sh import --file /opt/keycloak/data/import/faunalogic-realm.json
	@echo "$(GREEN)Realm imported successfully$(RESET)"

export-realm: ## Export realm configuration
	@echo "$(YELLOW)Exporting realm configuration...$(RESET)"
	@mkdir -p realms
	docker-compose -f $(COMPOSE_FILE) exec keycloak /opt/keycloak/bin/kc.sh export --realm faunalogic --file /opt/keycloak/data/import/faunalogic-realm-export.json
	@echo "$(GREEN)Realm exported to realms/faunalogic-realm-export.json$(RESET)"

reset-admin: ## Reset admin password
	@echo "$(YELLOW)Resetting admin password...$(RESET)"
	docker-compose -f $(COMPOSE_FILE) exec keycloak /opt/keycloak/bin/kc.sh config credentials --server $(KEYCLOAK_URL) --realm master --user $(ADMIN_USER) --password $(ADMIN_PASS)
	@echo "$(GREEN)Admin password reset$(RESET)"

test: health ## Run basic tests
	@echo "$(YELLOW)Running basic health tests...$(RESET)"
	@curl -s $(KEYCLOAK_URL)/realms/master/.well-known/openid_configuration > /dev/null && echo "$(GREEN)✓ Master realm accessible$(RESET)" || echo "$(RED)✗ Master realm not accessible$(RESET)"
	@echo "$(GREEN)Tests completed$(RESET)"

# Development helpers
dev-logs: ## Follow development logs
	docker-compose -f $(COMPOSE_FILE) logs -f --tail=100

dev-shell: ## Open shell in Keycloak container
	docker-compose -f $(COMPOSE_FILE) exec keycloak /bin/bash

dev-db-shell: ## Open psql shell in database
	docker-compose -f $(COMPOSE_FILE) exec keycloak-db psql -U keycloak -d keycloak