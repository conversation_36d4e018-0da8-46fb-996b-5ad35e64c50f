# FaunaLogic Authentication Service

This directory contains the Keycloak-based authentication service for the FaunaLogic platform.

## Overview

The authentication service provides:
- **Identity and Access Management (IAM)** using Keycloak
- **Single Sign-On (SSO)** capabilities
- **OAuth 2.0 / OpenID Connect** integration
- **User and client management**
- **Secure token-based authentication**

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Make (for using Makefile commands)

### Starting the Service

```bash
# From project root - starts ALL services (database + auth + api + frontend)
make up

# From auth directory - starts only auth services
make up

# Check service health
make auth-health

# View logs from all services
make logs

# View only auth service logs
make logs
```

### Accessing Keycloak

#### Admin Console
- **URL**: http://localhost:8080
- **Admin Credentials**: 
  - Username: `admin`
  - Password: `admin_password`

#### User Account Console (FaunaLogic Realm)
- **URL**: http://localhost:8080/realms/faunalogic/account
- **Test User Credentials**:
  - Username: `submission-user`
  - Password: `submission-password`
  - Email: `<EMAIL>`

## Service Configuration

### Components

1. **Keycloak Server** (Port 8080)
   - Authentication and authorization server
   - Admin console for realm management
   - REST API for client integration

2. **PostgreSQL Database** (Internal)
   - Keycloak's data persistence layer
   - User profiles, clients, and configuration storage

### Pre-configured Realm: `faunalogic`

The service includes a pre-configured realm with:

#### Client Configuration
- **Client ID**: `faunalogic-submission-client`
- **Client Secret**: `submission-client-secret`
- **Allowed Redirect URIs**: 
  - `http://localhost:3000/*`
  - `http://localhost:8000/*`
  - `urn:ietf:wg:oauth:2.0:oob`
- **Grant Types**: Authorization Code, Client Credentials, Resource Owner Password
- **Scopes**: `openid`, `profile`, `email`, `roles`

#### Test User
- **Username**: `submission-user`
- **Password**: `submission-password`
- **Email**: `<EMAIL>`
- **Role**: `submission-user`
- **Group**: `submission-users`

## Integration Guide

### For Submission Tool

The submission tool can authenticate using the following endpoints:

#### OAuth 2.0 / OpenID Connect Endpoints
```
# Well-known configuration
GET http://localhost:8080/realms/faunalogic/.well-known/openid_configuration

# Authorization endpoint
GET http://localhost:8080/realms/faunalogic/protocol/openid-connect/auth

# Token endpoint
POST http://localhost:8080/realms/faunalogic/protocol/openid-connect/token

# User info endpoint
GET http://localhost:8080/realms/faunalogic/protocol/openid-connect/userinfo
```

#### Client Credentials Flow (Recommended for CLI tools)
```bash
curl -X POST http://localhost:8080/realms/faunalogic/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=faunalogic-submission-client" \
  -d "client_secret=submission-client-secret"
```

#### Resource Owner Password Credentials Flow (Alternative)
```bash
curl -X POST http://localhost:8080/realms/faunalogic/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password" \
  -d "client_id=faunalogic-submission-client" \
  -d "client_secret=submission-client-secret" \
  -d "username=submission-user" \
  -d "password=submission-password"
```

## Available Commands

### Project-Level Commands (from root directory)
```bash
make help          # Show all available commands
make up            # Start ALL services (database + auth)
make down          # Stop ALL services
make status        # Show status of ALL services
make logs          # View logs from ALL services
make auth-health   # Check auth service health specifically
make import-realm  # Import Keycloak realm configuration
make export-realm  # Export Keycloak realm configuration
make reset-auth    # Reset Keycloak admin password
```

### Auth-Specific Commands (from auth/ directory)
```bash
make up            # Start auth services only
make down          # Stop auth services only
make restart       # Restart auth services
make logs          # View auth service logs
make status        # Show auth service status
make health        # Check auth service health
```

### Development
```bash
make dev-logs      # Follow development logs
make dev-shell     # Open shell in Keycloak container
make dev-db-shell  # Open database shell
```

### Realm Management
```bash
make import-realm  # Import realm configuration
make export-realm  # Export realm configuration
```

### Maintenance
```bash
make clean         # Clean containers and volumes
make reset-admin   # Reset admin password
```

## Security Considerations

### Development vs Production

**Current Configuration (Development)**:
- HTTP enabled (not HTTPS)
- Default admin credentials
- Permissive CORS settings
- Local database storage

**Production Recommendations**:
- Enable HTTPS with proper certificates
- Change default admin credentials
- Configure proper CORS origins
- Use external PostgreSQL database
- Enable audit logging
- Configure proper session timeouts
- Set up backup procedures

### Credential Management

**Default Credentials (Change in Production)**:
- **Admin Console**: `admin` / `admin_password`
- **Test User**: `submission-user` / `submission-password`
- **Client Secret**: `submission-client-secret`
- **Database**: `keycloak` / `keycloak_password`

## Troubleshooting

### Common Issues

1. **Service fails to start**
   ```bash
   # Check if ports are available
   netstat -tlnp | grep :8080
   
   # Check service logs
   make logs
   ```

2. **Database connection issues**
   ```bash
   # Check database status
   make logs-db
   
   # Verify database health
   docker exec faunalogic-keycloak-db pg_isready -U keycloak
   ```

3. **Realm import fails**
   ```bash
   # Ensure realm file exists
   ls -la realms/faunalogic-realm.json
   
   # Check Keycloak logs during import
   make logs-keycloak
   ```

### Health Checks

```bash
# Service health
curl http://localhost:8080/health/ready

# Realm availability
curl http://localhost:8080/realms/faunalogic/.well-known/openid_configuration
```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌────────────────┐
│  Submission     │    │    Keycloak      │    │   PostgreSQL   │
│     Tool        │◄───┤   Auth Server    │◄───┤   Database     │
│  (Port 8000)    │    │   (Port 8080)    │    │  (Internal)    │
└─────────────────┘    └──────────────────┘    └────────────────┘
```

## Development Workflow

1. **Start Services**: `make up`
2. **Access Admin Console**: http://localhost:8080 (admin/admin_password)
3. **Configure Realm/Clients** as needed
4. **Test with User Console**: http://localhost:8080/realms/faunalogic/account (submission-user/submission-password)
5. **Test Integration** with submission tool
6. **Export Configuration**: `make export-realm`
7. **Commit Changes** to realm configuration

## Support

For issues related to:
- **Keycloak Configuration**: Check official [Keycloak Documentation](https://www.keycloak.org/documentation)
- **Docker Issues**: Verify Docker installation and permissions
- **Integration Problems**: Review OAuth 2.0/OpenID Connect specifications