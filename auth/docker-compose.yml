version: '3.8'

services:
  keycloak:
    image: quay.io/keycloak/keycloak:25.0
    container_name: faunalogic-keycloak
    environment:
      # Database configuration
      KC_DB: postgres
      KC_DB_URL: *******************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: keycloak_password
      
      # Admin user configuration
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin_password
      
      # Keycloak configuration
      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8080
      KC_HOSTNAME_STRICT: false
      KC_HTTP_ENABLED: true
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
      
      # Development settings
      KC_LOG_LEVEL: INFO
    ports:
      - "8080:8080"
    command: start-dev
    depends_on:
      keycloak-db:
        condition: service_healthy
    volumes:
      - ./realms:/opt/keycloak/data/import
      - ./themes:/opt/keycloak/themes
    networks:
      - keycloak-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s

  keycloak-db:
    image: postgres:15-alpine
    container_name: faunalogic-keycloak-db
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: keycloak_password
    volumes:
      - keycloak-db-data:/var/lib/postgresql/data
    networks:
      - keycloak-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U keycloak -d keycloak"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  keycloak-db-data:
    driver: local

networks:
  keycloak-network:
    driver: bridge