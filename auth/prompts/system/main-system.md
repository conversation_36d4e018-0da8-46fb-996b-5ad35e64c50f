# Authentication & Authorization Service System Prompt

**Version**: 1.0.0  
**Last Updated**: 2025-06-29  
**Status**: 🚧 Development Ready  
**Service**: Identity and Access Management Layer

## Service Overview

### Authentication Service Description
The FaunaLogic authentication and authorization service provides centralized identity and access management using Keycloak. It manages user authentication, authorization, and access control across all FaunaLogic services with multi-realm support for different environments and tenants.

### Service Architecture
- **Identity Provider**: Keycloak 23+ with PostgreSQL backend
- **Multi-Realm Support**: Separate realms for saas-prod, saas-test, saas-dev
- **Protocol Support**: OAuth 2.0, OpenID Connect, SAML 2.0
- **Database Integration**: PostgreSQL for Keycloak data storage
- **Theme Support**: Custom FaunaLogic branding and themes
- **Federation**: Support for external identity providers
- **High Availability**: Clustered deployment support
- **Monitoring**: Metrics and health checks integration

## 🔐 CRITICAL: Multi-Realm Configuration

**⚠️ REALM SEPARATION IS MANDATORY FOR SECURITY:**

```yaml
# Keycloak Realm Configuration
realms:
  - name: "saas-prod"
    description: "FaunaLogic Production SaaS Environment"
    environment: "production"
    
  - name: "saas-test" 
    description: "FaunaLogic Testing SaaS Environment"
    environment: "testing"
    
  - name: "saas-dev"
    description: "FaunaLogic Development SaaS Environment" 
    environment: "development"
```

**Realm Access URLs:**
```
Production:  https://auth.faunalogic.com/realms/saas-prod
Testing:     https://auth-test.faunalogic.com/realms/saas-test
Development: https://auth-dev.faunalogic.com/realms/saas-dev
```

## Service Components

### Core Keycloak Features
- **User Management**: Registration, authentication, profile management
- **Role-Based Access Control**: Hierarchical roles and permissions
- **Client Management**: OAuth2/OIDC client applications
- **Federation**: External identity provider integration
- **Multi-Factor Authentication**: TOTP, SMS, email verification
- **Session Management**: SSO, session timeout, logout
- **Custom Themes**: FaunaLogic branded login/admin interfaces

### Realm-Specific Configuration
```yaml
# Per-realm configuration structure
realm_config:
  authentication:
    password_policy: "Minimum 8 characters, special chars, numbers"
    mfa_required: true
    session_timeout: "8 hours"
    remember_me: true
    
  registration:
    enabled: true
    email_verification: true
    terms_required: true
    
  clients:
    - name: "faunalogic-webapp"
      type: "public"
      redirect_uris: ["https://app.faunalogic.com/*"]
      
    - name: "faunalogic-api"
      type: "confidential" 
      service_account: true
      
    - name: "faunalogic-submission"
      type: "confidential"
      service_account: true
```

### Database Integration
The auth service integrates with the FaunaLogic database for:
- User profile synchronization
- Tenant management
- Permission mapping
- Audit logging

```sql
-- Database integration points
CREATE TABLE auth_user_sync (
    keycloak_user_id UUID NOT NULL,
    faunalogic_user_id UUID NOT NULL REFERENCES application_user(id),
    realm_name VARCHAR(50) NOT NULL,
    sync_status VARCHAR(20) DEFAULT 'pending',
    last_sync TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE auth_tenant_mapping (
    tenant_id UUID NOT NULL REFERENCES tenant(id),
    realm_name VARCHAR(50) NOT NULL,
    keycloak_group_id VARCHAR(255),
    environment VARCHAR(20) NOT NULL
);
```

## Development Constraints

### Authentication Service Requirements
- [ ] All realms must be isolated from each other
- [ ] User data must not cross realm boundaries
- [ ] Each realm must have separate admin users
- [ ] Client configurations must be realm-specific
- [ ] Themes must be consistent across realms
- [ ] Database connections must use dedicated auth schema
- [ ] All authentication flows must be logged and audited

### Security Requirements
- [ ] Multi-factor authentication enforced for admin users
- [ ] Strong password policies implemented
- [ ] Session security with proper timeout handling
- [ ] HTTPS required for all authentication flows
- [ ] CSRF protection enabled
- [ ] Rate limiting on authentication endpoints
- [ ] Brute force protection configured

### Integration Requirements
- [ ] OAuth2/OIDC client integration with all services
- [ ] User profile synchronization with FaunaLogic database
- [ ] Role mapping to FaunaLogic permissions
- [ ] Tenant isolation enforcement
- [ ] SSO across all FaunaLogic applications
- [ ] External identity provider federation support

## Context Files

### Always Include These Files
When working on authentication service tasks, include these files for context:

**Keycloak Configuration:**
- `auth/config/keycloak.conf` - Main Keycloak configuration
- `auth/realms/saas-prod/realm-config.json` - Production realm settings
- `auth/realms/saas-test/realm-config.json` - Testing realm settings  
- `auth/realms/saas-dev/realm-config.json` - Development realm settings

**Database Integration:**
- `database/migrations/` - Database schema for user integration
- `database/prompts/system/main-system.md` - Database service context

**Docker Configuration:**
- `auth/docker/docker-compose.yml` - Keycloak deployment
- `auth/docker/Dockerfile` - Custom Keycloak image

## Development Guidelines

### Keycloak Customization Best Practices
```bash
# Custom theme development
auth/themes/faunalogic/
├── login/
│   ├── theme.properties
│   ├── login.ftl
│   ├── register.ftl
│   └── resources/css/login.css
├── account/
│   ├── theme.properties
│   └── account.ftl
└── admin/
    ├── theme.properties
    └── resources/css/admin.css
```

### Realm Configuration Management
```json
{
  "realm": "saas-prod",
  "enabled": true,
  "registrationAllowed": true,
  "resetPasswordAllowed": true,
  "rememberMe": true,
  "verifyEmail": true,
  "loginWithEmailAllowed": true,
  "duplicateEmailsAllowed": false,
  "sslRequired": "external",
  "passwordPolicy": "length(8) and digits(1) and specialChars(1) and upperCase(1) and lowerCase(1)",
  "defaultSignatureAlgorithm": "RS256",
  "revokeRefreshToken": true,
  "refreshTokenMaxReuse": 0,
  "accessTokenLifespan": 900,
  "accessTokenLifespanForImplicitFlow": 900,
  "ssoSessionIdleTimeout": 28800,
  "ssoSessionMaxLifespan": 28800,
  "offlineSessionIdleTimeout": 2592000,
  "accessCodeLifespan": 60,
  "accessCodeLifespanUserAction": 300,
  "accessCodeLifespanLogin": 1800
}
```

### Client Configuration Patterns
```json
{
  "clientId": "faunalogic-webapp",
  "name": "FaunaLogic Web Application",
  "description": "Main web application client",
  "enabled": true,
  "publicClient": true,
  "redirectUris": [
    "https://app.faunalogic.com/*",
    "https://app-test.faunalogic.com/*",
    "https://app-dev.faunalogic.com/*"
  ],
  "webOrigins": [
    "https://app.faunalogic.com",
    "https://app-test.faunalogic.com", 
    "https://app-dev.faunalogic.com"
  ],
  "protocol": "openid-connect",
  "attributes": {
    "access.token.lifespan": "900",
    "pkce.code.challenge.method": "S256"
  },
  "defaultClientScopes": [
    "web-origins",
    "acr",
    "profile", 
    "roles",
    "email"
  ]
}
```

### Database Integration Patterns
```python
# User synchronization service
class KeycloakUserSync:
    def __init__(self, realm_name: str):
        self.realm_name = realm_name
        self.keycloak_admin = self.get_keycloak_admin()
    
    async def sync_user_profile(self, keycloak_user_id: str):
        """Synchronize user profile from Keycloak to FaunaLogic database"""
        
        # Get user from Keycloak
        keycloak_user = self.keycloak_admin.get_user(keycloak_user_id)
        
        # Map to FaunaLogic user structure
        faunalogic_user = {
            'first_name': keycloak_user.get('firstName'),
            'last_name': keycloak_user.get('lastName'),
            'email_address': keycloak_user.get('email'),
            'keycloak_user_id': keycloak_user_id,
            'realm_name': self.realm_name
        }
        
        # Insert/update via database API
        async with get_db_connection() as conn:
            await conn.execute("SET search_path = faunalogic_dapi_v1, public")
            
            result = await conn.fetchrow(
                "SELECT api_sync_user_from_keycloak($1, $2, $3)",
                keycloak_user_id,
                self.realm_name, 
                json.dumps(faunalogic_user)
            )
            
        return result
```

## Integration Points

### Service Integration
All FaunaLogic services must integrate with Keycloak for authentication:

```python
# FastAPI integration example
from fastapi import FastAPI, Depends, HTTPException
from fastapi.security import HTTPBearer
import jwt

app = FastAPI()
security = HTTPBearer()

def verify_token(token: str = Depends(security)):
    """Verify Keycloak JWT token"""
    try:
        # Get public key from Keycloak
        public_key = get_keycloak_public_key(realm_name)
        
        # Verify and decode token
        payload = jwt.decode(
            token.credentials,
            public_key,
            algorithms=["RS256"],
            audience="faunalogic-api"
        )
        
        return payload
        
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

@app.get("/protected")
async def protected_endpoint(token_data: dict = Depends(verify_token)):
    """Protected endpoint requiring valid JWT"""
    user_id = token_data.get("sub")
    realm = token_data.get("iss").split("/")[-1]
    
    return {"user_id": user_id, "realm": realm}
```

### Frontend Integration
```javascript
// React/JavaScript integration
import Keycloak from 'keycloak-js';

const keycloakConfig = {
  url: process.env.REACT_APP_KEYCLOAK_URL,
  realm: process.env.REACT_APP_KEYCLOAK_REALM,
  clientId: 'faunalogic-webapp'
};

const keycloak = new Keycloak(keycloakConfig);

// Initialize Keycloak
keycloak.init({
  onLoad: 'login-required',
  checkLoginIframe: false,
  pkceMethod: 'S256'
}).then((authenticated) => {
  if (authenticated) {
    // Set authorization header for API calls
    axios.defaults.headers.common['Authorization'] = `Bearer ${keycloak.token}`;
    
    // Auto-refresh token
    setInterval(() => {
      keycloak.updateToken(70).catch(() => {
        keycloak.logout();
      });
    }, 60000);
  }
});
```

## Testing Standards

### Authentication Testing
```python
# Integration test for authentication flow
class TestKeycloakAuthentication:
    def setup_method(self):
        """Setup test environment"""
        self.realm = "saas-test"
        self.test_user = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'TestPass123!'
        }
    
    async def test_user_login_flow(self):
        """Test complete user login flow"""
        
        # Create test user in Keycloak
        user_id = await self.create_keycloak_user(self.test_user)
        
        # Attempt login
        token_response = await self.authenticate_user(
            self.test_user['username'],
            self.test_user['password']
        )
        
        # Verify token
        assert token_response['access_token'] is not None
        assert token_response['token_type'] == 'Bearer'
        
        # Verify token claims
        claims = jwt.decode(
            token_response['access_token'],
            verify=False  # Skip verification for test
        )
        
        assert claims['preferred_username'] == self.test_user['username']
        assert claims['email'] == self.test_user['email']
        
        # Cleanup
        await self.delete_keycloak_user(user_id)
    
    async def test_realm_isolation(self):
        """Test that realms are properly isolated"""
        
        # Create user in saas-test realm
        test_user_id = await self.create_keycloak_user(
            self.test_user, realm="saas-test"
        )
        
        # Verify user doesn't exist in saas-dev realm
        dev_user = await self.get_keycloak_user(
            self.test_user['username'], realm="saas-dev"
        )
        
        assert dev_user is None
        
        # Cleanup
        await self.delete_keycloak_user(test_user_id, realm="saas-test")
```

### Load Testing
```python
# Performance test for authentication endpoints
class TestAuthenticationPerformance:
    async def test_concurrent_logins(self):
        """Test concurrent user authentication"""
        
        # Create multiple test users
        users = await self.create_multiple_users(count=100)
        
        # Simulate concurrent logins
        tasks = [
            self.authenticate_user(user['username'], user['password'])
            for user in users
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Verify all authentications succeeded
        assert all(result['access_token'] for result in results)
        
        # Verify performance requirements
        total_time = end_time - start_time
        assert total_time < 10.0  # All logins within 10 seconds
        
        # Cleanup
        await self.cleanup_test_users(users)
```

## Environment-Specific Considerations

### Development Environment
- Relaxed password policies for testing
- Local file-based database (H2) option
- Debug logging enabled
- Development-specific themes
- Local SSL certificates

### Testing Environment
- Production-like configuration
- Separate test database
- Automated testing integration
- CI/CD pipeline compatibility
- Test data management

### Production Environment
- High availability clustering
- External PostgreSQL database
- SSL/TLS termination
- Performance monitoring
- Backup and disaster recovery
- Security hardening

## Monitoring and Maintenance

### Health Checks
```bash
# Keycloak health endpoints
curl http://localhost:8080/health/ready
curl http://localhost:8080/health/live

# Realm-specific checks
curl http://localhost:8080/realms/saas-prod/.well-known/openid_configuration
```

### Metrics and Monitoring
```yaml
# Prometheus metrics configuration
keycloak_metrics:
  - name: "keycloak_logins_total"
    description: "Total number of logins per realm"
    labels: ["realm", "client_id"]
    
  - name: "keycloak_active_sessions"
    description: "Number of active user sessions"
    labels: ["realm"]
    
  - name: "keycloak_failed_logins_total"
    description: "Total number of failed login attempts"
    labels: ["realm", "error_type"]
```

### Backup and Recovery
```bash
# Database backup (Keycloak data)
pg_dump -h localhost -U keycloak keycloak_db > keycloak_backup.sql

# Realm configuration export
./kcadm.sh export --realm saas-prod --file saas-prod-export.json

# User data export
./kcadm.sh get users -r saas-prod --format csv > users-export.csv
```

## Security Best Practices

### Access Control
- Admin console access restricted to specific IPs
- Multi-factor authentication for admin users
- Regular password rotation policies
- Audit logging for all administrative actions

### Network Security
- HTTPS enforcement for all endpoints
- Proper CORS configuration
- Rate limiting on authentication endpoints
- DDoS protection measures

### Data Protection
- Encryption at rest for user data
- Secure token storage and transmission
- PII handling compliance (GDPR)
- Regular security audits and penetration testing

---

**Important**: This authentication service is the security foundation for the entire FaunaLogic system. All identity management, access control, and security policies must be carefully implemented and regularly audited.