{"id": "faunalogic", "realm": "faunalogic", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayNameHtml": "<div class=\"kc-logo-text\"><span>FaunaLogic</span></div>", "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": true, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": true, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "clients": [{"id": "faunalogic-submission-client", "clientId": "faunalogic-submission-client", "name": "FaunaLogic Submission Client", "description": "Client for FaunaLogic submission tool", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "submission-client-secret", "redirectUris": ["http://localhost:3000/*", "http://localhost:8000/*", "urn:ietf:wg:oauth:2.0:oob"], "webOrigins": ["http://localhost:3000", "http://localhost:8000"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false", "oauth2.device.authorization.grant.enabled": "false", "client_credentials.use_refresh_token": "false", "acr.loa.map": "{}", "tls.client.certificate.bound.access.tokens": "false", "saml.client.signature": "false", "saml.assertion.lifespan": "", "saml.signing.private.key": "", "saml.signing.certificate": "", "saml.encryption.private.key": "", "saml.encryption.certificate": "", "require.pushed.authorization.requests": "false", "use.refresh.tokens": "true", "id.token.as.detached.signature": "false", "display.on.consent.screen": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "access": {"view": true, "configure": true, "manage": true}}], "users": [{"id": "submission-user", "username": "submission-user", "enabled": true, "totp": false, "emailVerified": true, "firstName": "Submission", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "submission-password", "temporary": false}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-faunalogic"], "clientRoles": {"faunalogic-submission-client": ["submission-user"]}, "notBefore": 0, "groups": ["/submission-users"]}], "roles": {"realm": [{"id": "default-roles-faunalogic", "name": "default-roles-faunalogic", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "faunalogic"}, {"id": "offline_access", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "faunalogic"}, {"id": "uma_authorization", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "faunalogic"}], "client": {"faunalogic-submission-client": [{"id": "submission-user", "name": "submission-user", "description": "Role for submission tool users", "composite": false, "clientRole": true, "containerId": "faunalogic-submission-client"}]}}, "groups": [{"id": "submission-users", "name": "submission-users", "path": "/submission-users", "attributes": {}, "realmRoles": [], "clientRoles": {"faunalogic-submission-client": ["submission-user"]}, "subGroups": []}], "defaultRole": {"id": "default-roles-faunalogic", "name": "default-roles-faunalogic", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "faunalogic"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyRpEntityNamePasswordless": "keycloak", "webAuthnPolicySignatureAlgorithmsPasswordless": ["ES256"], "webAuthnPolicyRpIdPasswordless": "", "webAuthnPolicyAttestationConveyancePreferencePasswordless": "not specified", "webAuthnPolicyAuthenticatorAttachmentPasswordless": "not specified", "webAuthnPolicyRequireResidentKeyPasswordless": "not specified", "webAuthnPolicyUserVerificationRequirementPasswordless": "not specified", "webAuthnPolicyCreateTimeoutPasswordless": 0, "webAuthnPolicyAvoidSameAuthenticatorRegisterPasswordless": false, "scopeMappings": [], "clientScopeMappings": {}, "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';"}, "smtpServer": {}, "loginTheme": "", "accountTheme": "", "adminTheme": "", "emailTheme": "", "eventListeners": ["jboss-logging"], "enabledEventTypes": [], "eventsExpiration": 0, "eventsListeners": ["jboss-logging"], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [], "authenticatorConfig": [], "requiredActions": [], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaInterval": "5", "cibaAuthRequestedUserHint": "login_hint", "parRequestUriLifespan": "60", "frontendUrl": "", "clientOfflineSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "clientSessionIdleTimeout": "0", "clientSessionMaxLifespan": "0", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5"}, "keycloakVersion": "25.0.0", "userManagedAccessAllowed": false}