services:
  ## Build postgres docker image
  db:
    image: ${DOCKER_PROJECT_NAME}-db-${DOCKER_NAMESPACE}-img
    container_name: ${DOCKER_PROJECT_NAME}-db-${DOCKER_NAMESPACE}-container
    build:
      context: ./database/.docker/db
      dockerfile: Dockerfile
      args:
        - POSTGRES_VERSION=${POSTGRES_VERSION}
        - POSTGIS_VERSION=${POSTGIS_VERSION}
        - TZ=${DB_TZ}
    ports:
      - ${DB_PORT}:5432
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $DB_ADMIN -p 5432 -d $DB_DATABASE"]
      interval: 5s
      timeout: 5s
      retries: 30
    environment:
      - NODE_ENV=${NODE_ENV}
      - POSTGRES_USER=${DB_ADMIN}
      - POSTGRES_PASSWORD=${DB_ADMIN_PASS}
      - POSTGRES_DB=${DB_DATABASE}
      - PORT=5432
      - PGDATA=${PGDA<PERSON>}/${POSTGRES_VERSION}
    networks:
      - faunalogic-network
    volumes:
      - postgres:${PGDATA}

  ## Build the api docker image
  api:
    image: ${DOCKER_PROJECT_NAME}-api-${DOCKER_NAMESPACE}-img
    container_name: ${DOCKER_PROJECT_NAME}-api-${DOCKER_NAMESPACE}-container
    build:
      context: ./api
      dockerfile: ./.docker/api/Dockerfile
    ports:
      - ${API_PORT}:${API_PORT}
    environment:
      - NODE_ENV=${NODE_ENV}
      - NODE_OPTIONS=${API_NODE_OPTIONS}
      # ITIS API
      - ITIS_SOLR_URL=${ITIS_SOLR_URL}
      - S3_KEY_PREFIX=${S3_KEY_PREFIX}
      - TZ=${API_TZ}
      - API_HOST=${API_HOST}
      - API_PORT=${API_PORT}
      - DB_HOST=${DB_HOST}
      - DB_USER_API=${DB_USER_API}
      - DB_USER_API_PASS=${DB_USER_API_PASS}
      - DB_PORT=5432
      - DB_DATABASE=${DB_DATABASE}
      - DB_SCHEMA=${DB_SCHEMA}
      - DB_POOL_SIZE=${DB_POOL_SIZE}
      - DB_CONNECTION_MAX_USES=${DB_CONNECTION_MAX_USES}
      - DB_CONNECTION_TIMEOUT=${DB_CONNECTION_TIMEOUT}
      - DB_IDLE_TIMEOUT=${DB_IDLE_TIMEOUT}
      # Keycloak
      - KEYCLOAK_HOST=${KEYCLOAK_HOST}
      - KEYCLOAK_REALM=${KEYCLOAK_REALM}
      # Keycloak Service client
      - KEYCLOAK_ADMIN_USERNAME=${KEYCLOAK_ADMIN_USERNAME}
      - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
      # Keycloak CSS API
      - KEYCLOAK_API_TOKEN_URL=${KEYCLOAK_API_TOKEN_URL}
      - KEYCLOAK_API_CLIENT_ID=${KEYCLOAK_API_CLIENT_ID}
      - KEYCLOAK_API_CLIENT_SECRET=${KEYCLOAK_API_CLIENT_SECRET}
      - KEYCLOAK_API_HOST=${KEYCLOAK_API_HOST}
      - KEYCLOAK_API_ENVIRONMENT=${KEYCLOAK_API_ENVIRONMENT}
      # Object Store (S3)
      - OBJECT_STORE_URL=${OBJECT_STORE_URL}
      - OBJECT_STORE_ACCESS_KEY_ID=${OBJECT_STORE_ACCESS_KEY_ID}
      - OBJECT_STORE_SECRET_KEY_ID=${OBJECT_STORE_SECRET_KEY_ID}
      - OBJECT_STORE_BUCKET_NAME=${OBJECT_STORE_BUCKET_NAME}
      - MAX_REQ_BODY_SIZE=${MAX_REQ_BODY_SIZE}
      - MAX_UPLOAD_NUM_FILES=${MAX_UPLOAD_NUM_FILES}
      - MAX_UPLOAD_FILE_SIZE=${MAX_UPLOAD_FILE_SIZE}
      # Logging
      - LOG_LEVEL=${LOG_LEVEL}
      - LOG_LEVEL_FILE=${LOG_LEVEL_FILE}
      - LOG_FILE_DIR=${LOG_FILE_DIR}
      - LOG_FILE_NAME=${LOG_FILE_NAME}
      - LOG_FILE_DATE_PATTERN=${LOG_FILE_DATE_PATTERN}
      - LOG_FILE_MAX_SIZE=${LOG_FILE_MAX_SIZE}
      - LOG_FILE_MAX_FILES=${LOG_FILE_MAX_FILES}
      # Clamav
      - CLAMAV_PORT=${CLAMAV_PORT}
      - CLAMAV_HOST=${CLAMAV_HOST}
      - ENABLE_FILE_VIRUS_SCAN=${ENABLE_FILE_VIRUS_SCAN}
      # GCNotify
      - GCNOTIFY_SECRET_API_KEY=${GCNOTIFY_SECRET_API_KEY}
      - GCNOTIFY_ADMIN_EMAIL=${GCNOTIFY_ADMIN_EMAIL}
      - GCNOTIFY_REQUEST_ACCESS_SECURE_DOCUMENTS=${GCNOTIFY_REQUEST_ACCESS_SECURE_DOCUMENTS}
      - GCNOTIFY_ONBOARDING_REQUEST_EMAIL_TEMPLATE=${GCNOTIFY_ONBOARDING_REQUEST_EMAIL_TEMPLATE}
      - GCNOTIFY_ONBOARDING_REQUEST_SMS_TEMPLATE=${GCNOTIFY_ONBOARDING_REQUEST_SMS_TEMPLATE}
      - GCNOTIFY_EMAIL_URL=${GCNOTIFY_EMAIL_URL}
      - GCNOTIFY_SMS_URL=${GCNOTIFY_SMS_URL}
      - APP_HOST=${APP_HOST}
    volumes:
      - ./api:/opt/app-root/src
      - /opt/app-root/src/node_modules # prevents local node_modules overriding container node_modules
    networks:
      - faunalogic-network
    depends_on:
      db:
        condition: service_healthy
      db_setup:
        condition: service_completed_successfully

  # Build the api_worker docker image
  queue:
    image: ${DOCKER_PROJECT_NAME}-queue-${DOCKER_NAMESPACE}-img
    container_name: ${DOCKER_PROJECT_NAME}-queue-${DOCKER_NAMESPACE}-container
    build:
      context: ./api
      dockerfile: ./.docker/queue/Dockerfile
    environment:
      - NODE_ENV=${NODE_ENV}
      - S3_KEY_PREFIX=${S3_KEY_PREFIX}
      - API_HOST=${API_HOST}
      - API_PORT=${API_PORT}
      - TZ=${API_TZ}
      - DB_HOST=${DB_HOST}
      - DB_USER_API=${DB_USER_API}
      - DB_USER_API_PASS=${DB_USER_API_PASS}
      - DB_PORT=5432
      - DB_DATABASE=${DB_DATABASE}
      - DB_SCHEMA=${DB_SCHEMA}
      - DB_POOL_SIZE=${DB_POOL_SIZE}
      - DB_CONNECTION_MAX_USES=${DB_CONNECTION_MAX_USES}
      - DB_CONNECTION_TIMEOUT=${DB_CONNECTION_TIMEOUT}
      - DB_IDLE_TIMEOUT=${DB_IDLE_TIMEOUT}
      - OBJECT_STORE_URL=${OBJECT_STORE_URL}
      - OBJECT_STORE_ACCESS_KEY_ID=${OBJECT_STORE_ACCESS_KEY_ID}
      - OBJECT_STORE_SECRET_KEY_ID=${OBJECT_STORE_SECRET_KEY_ID}
      - OBJECT_STORE_BUCKET_NAME=${OBJECT_STORE_BUCKET_NAME}
      - LOG_LEVEL=${LOG_LEVEL}
      - APP_HOST=${APP_HOST}
    volumes:
      - ./api:/opt/app-root/src
      - /opt/app-root/src/node_modules # prevents local node_modules overriding container node_modules
    networks:
      - faunalogic-network
    depends_on:
      - db
      - db_setup

  # Build the clamav docker image
  clamav:
    image: mkodockx/docker-clamav:latest
    container_name: ${DOCKER_PROJECT_NAME}-clamav-${DOCKER_NAMESPACE}-container
    ports:
      - ${CLAMAV_PORT}:3310
    networks:
      - faunalogic-network

  ## Build the app docker image
  app:
    image: ${DOCKER_PROJECT_NAME}-app-${DOCKER_NAMESPACE}-img
    container_name: ${DOCKER_PROJECT_NAME}-app-${DOCKER_NAMESPACE}-container
    build:
      context: ./app
      dockerfile: ./.docker/app/Dockerfile
    stdin_open: true
    ports:
      - ${APP_PORT}:${APP_PORT}
    environment:
      - NODE_ENV=${NODE_ENV}
      - NODE_OPTIONS=${APP_NODE_OPTIONS}
      - REACT_APP_NODE_ENV=${NODE_ENV}
      - PORT=${APP_PORT}
      - REACT_APP_API_HOST=${API_HOST}
      - REACT_APP_API_PORT=${API_PORT}
      - REACT_APP_MAX_UPLOAD_NUM_FILES=${MAX_UPLOAD_NUM_FILES}
      - REACT_APP_MAX_UPLOAD_FILE_SIZE=${MAX_UPLOAD_FILE_SIZE}
      - REACT_APP_SITEMINDER_LOGOUT_URL=${SITEMINDER_LOGOUT_URL}
      - REACT_APP_KEYCLOAK_HOST=${KEYCLOAK_HOST}
      - REACT_APP_KEYCLOAK_REALM=${KEYCLOAK_REALM}
      - REACT_APP_KEYCLOAK_CLIENT_ID=${KEYCLOAK_CLIENT_ID}
    volumes:
      - ./app:/opt/app-root/src
      - /opt/app-root/src/node_modules # prevents local node_modules overriding container node_modules
    networks:
      - faunalogic-network

  ## Run the database migrations and seeding
  db_setup:
    image: ${DOCKER_PROJECT_NAME}-db-setup-${DOCKER_NAMESPACE}-img
    container_name: ${DOCKER_PROJECT_NAME}-db-setup-${DOCKER_NAMESPACE}-container
    build:
      context: ./database
      dockerfile: ./.docker/db/Dockerfile.setup
    environment:
      - NODE_ENV=${NODE_ENV}
      - DB_HOST=${DB_HOST}
      - DB_ADMIN=${DB_ADMIN}
      - DB_ADMIN_PASS=${DB_ADMIN_PASS}
      - DB_PORT=5432
      - DB_DATABASE=${DB_DATABASE}
      - DB_SCHEMA=${DB_SCHEMA}
      - DB_USER_API=${DB_USER_API}
      - DB_USER_API_PASS=${DB_USER_API_PASS}
      - ENABLE_MOCK_FEATURE_SEEDING=${ENABLE_MOCK_FEATURE_SEEDING}
      - NUM_MOCK_FEATURE_SUBMISSIONS=${NUM_MOCK_FEATURE_SUBMISSIONS}
    volumes:
      - /opt/app-root/src/node_modules # prevents local node_modules overriding container node_modules
    networks:
      - faunalogic-network
    depends_on:
      db:
        condition: service_healthy
    command: ["npm", "run", "setup"]

  # Elasticsearch cluster
  es01:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    container_name: ${DOCKER_PROJECT_NAME}-es01-${DOCKER_NAMESPACE}-container
    environment:
      - node.name=es01
      - cluster.name=es-docker-cluster
      - discovery.seed_hosts=es02,es03
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - es_data01:/usr/share/elasticsearch/data
    ports:
      - 9200:9200
    networks:
      - faunalogic-network
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:9200/_cluster/health | grep -q '\"status\":\"green\"\\|\"status\":\"yellow\"'"]
      interval: 10s
      timeout: 10s
      retries: 30

  es02:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    container_name: ${DOCKER_PROJECT_NAME}-es02-${DOCKER_NAMESPACE}-container
    environment:
      - node.name=es02
      - cluster.name=es-docker-cluster
      - discovery.seed_hosts=es01,es03
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - es_data02:/usr/share/elasticsearch/data
    networks:
      - faunalogic-network

  es03:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    container_name: ${DOCKER_PROJECT_NAME}-es03-${DOCKER_NAMESPACE}-container
    environment:
      - node.name=es03
      - cluster.name=es-docker-cluster
      - discovery.seed_hosts=es01,es02
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - es_data03:/usr/share/elasticsearch/data
    networks:
      - faunalogic-network

  kibana:
    image: docker.elastic.co/kibana/kibana:7.17.0
    container_name: ${DOCKER_PROJECT_NAME}-kibana-${DOCKER_NAMESPACE}-container
    environment:
      - "ELASTICSEARCH_HOSTS=http://es01:9200"
    ports:
      - 5601:5601
    networks:
      - faunalogic-network
    depends_on:
      es01:
        condition: service_healthy

networks:
  faunalogic-network:
    driver: bridge

volumes:
  postgres:
    name: ${DOCKER_PROJECT_NAME}-db-${DOCKER_NAMESPACE}-vol
  es_data01:
    name: ${DOCKER_PROJECT_NAME}-es01-${DOCKER_NAMESPACE}-data
  es_data02:
    name: ${DOCKER_PROJECT_NAME}-es02-${DOCKER_NAMESPACE}-data
  es_data03:
    name: ${DOCKER_PROJECT_NAME}-es03-${DOCKER_NAMESPACE}-data
