# FaunaLogic Database Environment Configuration
# Copy this file to .env and modify values as needed

# Database Configuration
POSTGRES_DB=faunalogic
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
PGUSER=postgres

# Database Connection Settings (for external connections)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=faunalogic

# API User Configuration
API_USER=faunalogic_api
API_PASSWORD=flatpass

# Flyway Configuration
FLYWAY_URL=******************************************
FLYWAY_USER=postgres
FLYWAY_PASSWORD=postgres
FLYWAY_SCHEMAS=faunalogic,faunalogic_dapi_v1
FLYWAY_LOCATIONS=filesystem:/flyway/sql

# Backup Configuration
BACKUP_DIR=./backups
BACKUP_RETENTION_DAYS=7

# Development Settings
DEV_MODE=true
LOG_LEVEL=info

# Production Settings (set DEV_MODE=false for production)
# SSL_MODE=require
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem
# SSL_CA_PATH=/path/to/ca.pem

# Performance Tuning (adjust based on available resources)
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
POSTGRES_WORK_MEM=16MB
POSTGRES_MAINTENANCE_WORK_MEM=256MB
POSTGRES_MAX_CONNECTIONS=100

# Logging Configuration
POSTGRES_LOG_MIN_DURATION_STATEMENT=1000
POSTGRES_LOG_STATEMENT=none
POSTGRES_LOG_LEVEL=warning

# PostGIS Configuration
POSTGIS_GDAL_ENABLED_DRIVERS=ENABLE_ALL
POSTGIS_ENABLE_OUTDB_RASTERS=false

# Security Settings
PASSWORD_ENCRYPTION=scram-sha-256
MAX_LOCKS_PER_TRANSACTION=256

# Timezone
TZ=UTC

# Docker Compose Profiles
COMPOSE_PROFILES=