{"extends": ["eslint:recommended", "plugin:prettier/recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "rules": {"prettier/prettier": ["warn"], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/ban-types": ["error", {"types": {"object": false, "extendDefaults": true}}], "@typescript-eslint/ban-ts-comment": ["error", {"ts-expect-error": false, "ts-ignore": false, "ts-nocheck": false, "ts-check": false}], "@typescript-eslint/no-unused-vars": ["error", {"args": "all", "argsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "no-var": "error"}}