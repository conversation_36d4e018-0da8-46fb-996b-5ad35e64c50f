'use strict';

const { OpenShiftClientX } = require('pipeline-cli');
const path = require('path');

/**
 * Build the database setup (migrations, seeding, etc) image.
 *
 * @param {*} settings
 */
const dbSetupBuild = (settings) => {
  const phases = settings.phases;
  const options = settings.options;
  const phase = settings.phase;

  const oc = new OpenShiftClientX(Object.assign({ namespace: phases[phase].namespace }, options));

  const templatesLocalBaseUrl = oc.toFileUrl(path.resolve(__dirname, '../templates'));

  const name = `${phases[phase].name}-setup`;

  const objects = [];

  objects.push(
    ...oc.processDeploymentTemplate(`${templatesLocalBaseUrl}/db.setup.bc.yaml`, {
      param: {
        NAME: name,
        SUFFIX: phases[phase].suffix,
        VERSION: phases[phase].tag,
        SOURCE_CONTEXT_DIR: 'database',
        DB_SETUP_DOCKERFILE_PATH: phases[phase].dbSetupDockerfilePath,
        SOURCE_REPOSITORY_URL: oc.git.http_url,
        SOURCE_REPOSITORY_REF: phases[phase].branch || oc.git.ref,
        CPU_REQUEST: '50m',
        CPU_LIMIT: '1000m',
        MEMORY_REQUEST: '100Mi',
        MEMORY_LIMIT: '1.5Gi'
      }
    })
  );

  oc.applyRecommendedLabels(objects, name, phase, phases[phase].changeId, phases[phase].instance);
  oc.applyAndBuild(objects);
};

module.exports = { dbSetupBuild };
