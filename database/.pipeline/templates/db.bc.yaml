kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: postgresql
parameters:
  - name: NAME
    displayName: Name
    description: A name used for all objects
    required: true
  - name: SUFFIX
    displayName: Name Suffix
    description: A suffix appended to all objects
    required: true
  - name: TAG_NAME
    required: true
objects:
  - kind: ImageStream
    apiVersion: image.openshift.io/v1
    metadata:
      name: '${NAME}'
      labels:
        shared: 'true'
      annotations: {}
    spec:
      lookupPolicy:
        local: false
      tags:
        - name: '${TAG_NAME}'
          annotations:
          from:
            kind: ImageStreamTag
            name: postgres-postgis:17-bullseye
            namespace: a0ec71-tools
          generation: 1
          importPolicy: {}
          referencePolicy:
            type: Source
