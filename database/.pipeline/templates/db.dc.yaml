kind: Template
apiVersion: template.openshift.io/v1
labels:
  template: postgresql-persistent-template
metadata:
  name: faunalogic-platform-db-dc
  labels:
    build: faunalogic-platform-db
parameters:
  - description: The name of the OpenShift Service exposed for the database.
    displayName: Database Service Name
    name: NAME
    required: true
    value: 'postgresql'
  - name: NAMESPACE
    description: The OpenShift Namespace where the ImageStream resides.
    required: true
    value: 'a0ec71-tools'
  - name: BASE_IMAGE_REGISTRY_URL
    description: The base image registry URL
    value: image-registry.openshift-image-registry.svc:5000
  - name: IMAGE_STREAM_NAME
    description: The OpenShift ImageStream name.
    required: true
    value: 'postgres-postgis'
  - name: IMAGE_STREAM_VERSION
    description: Version of PostgreSQL image to be used (9.2, 9.4, 9.5 or latest).
    required: true
    value: '17-bullseye'
  - description: The name of the OpenShift Service exposed for the database.
    displayName: Database Service Name
    name: DATABASE_SERVICE_NAME
    required: true
    value: 'postgresql'
  - description: Username for PostgreSQL user that will be used for accessing the database.
    displayName: PostgreSQL Connection Username
    name: POSTGRES_USER
    required: true
    value: 'postgres'
  - description: Password for the PostgreSQL connection user.
    displayName: PostgreSQL Connection Password
    from: '[a-zA-Z0-9]{16}'
    generate: expression
    name: POSTGRES_PASSWORD
    required: true
  - description: Name of the PostgreSQL database accessed.
    displayName: PostgreSQL Database Name
    name: POSTGRES_DB
    required: true
    value: 'faunalogic'
  - name: PGDATA
    description: Path to PostgreSQL data directory
    value: '/var/lib/postgresql/data/17'
    required: false
  - name: TZ
    description: Database timezone
    required: false
    value: 'America/Vancouver'
  - description: Volume space available for data, e.g. 512Mi, 2Gi.
    displayName: Volume Capacity
    name: VOLUME_CAPACITY
    required: true
    value: '500Mi'
  - name: CPU_REQUEST
    value: '50m'
  - name: CPU_LIMIT
    value: '200m'
  - name: MEMORY_REQUEST
    value: '100Mi'
  - name: MEMORY_LIMIT
    value: '2Gi'
  - name: REPLICAS
    value: '1'
objects:
  - kind: Secret
    apiVersion: v1
    metadata:
      annotations:
        template.openshift.io/expose-database_name: "{.data['database-name']}"
        template.openshift.io/expose-database-user: "{.data['database-admin']}"
        template.openshift.io/expose-database-user-password: "{.data['database-admin-password']}"
        as-copy-of: faunalogic-creds
      name: '${DATABASE_SERVICE_NAME}'
    stringData:
      database-name: '${POSTGRES_DB}'
      database-user: '${POSTGRES_USER}'
      database-user-password: '${POSTGRES_PASSWORD}'

  - kind: Service
    apiVersion: v1
    metadata:
      annotations:
        template.openshift.io/expose-uri: postgres://{.spec.clusterIP}:{.spec.ports[?(.name=="postgresql")].port}
      name: '${DATABASE_SERVICE_NAME}'
    spec:
      ports:
        - name: postgresql
          nodePort: 0
          port: 5432
          protocol: TCP
          targetPort: 5432
      selector:
        name: '${DATABASE_SERVICE_NAME}'
      sessionAffinity: None
      type: ClusterIP

  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: '${DATABASE_SERVICE_NAME}'
    spec:
      accessModes:
        - ReadWriteMany
      resources:
        requests:
          storage: '${VOLUME_CAPACITY}'

  - kind: ImageStream
    apiVersion: image.openshift.io/v1
    metadata:
      name: '${IMAGE_STREAM_NAME}'
      labels:
        shared: 'true'
      annotations: {}
    spec:
      lookupPolicy:
        local: false

  - kind: Deployment
    apiVersion: apps/v1
    metadata:
      annotations:
        template.alpha.openshift.io/wait-for-ready: 'true'
      name: '${DATABASE_SERVICE_NAME}'
    spec:
      replicas: ${{REPLICAS}}
      selector:
        matchLabels:
          name: '${DATABASE_SERVICE_NAME}'
      strategy:
        type: Recreate
        maxSurge: 50%
        maxUnavailable: 0
      template:
        metadata:
          labels:
            name: '${DATABASE_SERVICE_NAME}'
            role: db
        spec:
          containers:
            - name: postgresql
              env:
                - name: POSTGRES_DB
                  valueFrom:
                    secretKeyRef:
                      key: database-name
                      name: '${DATABASE_SERVICE_NAME}'
                - name: POSTGRES_USER
                  valueFrom:
                    secretKeyRef:
                      key: database-admin
                      name: '${DATABASE_SERVICE_NAME}'
                - name: POSTGRES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      key: database-admin-password
                      name: '${DATABASE_SERVICE_NAME}'
                - name: PGDATA
                  value: '${PGDATA}'
                - name: PGOPTIONS
                  value: '-c maintenance_work_mem=128MB'
                - name: PGTZ
                  value: '${TZ}'
              image: ${BASE_IMAGE_REGISTRY_URL}/${NAMESPACE}/${IMAGE_STREAM_NAME}:${IMAGE_STREAM_VERSION}
              imagePullPolicy: IfNotPresent
              ports:
                - containerPort: 5432
                  protocol: TCP
              startupProbe:
                exec:
                  command:
                    - '/bin/sh'
                    - '-i'
                    - '-c'
                    - psql -h 127.0.0.1 -U $POSTGRES_USER -q -d $POSTGRES_DB -c 'SELECT 1'
                initialDelaySeconds: 30
                periodSeconds: 10
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 30
              readinessProbe:
                exec:
                  command:
                    - '/bin/sh'
                    - '-i'
                    - '-c'
                    - psql -h 127.0.0.1 -U $POSTGRES_USER -q -d $POSTGRES_DB -c 'SELECT 1'
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              livenessProbe:
                tcpSocket:
                  port: 5432
                initialDelaySeconds: 0
                periodSeconds: 30
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 3
              resources:
                requests:
                  cpu: ${CPU_REQUEST}
                  memory: ${MEMORY_REQUEST}
                limits:
                  cpu: ${CPU_LIMIT}
                  memory: ${MEMORY_LIMIT}
              securityContext: {}
              terminationMessagePath: '/dev/termination-log'
              volumeMounts:
                - name: '${DATABASE_SERVICE_NAME}-data'
                  mountPath: '/var/lib/postgresql/data'
          dnsPolicy: ClusterFirst
          restartPolicy: Always
          volumes:
            - name: '${DATABASE_SERVICE_NAME}-data'
              persistentVolumeClaim:
                claimName: '${DATABASE_SERVICE_NAME}'
