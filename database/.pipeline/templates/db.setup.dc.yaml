kind: Template
apiVersion: template.openshift.io/v1
metadata:
  name: faunalogic-platform-db-setup-dc
  labels:
    build: faunalogic-platform-db-setup
parameters:
  - name: IMAGE
    description: 'Image for pod'
    required: true
  - name: NAME
    value: faunalogic-platform-db-setup
  - name: SUFFIX
    value: '-dev'
  - name: VERSION
    description: Version of the application
    value: '1.0.0'
  - name: CHANGE_ID
    description: Change id of the project. This will help to pull image stream
    required: true
    value: '0'
  - name: DB_SERVICE_NAME
    description: 'Database service name associated  with deployment'
    required: true
  - name: DB_SCHEMA
    description: 'Database schema'
    required: true
  - name: NODE_ENV
    description: Application Environment type variable
    required: true
    value: 'development'
  - name: CPU_REQUEST
    value: '50m'
  - name: CPU_LIMIT
    value: '1000m'
  - name: MEMORY_REQUEST
    value: '100Mi'
  - name: MEMORY_LIMIT
    value: '1.75Gi'
objects:
  - kind: Pod
    apiVersion: v1
    metadata:
      name: '${NAME}'
      creationTimestamp:
      labels:
        role: setup
    spec:
      containers:
        - name: postgresql-setup
          image: '${IMAGE}'
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEMORY_REQUEST}
            limits:
              cpu: ${CPU_LIMIT}
              memory: ${MEMORY_LIMIT}
          env:
            - name: DB_HOST
              value: ${DB_SERVICE_NAME}
            - name: DB_ADMIN
              valueFrom:
                secretKeyRef:
                  key: database-admin
                  name: ${DB_SERVICE_NAME}
            - name: DB_ADMIN_PASS
              valueFrom:
                secretKeyRef:
                  key: database-admin-password
                  name: ${DB_SERVICE_NAME}
            - name: DB_USER_API
              valueFrom:
                secretKeyRef:
                  key: database-user-api
                  name: ${DB_SERVICE_NAME}
            - name: DB_USER_API_PASS
              valueFrom:
                secretKeyRef:
                  key: database-user-api-password
                  name: ${DB_SERVICE_NAME}
            - name: DB_DATABASE
              valueFrom:
                secretKeyRef:
                  key: database-name
                  name: ${DB_SERVICE_NAME}
            - name: DB_PORT
              value: '5432'
            - name: CHANGE_VERSION
              value: ${CHANGE_ID}
            - name: NODE_ENV
              value: ${NODE_ENV}
            - name: VERSION
              value: ${VERSION}
            - name: DB_SCHEMA
              value: ${DB_SCHEMA}
          imagePullPolicy: Always
      restartPolicy: Never
      activeDeadlineSeconds: 900
      dnsPolicy: ClusterFirst
