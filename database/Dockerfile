# FaunaLogic Database Docker Image
# PostGIS-enabled PostgreSQL with spatial extensions

FROM postgis/postgis:15-3.3

# Install additional PostgreSQL extensions
RUN apt-get update && apt-get install -y \
    postgresql-15-pgrouting \
    postgresql-15-pgrouting-scripts \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV POSTGRES_DB=faunalogic
ENV POSTGRES_USER=postgres
ENV POSTGRES_PASSWORD=postgres
ENV PGUSER=postgres

# Create directory for initialization scripts
RUN mkdir -p /docker-entrypoint-initdb.d

# Copy initialization scripts
COPY scripts/init/ /docker-entrypoint-initdb.d/

# Set permissions
RUN chmod +x /docker-entrypoint-initdb.d/*.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB

# Expose PostgreSQL port
EXPOSE 5432

# Use the default PostgreSQL entrypoint
CMD ["postgres"]