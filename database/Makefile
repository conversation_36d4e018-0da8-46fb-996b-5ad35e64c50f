# FaunaLogic Database Makefile
# Convenient commands for database management

.PHONY: help build up down logs clean migrate backup restore test

# Detect Docker Compose command (V1 vs V2)
DOCKER_COMPOSE_CMD := $(shell if command -v docker-compose >/dev/null 2>&1; then echo "docker-compose"; elif command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then echo "docker compose"; else echo ""; fi)

# Default environment
ENV ?= dev
COMPOSE_FILE_FLAG = -f docker-compose.yml
ifeq ($(ENV),prod)
	COMPOSE_FILE_FLAG = -f docker-compose.yml -f docker-compose.prod.yml
endif

# Check if Docker Compose is available
ifeq ($(DOCKER_COMPOSE_CMD),)
$(error Docker Compose is not installed. Please install Docker Compose V1 or V2)
endif

# Colors for help
YELLOW := \033[33m
GREEN := \033[32m
RED := \033[31m
NC := \033[0m # No Color

help: ## Show this help message
	@echo "$(GREEN)FaunaLogic Database Management$(NC)"
	@echo ""
	@echo "$(YELLOW)Usage:$(NC)"
	@echo "  make [command] [ENV=dev|prod]"
	@echo ""
	@echo "$(YELLOW)Commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Examples:$(NC)"
	@echo "  make up              # Start development database"
	@echo "  make migrate         # Run migrations"
	@echo "  make backup          # Create database backup"
	@echo "  make up ENV=prod     # Start production database"

build: ## Build database Docker image
	@echo "$(GREEN)Building database image...$(NC)"
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) build postgres

up: ## Start database services
	@echo "$(GREEN)Starting database services ($(ENV))...$(NC)"
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) up -d postgres
	@echo "$(GREEN)Database is starting up...$(NC)"
	@echo "Use 'make logs' to view startup progress"

down: ## Stop database services
	@echo "$(RED)Stopping database services...$(NC)"
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) down

logs: ## View database logs
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) logs -f postgres

clean: ## Remove all containers, volumes, and images
	@echo "$(RED)WARNING: This will remove all database data!$(NC)"
	@printf "Are you sure? (y/N): "; \
	read REPLY; \
	case "$$REPLY" in \
		[Yy]|[Yy][Ee][Ss]) \
			echo "$(GREEN)Removing containers and volumes...$(NC)"; \
			$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) down -v --remove-orphans; \
			docker system prune -f; \
			echo "$(GREEN)Cleanup completed$(NC)"; \
			;; \
		*) \
			echo "$(YELLOW)Cleanup cancelled$(NC)"; \
			;; \
	esac

migrate: ## Run database migrations
	@echo "$(GREEN)Running database migrations...$(NC)"
	./scripts/migrate.sh migrate

migrate-info: ## Show migration status
	@echo "$(GREEN)Getting migration information...$(NC)"
	./scripts/migrate.sh info

migrate-validate: ## Validate migrations
	@echo "$(GREEN)Validating migrations...$(NC)"
	./scripts/migrate.sh validate

migrate-clean: ## Clean database (removes all objects)
	@echo "$(RED)WARNING: This will remove all database objects!$(NC)"
	./scripts/migrate.sh clean

migrate-repair: ## Repair Flyway metadata table
	@echo "$(GREEN)Repairing Flyway metadata...$(NC)"
	./scripts/migrate.sh repair

seed-dev: ## Run development seed data migrations
	@echo "$(GREEN)Running development seed migrations...$(NC)"
	./scripts/migrate.sh migrate -locations=filesystem:/flyway/dev-seed -table=flyway_schema_history_seed -validateOnMigrate=false

seed-clean: ## Clean development seed data
	@echo "$(RED)WARNING: This will clean all development seed data!$(NC)"
	@printf "Are you sure? (y/N): "; \
	read REPLY; \
	case "$$REPLY" in \
		[Yy]|[Yy][Ee][Ss]) \
			echo "$(GREEN)Cleaning development seed data...$(NC)"; \
			./scripts/migrate.sh clean -locations=filesystem:/flyway/dev-seed -table=flyway_schema_history_seed; \
			echo "$(GREEN)Development seed data cleaned$(NC)"; \
			;; \
		*) \
			echo "$(YELLOW)Seed clean cancelled$(NC)"; \
			;; \
	esac

seed-info: ## Show development seed migration status
	@echo "$(GREEN)Getting development seed migration information...$(NC)"
	./scripts/migrate.sh info -locations=filesystem:/flyway/dev-seed -table=flyway_schema_history_seed

backup: ## Create database backup
	@echo "$(GREEN)Creating database backup...$(NC)"
	./scripts/backup.sh

restore: ## Restore database from backup
	@echo "$(YELLOW)Available backups:$(NC)"
	@ls -la backups/faunalogic_backup_*.backup 2>/dev/null || echo "No backups found"
	@read -p "Enter backup filename: " backup_file; \
	if [ -n "$$backup_file" ]; then \
		echo "$(GREEN)Restoring from $$backup_file...$(NC)"; \
		./scripts/restore.sh "$$backup_file"; \
	else \
		echo "$(RED)No backup file specified$(NC)"; \
	fi

test: ## Run database tests
	@echo "$(GREEN)Starting test database...$(NC)"
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) --profile test up -d postgres-test
	@echo "$(GREEN)Running database tests...$(NC)"
	# Add test commands here
	@echo "$(GREEN)Stopping test database...$(NC)"
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) --profile test down

psql: ## Connect to database with psql
	@echo "$(GREEN)Connecting to database...$(NC)"
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) exec postgres psql -U postgres -d faunalogic

psql-test: ## Connect to test database with psql
	@echo "$(GREEN)Connecting to test database...$(NC)"
	$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) --profile test exec postgres-test psql -U postgres -d faunalogic_test

setup: ## Initial setup (build, up, migrate)
	@echo "$(GREEN)Setting up FaunaLogic database...$(NC)"
	$(MAKE) build
	$(MAKE) up
	@echo "$(GREEN)Waiting for database to be ready...$(NC)"
	@sleep 10
	$(MAKE) migrate
	@echo "$(GREEN)Database setup completed!$(NC)"

reset: ## Reset database (down, clean, setup)
	@echo "$(RED)This will completely reset the database!$(NC)"
	@printf "Are you sure? (y/N): "; \
	read REPLY; \
	case "$$REPLY" in \
		[Yy]|[Yy][Ee][Ss]) \
			echo "$(GREEN)Resetting database...$(NC)"; \
			$(MAKE) down; \
			docker volume rm faunalogic_postgres_data 2>/dev/null || true; \
			$(MAKE) setup; \
			echo "$(GREEN)Database reset completed!$(NC)"; \
			;; \
		*) \
			echo "$(YELLOW)Reset cancelled$(NC)"; \
			;; \
	esac

status: ## Show database status
	@echo "$(GREEN)Database Status:$(NC)"
	@$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) ps
	@echo ""
	@echo "$(GREEN)Database Health:$(NC)"
	@$(DOCKER_COMPOSE_CMD) $(COMPOSE_FILE_FLAG) exec postgres pg_isready -U postgres -d faunalogic 2>/dev/null || echo "Database not ready"
	@echo ""
	@echo "$(GREEN)Migration Status:$(NC)"
	@./scripts/migrate.sh info 2>/dev/null || echo "Migration status unavailable"

dev: setup ## Alias for setup (for development)

prod-deploy: ## Deploy to production
	@echo "$(GREEN)Deploying to production...$(NC)"
	ENV=prod $(MAKE) build
	ENV=prod $(MAKE) up
	@sleep 15
	ENV=prod $(MAKE) migrate
	@echo "$(GREEN)Production deployment completed!$(NC)"