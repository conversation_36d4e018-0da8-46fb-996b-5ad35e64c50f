# FaunaLogic Database Service

PostgreSQL database with PostGIS spatial extensions for the FaunaLogic spatial wildlife data management system.

> **✅ PRODUCTION READY**: Complete database infrastructure with Docker, Flyway migrations, and spatial data support.

## 🏗️ Architecture

- **Database Engine**: PostgreSQL 15 with PostGIS 3.3
- **Migration Tool**: Flyway Community Edition for database versioning
- **Containerization**: Docker and Docker Compose (V1/V2 compatible)
- **Spatial Features**: Full PostGIS suite including routing and topology
- **API Access**: Dedicated `faunalogic_api` user with access to `faunalogic_dapi_v1` schema
- **Security**: Multi-tenant architecture with audit logging and spatial data transformations

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Make (optional, for convenience commands)

### Development Setup

1. **Clone and navigate to database directory**:
   ```bash
   cd database/
   ```

2. **Copy environment configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your preferred settings
   ```

3. **Start the database** (using Make):
   ```bash
   make setup
   ```
   
   This command will:
   - Build the PostgreSQL + PostGIS Docker image
   - Start the database container
   - Wait for database readiness
   - Run all Flyway migrations (V1.0.0 through V1.0.16)
   - Create the API user and schema

4. **Add development seed data** (optional):
   ```bash
   make seed-dev
   ```
   
   This creates test data for development:
   - Test tenant: "Test Tenant"
   - Test identity provider: "Dev Identity Provider"
   - Test user: "<EMAIL>"
   - Test source transform with sample metadata

5. **Verify setup**:
   ```bash
   make status
   ```

### Connection from WSL2/Windows

```bash
# Using the built-in command (recommended)
make psql

# Or direct connection (requires postgresql-client)
psql -h localhost -p 5432 -U postgres -d faunalogic
# Password: postgres

# Using Docker exec
docker exec -it faunalogic-postgres psql -U postgres -d faunalogic
```

### Alternative Manual Setup

```bash
# Build the database image
docker-compose build postgres

# Start PostgreSQL
docker-compose up -d postgres

# Wait for database to be ready
docker-compose exec postgres pg_isready -U postgres -d faunalogic

# Run migrations
./scripts/migrate.sh migrate

# Check migration status
./scripts/migrate.sh info
```

## 📁 Project Structure

```
database/
├── Dockerfile                    # PostgreSQL + PostGIS image
├── docker-compose.yml           # Development configuration
├── docker-compose.prod.yml      # Production overrides
├── Makefile                     # Convenience commands
├── README.md                    # This file
├── .env.example                 # Environment template
├── config/                      # Database configuration files
│   ├── flyway.conf             # Flyway settings
│   ├── postgresql.conf         # Development PostgreSQL config
│   ├── postgresql.prod.conf    # Production PostgreSQL config
│   ├── pg_hba.conf             # Development auth config
│   └── pg_hba.prod.conf        # Production auth config
├── migrations/                  # Flyway migrations (Versioned)
│   ├── V1_0_0__initial_database_setup.sql
│   ├── V1_0_1__create_spatial_extensions.sql
│   ├── V1_0_2__create_api_user.sql
│   ├── V1_0_3__create_core_tables.sql
│   ├── V1_0_4__create_sequences.sql
│   ├── V1_0_5__populate_system_constants.sql
│   ├── V1_0_6__populate_user_identity_source.sql
│   ├── V1_0_7__api_get_system_constant.sql
│   ├── V1_0_8__api_get_system_metadata_constant.sql
│   ├── V1_0_9__tr_audit_trigger.sql
│   ├── V1_0_10__tr_journal_trigger.sql
│   ├── V1_0_11__tr_generated_audit_triggers.sql
│   ├── V1_0_12__tr_generated_journal_triggers.sql
│   ├── V1_0_13__vw_generated_dapi_views.sql
│   ├── V1_0_14__api_set_context.sql
│   ├── V1_0_15__api_get_context_user_id.sql
│   └── V1_0_16__grant_context_to_api_user.sql
├── dev-seed/                   # Development seed data migrations
│   ├── V2_0_0__set_context.sql
│   ├── V2_0_1__seed_test_tenant.sql
│   ├── V2_0_2__seed_user_identity_source.sql
│   ├── V2_0_3__seed_application_user.sql
│   └── V2_0_4__seed_source_transform.sql
├── scripts/                     # Utility scripts
│   ├── init/                   # Database initialization
│   │   └── 01-init-database.sh
│   ├── backup.sh               # Backup script
│   ├── restore.sh              # Restore script
│   └── migrate.sh              # Migration helper
├── backups/                    # Database backups
└── src/migrations/release.0.1.0/  # Original migration files
```

## 🛠️ Available Commands

### Make Commands (Recommended)

```bash
make help          # Show all available commands
make up            # Start database services
make down          # Stop database services
make logs          # View database logs
make migrate       # Run database migrations
make backup        # Create database backup
make restore       # Restore from backup
make psql          # Connect to database
make status        # Show database status
make clean         # Remove all containers and volumes
make reset         # Complete database reset

# Development seed data commands
make seed-dev      # Run development seed migrations
make seed-info     # Show seed migration status
make seed-clean    # Clean development seed data
```

### Direct Script Usage

```bash
# Migration management
./scripts/migrate.sh migrate       # Run migrations
./scripts/migrate.sh info          # Show migration status
./scripts/migrate.sh validate      # Validate migrations
./scripts/migrate.sh clean         # Clean database (dangerous!)

# Backup and restore
./scripts/backup.sh                # Create backup
./scripts/restore.sh backup_file   # Restore from backup
```

## 🔐 API Access (IMPORTANT)

**⚠️ All application connections MUST use the dedicated API user and schema:**

```bash
# API Connection Details
Host: localhost
Port: 5432
Database: faunalogic
Username: faunalogic_api
Password: flatpass
Schema: faunalogic_dapi_v1
```

**Connection Examples:**

```bash
# psql connection
psql -h localhost -p 5432 -U faunalogic_api -d faunalogic

# Connection string
postgresql://faunalogic_api:flatpass@localhost:5432/faunalogic

# In application code, ensure you set the search_path:
SET search_path = faunalogic_dapi_v1, public;
```

**🚨 MANDATORY SESSION INITIALIZATION:**

**Every database session MUST begin with a call to `faunalogic.api_set_context` before any other operations:**

```sql
-- Required at the start of every session
SELECT faunalogic.api_set_context(
    p_tenant_id => 1,           -- Your tenant ID
    p_user_id => 123,           -- Current user ID
    p_application_context => 'web_app'  -- Application context
);
```

**🔒 CONCURRENCY CONTROL REQUIREMENT:**

**Every UPDATE statement MUST include a `revision_count` column value to enforce optimistic concurrency control:**

```sql
-- Correct: Include revision_count for concurrency
UPDATE spatial_component 
SET name = 'Updated Name',
    revision_count = revision_count + 1
WHERE id = 456 
    AND revision_count = 3;  -- Must match expected revision

-- Incorrect: Will fail - missing revision_count
UPDATE spatial_component 
SET name = 'Updated Name'
WHERE id = 456;
```

**Why use the API user?**
- ✅ **Restricted permissions** - Only access to necessary functions and views
- ✅ **Schema isolation** - Uses `faunalogic_dapi_v1` schema for API operations
- ✅ **Security** - No direct access to core tables (uses functions/views)
- ✅ **Audit compliance** - All API operations are logged with proper user context
- ✅ **Session context** - Required context setting for all operations
- ✅ **Concurrency control** - Revision tracking prevents data conflicts

**🚫 DO NOT use the `postgres` superuser for application connections!**

## 🌱 Development Seed Data

The database includes a separate seed data system for development and testing purposes.

### Quick Start with Seed Data

```bash
# After initial setup, add development seed data
make seed-dev

# Check what was created
make seed-info

# Query the test data
make psql -c "SET search_path = faunalogic, public; SELECT t.name as tenant_name, uis.name as identity_source, au.user_identifier FROM tenant t JOIN user_identity_source uis ON t.tenant_id = uis.tenant_id JOIN application_user au ON uis.user_identity_source_id = au.user_identity_source_id;"
```

### Seed Data Structure

The development seed creates a complete test hierarchy:

1. **Test Tenant** (`tenant` table)
   - Name: "Test Tenant"
   - Description: "Development test tenant for local testing and development"

2. **Dev Identity Provider** (`user_identity_source` table)
   - Name: "Dev Identity Provider"
   - Linked to Test Tenant
   - Used for development authentication testing

3. **Test Application User** (`application_user` table)
   - Identifier: "<EMAIL>"
   - UUID: "dev-user-uuid-12345"
   - Linked to Dev Identity Provider

4. **Sample Source Transform** (`source_transform` table)
   - Version: "1.0.0"
   - Metadata Index: "dev-test-index"
   - Sample JSON transform for testing
   - Linked to Test Application User

### Seed Data Commands

```bash
# Apply development seed data
make seed-dev

# View seed migration status
make seed-info

# Remove all seed data (with confirmation)
make seed-clean
```

### Seed Data Technical Details

- **Migration Files**: Located in `dev-seed/` directory
- **Version Range**: V2.0.0 through V2.0.4
- **Separate History**: Uses `flyway_schema_history_seed` table
- **Dependencies**: Requires core migrations (V1.0.x) to be applied first
- **Rollback**: Use `make seed-clean` to remove all seed data

### Using Seed Data in Development

Once seed data is applied, you can use it for API testing:

```sql
-- Set context for the test user
SELECT faunalogic.api_set_context('<EMAIL>', 'DATABASE');

-- Query using the test tenant and user data
SELECT * FROM faunalogic_dapi_v1.tenant WHERE name = 'Test Tenant';
```

**⚠️ Important**: Seed data is for development only. Never apply seed migrations to production databases.

## 🗃️ Database Schema

### Core Tables

- **`tenant`** - Multi-tenant organization support
- **`application_user`** - User accounts and profiles
- **`user_identity_source`** - Authentication provider mapping
- **`audit_log`** - Comprehensive audit trail
- **`system_constant`** - Application configuration
- **`system_metadata_constant`** - Metadata configuration

### Spatial Tables

- **`spatial_component`** - Base spatial data storage
- **`secure_spatial_component`** - Security-enhanced spatial data
- **`unsecure_spatial_component`** - Public spatial data

### Security Tables

- **`security_reason`** - Security classification reasons
- **`security_transform`** - Data transformation rules
- **`security_transform_submission`** - Applied transformations
- **`application_user_security_exception`** - Security exceptions

### Key Features

- **PostGIS Extensions**: Full spatial data support with geometry/geography types
- **Spatial Indexing**: GIST indexes for optimal spatial query performance
- **Multi-tenant Architecture**: Tenant-based data isolation
- **Audit Triggers**: Automatic change tracking for all tables
- **Security Transforms**: Configurable spatial data obfuscation
- **API Functions**: Database functions for application integration

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Database Settings
POSTGRES_DB=faunalogic
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# API User
API_USER=faunalogic_api
API_PASSWORD=flatpass

# Performance (adjust for your system)
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
POSTGRES_WORK_MEM=16MB
```

### Development vs Production

- **Development**: Uses `docker-compose.yml` with relaxed security
- **Production**: Uses `docker-compose.prod.yml` overlay with enhanced security

Switch to production mode:
```bash
ENV=prod make up
ENV=prod make migrate
```

## 🔐 Security

### Authentication

- **Development**: Trust authentication for local connections
- **Production**: SCRAM-SHA-256 authentication with SSL

### Access Control

- **API User**: Limited permissions for application connections
- **Admin User**: Full access for maintenance operations
- **Row Level Security**: Tenant-based data isolation (ready for implementation)

### Data Protection

- **Audit Logging**: All data changes tracked
- **Security Transforms**: Configurable spatial data obfuscation
- **Encrypted Storage**: Database-level encryption support
- **SSL Connections**: Required for production

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Check database health
make status

# Check migration status
./scripts/migrate.sh info

# Connect to database
make psql
```

### Backup Strategy

```bash
# Create manual backup
make backup

# Automated backups (production)
docker-compose --profile backup up -d backup
```

Backups include:
- Custom format (`.backup`) for full restore
- Compressed SQL (`.sql.gz`) for inspection
- Automatic cleanup (7-day retention)

### Performance Monitoring

Key PostgreSQL views for monitoring:
- `pg_stat_activity` - Current activity
- `pg_stat_statements` - Query performance (when enabled)
- `pg_stat_user_tables` - Table statistics
- `pg_stat_user_indexes` - Index usage

### Log Analysis

```bash
# View real-time logs
make logs

# Production log location
./logs/postgresql-*.log
./logs/postgresql-*.csv  # CSV format for analysis
```

## 🧪 Testing

### Test Database

```bash
# Start test database
docker-compose --profile test up -d postgres-test

# Connect to test database
make psql-test

# Run tests (implement your test suite)
make test
```

### Migration Testing

```bash
# Validate migrations
./scripts/migrate.sh validate

# Test migration on clean database
make clean
make migrate
```

## 🚨 Troubleshooting

### ✅ Fixed Issues (Applied in this release)

The following issues were identified and resolved:

1. **✅ Docker Compose command compatibility** - Fixed V1/V2 detection
2. **✅ Flyway Teams Edition features** - Removed incompatible settings for Community Edition
3. **✅ Script line endings** - Converted Windows CRLF to Unix LF format
4. **✅ Make clean command** - Fixed interactive prompts for POSIX shell compatibility
5. **✅ Audit trigger dependencies** - Reordered initial data to create system user first
6. **✅ Identity column constraints** - Added OVERRIDING SYSTEM VALUE for explicit IDs

### Common Issues

1. **Database won't start**:
   ```bash
   # Check logs
   make logs
   
   # Verify configuration
   docker-compose config
   
   # Check if port 5432 is already in use
   docker ps | grep 5432
   ```

2. **Migration failures**:
   ```bash
   # Check migration status
   ./scripts/migrate.sh info
   
   # Repair metadata if needed
   ./scripts/migrate.sh repair
   
   # Check for Flyway Community Edition compatibility
   grep -E "(skipExecutingMigrations|outputQueryResults|errorOverrides)" config/flyway.conf
   ```

3. **Permission denied errors**:
   ```bash
   # Fix script permissions (especially after Windows checkout)
   chmod +x scripts/*.sh scripts/init/*.sh
   
   # Fix line endings if needed
   sed -i 's/\r$//' scripts/*.sh scripts/init/*.sh
   ```

4. **PostGIS extension errors**:
   ```bash
   # Verify extensions in database
   make psql
   SELECT * FROM pg_available_extensions WHERE name LIKE 'postgis%';
   
   # Check if initialization script ran
   docker logs faunalogic-postgres | grep "PostGIS"
   ```

5. **WSL2 connection issues**:
   ```bash
   # Ensure Docker Desktop is running
   docker --version
   
   # Use the built-in connection
   make psql
   
   # Check if database is accessible
   timeout 5 docker exec faunalogic-postgres pg_isready -U postgres -d faunalogic
   ```

### Recovery Procedures

1. **Complete reset**:
   ```bash
   make reset
   ```

2. **Restore from backup**:
   ```bash
   make restore
   # Follow prompts to select backup file
   ```

3. **Fix broken migrations**:
   ```bash
   ./scripts/migrate.sh repair
   ./scripts/migrate.sh migrate
   ```

## 📚 Additional Resources

- [PostGIS Documentation](https://postgis.net/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Flyway Documentation](https://flywaydb.org/documentation/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)

## 🤝 Contributing

1. Make changes to migration files in `migrations/`
2. Test changes locally: `make reset && make migrate`
3. Validate migrations: `./scripts/migrate.sh validate`
4. Update documentation as needed
5. Submit pull request

## 📝 License

This project is part of the FaunaLogic spatial data management system.