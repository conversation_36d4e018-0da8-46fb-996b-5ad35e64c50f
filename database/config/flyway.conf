# Flyway Configuration for FaunaLogic Database

# Database connection settings
flyway.url=******************************************
flyway.user=postgres
flyway.password=postgres

# Schema configuration
flyway.schemas=faunalogic,faunalogic_dapi_v1
flyway.defaultSchema=faunalogic

# Migration settings
flyway.locations=filesystem:/flyway/sql
flyway.sqlMigrationPrefix=V
flyway.sqlMigrationSeparator=__
flyway.sqlMigrationSuffixes=.sql
flyway.repeatableSqlMigrationPrefix=R
flyway.encoding=UTF-8

# Baseline configuration
flyway.baselineOnMigrate=true
flyway.baselineVersion=0.0.0
flyway.baselineDescription=Initial baseline

# Validation settings
flyway.validateOnMigrate=true

# Transaction settings
flyway.group=true
flyway.mixed=true
flyway.outOfOrder=false

# PostgreSQL-specific settings
flyway.installedBy=flyway
flyway.createSchemas=true

# Placeholder configuration
flyway.placeholderReplacement=true
flyway.placeholderPrefix=${
flyway.placeholderSuffix=}
flyway.placeholders.database_name=faunalogic
flyway.placeholders.api_user=faunalogic_api
flyway.placeholders.api_password=flatpass

# Output settings

# Error handling

# Callbacks
flyway.callbacks=