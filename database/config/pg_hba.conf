# PostgreSQL Client Authentication Configuration File
# FaunaLogic Database

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     trust

# IPv4 local connections:
host    all             all             127.0.0.1/32            scram-sha-256
host    all             all             0.0.0.0/0               scram-sha-256

# IPv6 local connections:
host    all             all             ::1/128                 scram-sha-256

# Docker network connections
host    all             all             **********/12           scram-sha-256
host    all             all             ***********/16          scram-sha-256
host    all             all             10.0.0.0/8              scram-sha-256

# Replication connections
local   replication     all                                     trust
host    replication     all             127.0.0.1/32            scram-sha-256
host    replication     all             ::1/128                 scram-sha-256