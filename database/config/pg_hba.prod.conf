# PostgreSQL Production Client Authentication Configuration File
# FaunaLogic Database - Production Security Settings

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             postgres                                peer
local   all             all                                     md5

# IPv4 local connections (restrict to application servers only)
host    all             postgres        127.0.0.1/32            scram-sha-256
host    faunalogic      faunalogic_api  **********/24          scram-sha-256

# IPv6 local connections
host    all             postgres        ::1/128                 scram-sha-256

# SSL connections from application servers
hostssl all             faunalogic_api  0.0.0.0/0              scram-sha-256

# Replication connections (if using replication)
local   replication     postgres                                peer
host    replication     postgres        127.0.0.1/32            scram-sha-256
hostssl replication     postgres        **********/24          scram-sha-256

# Deny all other connections
host    all             all             0.0.0.0/0               reject