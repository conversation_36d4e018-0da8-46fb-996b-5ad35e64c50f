# PostgreSQL Production Configuration for FaunaLogic Database
# Optimized for production spatial data processing

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings (adjust based on available RAM)
shared_buffers = 1GB                    # 25% of RAM
effective_cache_size = 3GB              # 75% of RAM
work_mem = 32MB                         # Higher for complex spatial queries
maintenance_work_mem = 1GB              # For VACUUM, CREATE INDEX
autovacuum_work_mem = 512MB             # For autovacuum processes

# Checkpoints and WAL
wal_buffers = 32MB
checkpoint_completion_target = 0.9
checkpoint_timeout = 15min
max_wal_size = 4GB
min_wal_size = 512MB
wal_compression = on
wal_log_hints = on

# Query Planning
random_page_cost = 1.1
effective_io_concurrency = 200
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4

# Logging (Production)
log_destination = 'stderr,csvlog'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_file_mode = 0640
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 2000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_lock_waits = on
log_statement = 'ddl'
log_temp_files = 100MB
log_autovacuum_min_duration = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on

# CSV Logging for analysis
log_destination = 'stderr,csvlog'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.csv'

# Performance and Monitoring
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
track_activity_query_size = 1024

# PostGIS and Spatial Settings
shared_preload_libraries = 'postgis-3,pg_stat_statements'
max_locks_per_transaction = 512

# Autovacuum (tuned for spatial data)
autovacuum = on
autovacuum_max_workers = 6
autovacuum_naptime = 30s
autovacuum_vacuum_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_threshold = 50
autovacuum_analyze_scale_factor = 0.05
autovacuum_vacuum_cost_delay = 10ms
autovacuum_vacuum_cost_limit = 1000

# Security Settings
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
ssl_ca_file = 'ca.crt'
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
ssl_prefer_server_ciphers = on
password_encryption = scram-sha-256

# Connection Security
authentication_timeout = 60s
tcp_keepalives_idle = 600
tcp_keepalives_interval = 30
tcp_keepalives_count = 3

# Locale Settings
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# Time Zone
timezone = 'UTC'

# Archive Settings (for point-in-time recovery)
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/archive/%f'
archive_timeout = 300

# Replication Settings (if needed)
wal_level = replica
max_wal_senders = 3
wal_keep_size = 1GB

# Performance Settings for Spatial Queries
enable_seqscan = on
enable_indexscan = on
enable_bitmapscan = on
enable_hashjoin = on
enable_mergejoin = on
enable_nestloop = on
enable_sort = on
enable_gathermerge = on

# Optimizer Settings
default_statistics_target = 500        # Higher for better spatial query planning
constraint_exclusion = partition
cpu_tuple_cost = 0.01
cpu_index_tuple_cost = 0.005
cpu_operator_cost = 0.0025
random_page_cost = 1.1
seq_page_cost = 1.0

# Custom Settings for PostGIS
postgis.gdal_enabled_drivers = 'ENABLE_ALL'
postgis.enable_outdb_rasters = false

# Custom Variable Classes
custom_variable_classes = 'postgis'

# Statement Timeout (prevent runaway queries)
statement_timeout = 1800000             # 30 minutes
lock_timeout = 300000                   # 5 minutes
idle_in_transaction_session_timeout = 600000  # 10 minutes

# Background Writer
bgwriter_delay = 100ms
bgwriter_lru_maxpages = 1000
bgwriter_lru_multiplier = 10.0

# Vacuum and Analyze
vacuum_cost_delay = 10ms
vacuum_cost_page_hit = 1
vacuum_cost_page_miss = 10
vacuum_cost_page_dirty = 20
vacuum_cost_limit = 2000