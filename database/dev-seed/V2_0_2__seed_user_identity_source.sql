--
-- Dev Seed: User Identity Source
-- Creates test user identity source for development purposes
--

SET search_path = faunalogic, public;

SELECT api_set_context((
  SELECT user_uuid
  FROM application_user
  WHERE user_identifier = session_user
  limit 1
));

INSERT INTO user_identity_source (
    tenant_id, 
    name, 
    description, 
    notes,
    create_user
) 
SELECT 
    t.tenant_id,
    'Dev Identity Provider',
    'Development user identity provider for local testing',
    'Used for development and testing purposes only',
    'dev-seed'
FROM tenant t 
WHERE t.name = 'Test Tenant';