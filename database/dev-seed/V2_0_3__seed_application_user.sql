--
-- Dev Seed: Application User
-- Creates test application user for development purposes
--

SET search_path = faunalogic, public;

SELECT api_set_context((
  SELECT user_uuid
  FROM application_user
  WHERE user_identifier = session_user
  limit 1
));

INSERT INTO application_user (
    user_identity_source_id,
    user_identifier,
    user_uuid,
    create_user
)
SELECT 
    uis.user_identity_source_id,
    '<EMAIL>',
    'cd19238b-6fb3-46a6-8129-2ab6c94e91cd',
    'dev-seed'
FROM user_identity_source uis
JOIN tenant t ON uis.tenant_id = t.tenant_id
WHERE t.name = 'Test Tenant' 
  AND uis.name = 'Dev Identity Provider';