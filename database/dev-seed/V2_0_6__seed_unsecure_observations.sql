SET search_path = faunalogic, public;

SELECT api_set_context('cd19238b-6fb3-46a6-8129-2ab6c94e91cd');

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-126.747396, 56.552212]}, "properties": {"eventID": "evt-0001", "eventDate": "2023-03-01", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "122.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-126.747396, 56.552212), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-126.747396, 56.552212), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.21884, 57.479643]}, "properties": {"eventID": "evt-0007", "eventDate": "2023-01-26", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "25.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.21884, 57.479643), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.21884, 57.479643), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.264735, 54.882177]}, "properties": {"eventID": "evt-0010", "eventDate": "2023-01-30", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "3.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "135.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.264735, 54.882177), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.264735, 54.882177), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.036692, 49.258268]}, "properties": {"eventID": "evt-0013", "eventDate": "2023-11-19", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-2.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "10.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.036692, 49.258268), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.036692, 49.258268), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.565023, 49.16922]}, "properties": {"eventID": "evt-0014", "eventDate": "2023-12-11", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "11.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "127.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.565023, 49.16922), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.565023, 49.16922), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.395902, 50.509992]}, "properties": {"eventID": "evt-0015", "eventDate": "2023-05-07", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-6.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "104.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.395902, 50.509992), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.395902, 50.509992), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.904821, 58.448997]}, "properties": {"eventID": "evt-0020", "eventDate": "2023-12-13", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-0.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "9.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.904821, 58.448997), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.904821, 58.448997), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.709507, 50.058692]}, "properties": {"eventID": "evt-0025", "eventDate": "2023-05-06", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "5.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "109.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.709507, 50.058692), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.709507, 50.058692), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.99997, 56.728089]}, "properties": {"eventID": "evt-0028", "eventDate": "2023-12-14", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "148.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.99997, 56.728089), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.99997, 56.728089), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.698625, 54.19988]}, "properties": {"eventID": "evt-0033", "eventDate": "2023-06-24", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "13.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "45.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.698625, 54.19988), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.698625, 54.19988), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.712291, 54.135505]}, "properties": {"eventID": "evt-0034", "eventDate": "2023-03-12", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "12.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "92.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.712291, 54.135505), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.712291, 54.135505), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-129.398589, 59.621375]}, "properties": {"eventID": "evt-0035", "eventDate": "2023-07-24", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-1.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "76.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-129.398589, 59.621375), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-129.398589, 59.621375), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.770564, 54.370305]}, "properties": {"eventID": "evt-0036", "eventDate": "2023-02-19", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "12.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "132.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.770564, 54.370305), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.770564, 54.370305), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.554717, 56.765275]}, "properties": {"eventID": "evt-0037", "eventDate": "2023-06-18", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "4.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "84.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.554717, 56.765275), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.554717, 56.765275), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.035188, 53.285126]}, "properties": {"eventID": "evt-0039", "eventDate": "2023-03-12", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "4.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "88.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.035188, 53.285126), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.035188, 53.285126), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.314219, 53.055751]}, "properties": {"eventID": "evt-0040", "eventDate": "2023-12-19", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-9.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "127.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.314219, 53.055751), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.314219, 53.055751), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.443313, 56.778498]}, "properties": {"eventID": "evt-0044", "eventDate": "2023-07-13", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "3.2", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "68.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.443313, 56.778498), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.443313, 56.778498), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.795832, 55.353635]}, "properties": {"eventID": "evt-0048", "eventDate": "2023-09-18", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-1.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "69.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.795832, 55.353635), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.795832, 55.353635), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.990624, 58.192869]}, "properties": {"eventID": "evt-0051", "eventDate": "2023-08-04", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-7.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "10.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.990624, 58.192869), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.990624, 58.192869), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-126.855992, 50.66376]}, "properties": {"eventID": "evt-0053", "eventDate": "2023-09-17", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "2.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "133.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-126.855992, 50.66376), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-126.855992, 50.66376), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.585789, 51.940014]}, "properties": {"eventID": "evt-0054", "eventDate": "2023-06-30", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "3.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "86.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.585789, 51.940014), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.585789, 51.940014), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.917645, 52.163847]}, "properties": {"eventID": "evt-0056", "eventDate": "2023-08-03", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "149.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.917645, 52.163847), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.917645, 52.163847), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.923509, 58.531625]}, "properties": {"eventID": "evt-0059", "eventDate": "2023-09-03", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-7.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "135.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.923509, 58.531625), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.923509, 58.531625), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-126.705634, 51.04116]}, "properties": {"eventID": "evt-0062", "eventDate": "2023-11-15", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "1.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "41.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-126.705634, 51.04116), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-126.705634, 51.04116), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.477738, 55.179772]}, "properties": {"eventID": "evt-0066", "eventDate": "2023-05-26", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-9.8", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "122.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.477738, 55.179772), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.477738, 55.179772), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.503217, 53.747891]}, "properties": {"eventID": "evt-0070", "eventDate": "2023-10-17", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "56.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.503217, 53.747891), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.503217, 53.747891), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-126.784531, 58.360898]}, "properties": {"eventID": "evt-0073", "eventDate": "2023-04-30", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "17.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "94.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-126.784531, 58.360898), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-126.784531, 58.360898), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.898455, 50.769015]}, "properties": {"eventID": "evt-0074", "eventDate": "2023-04-13", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-4.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "88.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.898455, 50.769015), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.898455, 50.769015), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.179345, 52.109624]}, "properties": {"eventID": "evt-0080", "eventDate": "2023-10-15", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "6.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "85.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.179345, 52.109624), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.179345, 52.109624), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.590218, 50.370657]}, "properties": {"eventID": "evt-0081", "eventDate": "2023-12-29", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-0.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "123.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.590218, 50.370657), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.590218, 50.370657), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.073375, 50.644099]}, "properties": {"eventID": "evt-0083", "eventDate": "2023-02-23", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "24.2", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "11.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.073375, 50.644099), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.073375, 50.644099), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.964145, 58.480539]}, "properties": {"eventID": "evt-0084", "eventDate": "2023-04-04", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "9.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "18.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.964145, 58.480539), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.964145, 58.480539), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.423067, 59.658272]}, "properties": {"eventID": "evt-0091", "eventDate": "2023-01-03", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "8.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "23.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.423067, 59.658272), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.423067, 59.658272), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.29959, 51.254293]}, "properties": {"eventID": "evt-0092", "eventDate": "2023-10-03", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "20.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "89.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.29959, 51.254293), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.29959, 51.254293), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.026875, 58.250985]}, "properties": {"eventID": "evt-0096", "eventDate": "2023-01-14", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "28.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.026875, 58.250985), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.026875, 58.250985), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.950723, 52.182763]}, "properties": {"eventID": "evt-0099", "eventDate": "2023-12-29", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "44.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.950723, 52.182763), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.950723, 52.182763), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.024016, 57.679827]}, "properties": {"eventID": "evt-0100", "eventDate": "2023-05-21", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "1.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "42.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.024016, 57.679827), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.024016, 57.679827), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.471383, 53.090391]}, "properties": {"eventID": "evt-0101", "eventDate": "2023-08-16", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "2.2", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "128.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.471383, 53.090391), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.471383, 53.090391), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.845144, 50.176228]}, "properties": {"eventID": "evt-0104", "eventDate": "2023-07-30", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "24.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "128.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.845144, 50.176228), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.845144, 50.176228), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-129.817733, 56.858898]}, "properties": {"eventID": "evt-0105", "eventDate": "2023-02-02", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-9.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "78.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-129.817733, 56.858898), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-129.817733, 56.858898), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.056846, 50.894]}, "properties": {"eventID": "evt-0106", "eventDate": "2023-11-02", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "2.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "78.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.056846, 50.894), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.056846, 50.894), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.504449, 54.203699]}, "properties": {"eventID": "evt-0109", "eventDate": "2023-10-13", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "21.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "115.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.504449, 54.203699), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.504449, 54.203699), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.498711, 50.602268]}, "properties": {"eventID": "evt-0110", "eventDate": "2023-12-23", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "3.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "51.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.498711, 50.602268), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.498711, 50.602268), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.152461, 57.44415]}, "properties": {"eventID": "evt-0111", "eventDate": "2023-05-18", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-5.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "56.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.152461, 57.44415), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.152461, 57.44415), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.405449, 58.573804]}, "properties": {"eventID": "evt-0112", "eventDate": "2023-07-07", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "11.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "39.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.405449, 58.573804), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.405449, 58.573804), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.991065, 56.457127]}, "properties": {"eventID": "evt-0115", "eventDate": "2023-08-23", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "3.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "32.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.991065, 56.457127), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.991065, 56.457127), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.324021, 59.72616]}, "properties": {"eventID": "evt-0116", "eventDate": "2023-08-20", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "12.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "88.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.324021, 59.72616), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.324021, 59.72616), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.460281, 56.822416]}, "properties": {"eventID": "evt-0118", "eventDate": "2023-05-25", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "16.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "120.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.460281, 56.822416), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.460281, 56.822416), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.942049, 51.10865]}, "properties": {"eventID": "evt-0119", "eventDate": "2023-10-13", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-2.0", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "128.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.942049, 51.10865), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.942049, 51.10865), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.054966, 55.739883]}, "properties": {"eventID": "evt-0120", "eventDate": "2023-06-07", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "19.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "121.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.054966, 55.739883), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.054966, 55.739883), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.228576, 49.781335]}, "properties": {"eventID": "evt-0125", "eventDate": "2023-10-21", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "19.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "107.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.228576, 49.781335), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.228576, 49.781335), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.545487, 50.792381]}, "properties": {"eventID": "evt-0127", "eventDate": "2023-09-20", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "4.8", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "95.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.545487, 50.792381), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.545487, 50.792381), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.822319, 51.471085]}, "properties": {"eventID": "evt-0130", "eventDate": "2023-08-08", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "10.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "89.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.822319, 51.471085), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.822319, 51.471085), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-129.97774, 58.382292]}, "properties": {"eventID": "evt-0131", "eventDate": "2023-03-10", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-2.8", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "28.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-129.97774, 58.382292), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-129.97774, 58.382292), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.833025, 50.189982]}, "properties": {"eventID": "evt-0137", "eventDate": "2023-10-01", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "5.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "1.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.833025, 50.189982), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.833025, 50.189982), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.920257, 54.070543]}, "properties": {"eventID": "evt-0138", "eventDate": "2023-03-26", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "13.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "66.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.920257, 54.070543), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.920257, 54.070543), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.857084, 48.98613]}, "properties": {"eventID": "evt-0141", "eventDate": "2023-01-24", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "5.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "116.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.857084, 48.98613), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.857084, 48.98613), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.870692, 53.146414]}, "properties": {"eventID": "evt-0142", "eventDate": "2023-08-17", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "6.2", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "45.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.870692, 53.146414), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.870692, 53.146414), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.022638, 55.436346]}, "properties": {"eventID": "evt-0145", "eventDate": "2023-09-07", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "10.8", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "7.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.022638, 55.436346), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.022638, 55.436346), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-129.42247, 58.274558]}, "properties": {"eventID": "evt-0146", "eventDate": "2023-04-24", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-5.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "137.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-129.42247, 58.274558), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-129.42247, 58.274558), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-129.335114, 53.274568]}, "properties": {"eventID": "evt-0151", "eventDate": "2023-10-30", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "13.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "18.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-129.335114, 53.274568), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-129.335114, 53.274568), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.457615, 50.231421]}, "properties": {"eventID": "evt-0152", "eventDate": "2023-05-02", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "7.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "98.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.457615, 50.231421), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.457615, 50.231421), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.742044, 58.153317]}, "properties": {"eventID": "evt-0155", "eventDate": "2023-03-30", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-1.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "131.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.742044, 58.153317), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.742044, 58.153317), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-127.108965, 48.674134]}, "properties": {"eventID": "evt-0156", "eventDate": "2023-06-19", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-8.8", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "60.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-127.108965, 48.674134), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-127.108965, 48.674134), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.777111, 54.459662]}, "properties": {"eventID": "evt-0160", "eventDate": "2023-09-12", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "2.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "19.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.777111, 54.459662), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.777111, 54.459662), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.524675, 49.633716]}, "properties": {"eventID": "evt-0161", "eventDate": "2023-01-26", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "17.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "70.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.524675, 49.633716), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.524675, 49.633716), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.354788, 50.427965]}, "properties": {"eventID": "evt-0162", "eventDate": "2023-09-21", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "13.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "72.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.354788, 50.427965), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.354788, 50.427965), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.466501, 57.627655]}, "properties": {"eventID": "evt-0165", "eventDate": "2023-10-08", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "21.8", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "79.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.466501, 57.627655), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.466501, 57.627655), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.899297, 49.010915]}, "properties": {"eventID": "evt-0167", "eventDate": "2023-10-27", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "19.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "101.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.899297, 49.010915), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.899297, 49.010915), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-126.17211, 48.343308]}, "properties": {"eventID": "evt-0170", "eventDate": "2023-08-11", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 2, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-1.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "91.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-126.17211, 48.343308), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-126.17211, 48.343308), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.886776, 54.442586]}, "properties": {"eventID": "evt-0171", "eventDate": "2023-01-24", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "13.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "101.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.886776, 54.442586), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.886776, 54.442586), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.110893, 53.867021]}, "properties": {"eventID": "evt-0177", "eventDate": "2023-07-07", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-9.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "30.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.110893, 53.867021), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.110893, 53.867021), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.73213, 58.343913]}, "properties": {"eventID": "evt-0187", "eventDate": "2023-04-26", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "84.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.73213, 58.343913), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.73213, 58.343913), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.452053, 55.911138]}, "properties": {"eventID": "evt-0189", "eventDate": "2023-04-03", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-6.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "63.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.452053, 55.911138), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.452053, 55.911138), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.808104, 49.56922]}, "properties": {"eventID": "evt-0190", "eventDate": "2023-01-02", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "8.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "148.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.808104, 49.56922), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.808104, 49.56922), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.926056, 55.434248]}, "properties": {"eventID": "evt-0192", "eventDate": "2023-04-19", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "4.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "117.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.926056, 55.434248), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.926056, 55.434248), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.205844, 56.864834]}, "properties": {"eventID": "evt-0193", "eventDate": "2023-01-29", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 1, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "19.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "137.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.205844, 56.864834), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.205844, 56.864834), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.846223, 56.866202]}, "properties": {"eventID": "evt-0195", "eventDate": "2023-03-28", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 5, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "18.8", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "107.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.846223, 56.866202), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.846223, 56.866202), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.272754, 49.061767]}, "properties": {"eventID": "evt-0196", "eventDate": "2023-02-07", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 4, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-5.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "14.2", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.272754, 49.061767), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.272754, 49.061767), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-128.666983, 58.612338]}, "properties": {"eventID": "evt-0197", "eventDate": "2023-07-10", "habitat": "boreal forest", "scientificName": "Alces alces", "individualCount": 3, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "15.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "4.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#8D4004", "strokeColor": "#5D2701", "strokeWidth": 3, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-128.666983, 58.612338), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-128.666983, 58.612338), 4326))
);

