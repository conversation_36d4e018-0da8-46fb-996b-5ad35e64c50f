-- V2_0_5__seed_submission.sql
-- Seed data for submission and submission_observation tables

SET search_path = faunalogic, public;

SELECT api_set_context('cd19238b-6fb3-46a6-8129-2ab6c94e91cd');

-- Insert a single submission record
INSERT INTO submission (source_transform_id)
select st.source_transform_id 
from source_transform st,
	application_user au
where au.user_uuid = 'cd19238b-6fb3-46a6-8129-2ab6c94e91cd'
  and st.version = '1.0.0'
;

-- Insert a single submission_observation record with the Darwin Core source data
-- note that we can't exactly identify the submission and the UUID is generated at insert
INSERT INTO submission_observation (submission_id, darwin_core_source)
VALUES (
    (SELECT submission_id FROM submission ORDER BY submission_id DESC LIMIT 1),
    $darwin_core$[
  {}
]$darwin_core$::jsonb
);