SET search_path = faunalogic, public;

SELECT api_set_context('cd19238b-6fb3-46a6-8129-2ab6c94e91cd');

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-120.621928, 58.579949]}, "properties": {"eventID": "evt-1001", "eventDate": "2023-04-09", "habitat": "urban", "scientificName": "Myotis lucifugus", "individualCount": 7, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "21.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "22.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "7", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-120.621928, 58.579949), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-120.621928, 58.579949), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.238832, 49.228528]}, "properties": {"eventID": "evt-1002", "eventDate": "2023-03-24", "habitat": "mixed forest", "scientificName": "Myotis lucifugus", "individualCount": 6, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-3.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "39.7", "measurementUnit": "cm"}], "markerType": "pin", "iconImage": "marker-15", "iconSize": 1.5, "iconAnchor": "bottom", "text": "6", "textColor": "white", "textSize": 12, "textAnchor": "top", "textOffset": [0, 1]}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.238832, 49.228528), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.238832, 49.228528), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-119.847747, 49.207764]}, "properties": {"eventID": "evt-1007", "eventDate": "2023-11-04", "habitat": "mixed forest", "scientificName": "Myotis lucifugus", "individualCount": 10, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "5.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "7.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 18, "fillColor": "#ff6b35", "strokeColor": "#d63031", "strokeWidth": 3, "fillOpacity": 0.7, "strokeOpacity": 1, "text": "10", "textColor": "white", "textSize": 14}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-119.847747, 49.207764), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-119.847747, 49.207764), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.497314, 52.929664]}, "properties": {"eventID": "evt-1008", "eventDate": "2023-02-21", "habitat": "urban", "scientificName": "Myotis lucifugus", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "2.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "43.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.497314, 52.929664), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.497314, 52.929664), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.12473, 56.136544]}, "properties": {"eventID": "evt-1010", "eventDate": "2023-04-16", "habitat": "coniferous forest", "scientificName": "Myotis lucifugus", "individualCount": 5, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "0.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "46.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.12473, 56.136544), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.12473, 56.136544), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-118.54784, 54.286729]}, "properties": {"eventID": "evt-1016", "eventDate": "2023-06-04", "habitat": "mixed forest", "scientificName": "Myotis lucifugus", "individualCount": 10, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "1.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "19.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "10", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-118.54784, 54.286729), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-118.54784, 54.286729), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-114.075285, 57.413127]}, "properties": {"eventID": "evt-1017", "eventDate": "2023-07-15", "habitat": "riparian zone", "scientificName": "Myotis lucifugus", "individualCount": 5, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "8.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "14.6", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-114.075285, 57.413127), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-114.075285, 57.413127), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-129.623762, 55.840078]}, "properties": {"eventID": "evt-1018", "eventDate": "2023-01-28", "habitat": "wetlands", "scientificName": "Myotis lucifugus", "individualCount": 8, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "12.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "46.5", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "8", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-129.623762, 55.840078), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-129.623762, 55.840078), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-129.989889, 58.65382]}, "properties": {"eventID": "evt-1022", "eventDate": "2023-08-18", "habitat": "mixed forest", "scientificName": "Myotis lucifugus", "individualCount": 6, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "10.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "27.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "6", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-129.989889, 58.65382), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-129.989889, 58.65382), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-115.856819, 55.227769]}, "properties": {"eventID": "evt-1023", "eventDate": "2023-02-27", "habitat": "riparian zone", "scientificName": "Myotis lucifugus", "individualCount": 1, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-7.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "46.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "1", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-115.856819, 55.227769), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-115.856819, 55.227769), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.882226, 51.85498]}, "properties": {"eventID": "evt-1028", "eventDate": "2023-02-27", "habitat": "coniferous forest", "scientificName": "Myotis lucifugus", "individualCount": 6, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "13.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "37.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "6", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.882226, 51.85498), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.882226, 51.85498), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-117.837843, 57.82811]}, "properties": {"eventID": "evt-1029", "eventDate": "2023-10-05", "habitat": "riparian zone", "scientificName": "Myotis lucifugus", "individualCount": 2, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-1.1", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "3.4", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-117.837843, 57.82811), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-117.837843, 57.82811), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-124.764553, 49.979404]}, "properties": {"eventID": "evt-1030", "eventDate": "2023-12-18", "habitat": "coniferous forest", "scientificName": "Myotis lucifugus", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "2.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "5.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-124.764553, 49.979404), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-124.764553, 49.979404), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-115.63662, 54.32468]}, "properties": {"eventID": "evt-1032", "eventDate": "2023-05-30", "habitat": "mixed forest", "scientificName": "Myotis lucifugus", "individualCount": 2, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-1.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "35.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-115.63662, 54.32468), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-115.63662, 54.32468), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-118.355446, 52.889798]}, "properties": {"eventID": "evt-1033", "eventDate": "2023-11-28", "habitat": "caves", "scientificName": "Myotis lucifugus", "individualCount": 2, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "1.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "32.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "2", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-118.355446, 52.889798), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-118.355446, 52.889798), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.607251, 51.894026]}, "properties": {"eventID": "evt-1035", "eventDate": "2023-03-01", "habitat": "caves", "scientificName": "Myotis lucifugus", "individualCount": 8, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "6.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "30.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "8", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.607251, 51.894026), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.607251, 51.894026), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-123.975575, 48.711464]}, "properties": {"eventID": "evt-1036", "eventDate": "2023-08-07", "habitat": "urban", "scientificName": "Myotis lucifugus", "individualCount": 7, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "14.5", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "41.3", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "7", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-123.975575, 48.711464), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-123.975575, 48.711464), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.803899, 50.749895]}, "properties": {"eventID": "evt-1037", "eventDate": "2023-10-07", "habitat": "mixed forest", "scientificName": "Myotis lucifugus", "individualCount": 6, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-9.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "48.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "6", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.803899, 50.749895), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.803899, 50.749895), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.254685, 58.717368]}, "properties": {"eventID": "evt-1039", "eventDate": "2023-03-07", "habitat": "caves", "scientificName": "Myotis lucifugus", "individualCount": 4, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "15.4", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "42.8", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.254685, 58.717368), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.254685, 58.717368), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.345006, 54.201004]}, "properties": {"eventID": "evt-1041", "eventDate": "2023-07-12", "habitat": "mixed forest", "scientificName": "Myotis lucifugus", "individualCount": 4, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "-5.2", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "16.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "4", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.345006, 54.201004), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.345006, 54.201004), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-121.92113, 48.95249]}, "properties": {"eventID": "evt-1042", "eventDate": "2023-09-15", "habitat": "coniferous forest", "scientificName": "Myotis lucifugus", "individualCount": 3, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "17.3", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "20.7", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "3", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-121.92113, 48.95249), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-121.92113, 48.95249), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-125.298708, 50.090508]}, "properties": {"eventID": "evt-1043", "eventDate": "2023-10-04", "habitat": "urban", "scientificName": "Myotis lucifugus", "individualCount": 7, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "8.6", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "8.9", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "7", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-125.298708, 50.090508), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-125.298708, 50.090508), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-118.943336, 48.425086]}, "properties": {"eventID": "evt-1046", "eventDate": "2023-11-12", "habitat": "riparian zone", "scientificName": "Myotis lucifugus", "individualCount": 5, "sex": "female", "measurements": [{"measurementType": "Air temperature", "measurementValue": "10.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "47.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "5", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-118.943336, 48.425086), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-118.943336, 48.425086), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-122.685651, 54.4336]}, "properties": {"eventID": "evt-1047", "eventDate": "2023-12-29", "habitat": "caves", "scientificName": "Myotis lucifugus", "individualCount": 7, "sex": "male", "measurements": [{"measurementType": "Air temperature", "measurementValue": "24.9", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "25.0", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "7", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-122.685651, 54.4336), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-122.685651, 54.4336), 4326))
);

INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Point", "coordinates": [-115.67461, 56.763167]}, "properties": {"eventID": "evt-1049", "eventDate": "2023-03-23", "habitat": "coniferous forest", "scientificName": "Myotis lucifugus", "individualCount": 9, "sex": "unknown", "measurements": [{"measurementType": "Air temperature", "measurementValue": "2.7", "measurementUnit": "Celsius"}, {"measurementType": "Snow depth", "measurementValue": "14.1", "measurementUnit": "cm"}], "markerType": "circle", "radius": 15, "fillColor": "#3388ff", "strokeColor": "#0066cc", "strokeWidth": 2, "fillOpacity": 0.8, "strokeOpacity": 1, "text": "9", "textColor": "white", "textSize": 12}}'::jsonb,
    ST_Transform(ST_SetSRID(ST_MakePoint(-115.67461, 56.763167), 4326), 3005),
    geography(ST_SetSRID(ST_MakePoint(-115.67461, 56.763167), 4326))
);

-- Add example polygon feature (habitat area)
INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "Polygon", "coordinates": [[[-125.0, 50.0], [-124.0, 50.0], [-124.0, 51.0], [-125.0, 51.0], [-125.0, 50.0]]]}, "properties": {"id": "habitat-001", "name": "Myotis Core Habitat", "habitatType": "cave system", "area": "2.5 km²", "fillColor": "#74b9ff", "fillOpacity": 0.4, "strokeColor": "#0984e3", "strokeWidth": 2, "strokeOpacity": 0.8, "description": "Primary roosting habitat for Myotis lucifugus"}}'::jsonb,
    ST_Transform(ST_GeomFromGeoJSON('{"type": "Polygon", "coordinates": [[[-125.0, 50.0], [-124.0, 50.0], [-124.0, 51.0], [-125.0, 51.0], [-125.0, 50.0]]]}'), 3005),
    geography(ST_GeomFromGeoJSON('{"type": "Polygon", "coordinates": [[[-125.0, 50.0], [-124.0, 50.0], [-124.0, 51.0], [-125.0, 51.0], [-125.0, 50.0]]]}'))
);

-- Add example linestring feature (migration route)
INSERT INTO unsecure_spatial_component (submission_observation_id, spatial_component, geometry, geography)
VALUES (
    (SELECT submission_observation_id FROM submission_observation order by submission_observation_id desc LIMIT 1),
    '{"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[-130.0, 60.0], [-125.0, 55.0], [-120.0, 50.0], [-115.0, 48.0]]}, "properties": {"id": "route-001", "name": "Migration Corridor", "routeType": "seasonal migration", "length": "850 km", "color": "#fd79a8", "weight": 4, "opacity": 0.9, "description": "Primary north-south migration corridor for bat populations"}}'::jsonb,
    ST_Transform(ST_GeomFromGeoJSON('{"type": "LineString", "coordinates": [[-130.0, 60.0], [-125.0, 55.0], [-120.0, 50.0], [-115.0, 48.0]]}'), 3005),
    geography(ST_GeomFromGeoJSON('{"type": "LineString", "coordinates": [[-130.0, 60.0], [-125.0, 55.0], [-120.0, 50.0], [-115.0, 48.0]]}'))
);

