version: '3.8'

# Production Docker Compose Configuration for FaunaLogic Database
# This extends the base docker-compose.yml with production-specific settings

services:
  postgres:
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGUSER: ${POSTGRES_USER}
    volumes:
      # Production data persistence
      - postgres_data_prod:/var/lib/postgresql/data
      # Production configuration files
      - ./config/postgresql.prod.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.prod.conf:/etc/postgresql/pg_hba.conf:ro
      # SSL certificates (uncomment if using SSL)
      # - ./certs/server.crt:/var/lib/postgresql/server.crt:ro
      # - ./certs/server.key:/var/lib/postgresql/server.key:ro
      # - ./certs/ca.crt:/var/lib/postgresql/ca.crt:ro
      # Backup directory
      - ./backups:/backups
      # Log directory
      - ./logs:/var/log/postgresql
    # Remove port mapping for production (use internal networking only)
    ports: []
    networks:
      - faunalogic-prod-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  flyway:
    environment:
      FLYWAY_URL: *******************************/${POSTGRES_DB}
      FLYWAY_USER: ${POSTGRES_USER}
      FLYWAY_PASSWORD: ${POSTGRES_PASSWORD}
      FLYWAY_SCHEMAS: faunalogic,faunalogic_dapi_v1
      FLYWAY_LOCATIONS: filesystem:/flyway/sql
      FLYWAY_BASELINE_ON_MIGRATE: false
      FLYWAY_VALIDATE_ON_MIGRATE: true
      FLYWAY_OUT_OF_ORDER: false
      FLYWAY_GROUP: true
      FLYWAY_MIXED: true
    networks:
      - faunalogic-prod-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Production backup service
  backup:
    image: postgres:15
    container_name: faunalogic-backup
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      PGHOST: postgres
      PGPORT: 5432
      PGDATABASE: ${POSTGRES_DB}
      PGUSER: ${POSTGRES_USER}
      PGPASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/usr/local/bin/backup.sh:ro
    networks:
      - faunalogic-prod-network
    # Run backup daily at 2 AM
    command: >
      sh -c "
        echo '0 2 * * * /usr/local/bin/backup.sh' | crontab -
        && crond -f
      "
    profiles:
      - backup

  # Log monitoring service
  log-monitor:
    image: postgres:15
    container_name: faunalogic-log-monitor
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      PGHOST: postgres
      PGPORT: 5432
      PGDATABASE: ${POSTGRES_DB}
      PGUSER: ${POSTGRES_USER}
      PGPASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./logs:/var/log/postgresql:ro
      - ./scripts/monitor-logs.sh:/usr/local/bin/monitor-logs.sh:ro
    networks:
      - faunalogic-prod-network
    command: /usr/local/bin/monitor-logs.sh
    profiles:
      - monitoring

volumes:
  postgres_data_prod:
    name: faunalogic_postgres_data_prod
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/faunalogic/postgres

networks:
  faunalogic-prod-network:
    name: faunalogic-prod-network
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24