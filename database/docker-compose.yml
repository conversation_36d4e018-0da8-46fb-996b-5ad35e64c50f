
services:
  postgres:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: faunalogic-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: faunalogic
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      PGUSER: postgres
    ports:
      - "5432:5432"
    volumes:
      # Persistent data storage
      - postgres_data:/var/lib/postgresql/data
      # Configuration
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      # Initialization scripts
      - ./scripts/init:/docker-entrypoint-initdb.d:ro
      # Backup directory
      - ./backups:/backups
    networks:
      - faunalogic-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d faunalogic"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  flyway:
    image: flyway/flyway:9.16.0
    container_name: faunalogic-flyway
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      FLYWAY_URL: ******************************************
      FLYWAY_USER: postgres
      FLYWAY_PASSWORD: postgres
      FLYWAY_SCHEMAS: faunalogic,faunalogic_dapi_v1
      FLYWAY_LOCATIONS: filesystem:/flyway/sql
      FLYWAY_BASELINE_ON_MIGRATE: true
      FLYWAY_BASELINE_VERSION: 0.0.0
      FLYWAY_GROUP: true
      FLYWAY_MIXED: true
      FLYWAY_OUT_OF_ORDER: false
      FLYWAY_VALIDATE_ON_MIGRATE: true
    volumes:
      - ./migrations:/flyway/sql:ro
      - ./dev-seed:/flyway/dev-seed:ro
      - ./config/flyway.conf:/flyway/conf/flyway.conf:ro
    networks:
      - faunalogic-network
    profiles:
      - migration

  # Development database for testing
  postgres-test:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: faunalogic-postgres-test
    restart: "no"
    environment:
      POSTGRES_DB: faunalogic_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      PGUSER: postgres
    ports:
      - "5433:5432"
    volumes:
      - ./scripts/init:/docker-entrypoint-initdb.d:ro
    networks:
      - faunalogic-network
    profiles:
      - test

volumes:
  postgres_data:
    name: faunalogic_postgres_data

networks:
  faunalogic-network:
    name: faunalogic-network
    driver: bridge