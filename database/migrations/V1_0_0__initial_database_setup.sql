-- FaunaLogic Database Initial Setup
-- Version: 1.0.0
-- Description: Initial database schema with spatial extensions and base configuration

-- Set client settings
SET client_min_messages = warning;

-- Create schemas
CREATE SCHEMA IF NOT EXISTS faunalogic;
CREATE SCHEMA IF NOT EXISTS faunalogic_dapi_v1;

-- Set search path
SET search_path = faunalogic, public;

-- Grant schema permissions to postgres
GRANT ALL ON SCHEMA faunalogic TO postgres;
GRANT ALL ON SCHEMA faunalogic_dapi_v1 TO postgres;