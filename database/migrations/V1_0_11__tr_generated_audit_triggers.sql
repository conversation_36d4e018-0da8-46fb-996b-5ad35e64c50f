 create trigger audit_application_user before insert or update or delete on faunalogic.application_user for each row execute procedure tr_audit_trigger();
 create trigger audit_secure_spatial_component before insert or update or delete on faunalogic.secure_spatial_component for each row execute procedure tr_audit_trigger();
 create trigger audit_application_user_security_exception before insert or update or delete on faunalogic.application_user_security_exception for each row execute procedure tr_audit_trigger();
 create trigger audit_artifact before insert or update or delete on faunalogic.artifact for each row execute procedure tr_audit_trigger();
 create trigger audit_security_reason before insert or update or delete on faunalogic.security_reason for each row execute procedure tr_audit_trigger();
 create trigger audit_spatial_transform before insert or update or delete on faunalogic.spatial_transform for each row execute procedure tr_audit_trigger();
 create trigger audit_spatial_transform_submission before insert or update or delete on faunalogic.spatial_transform_submission for each row execute procedure tr_audit_trigger();
 create trigger audit_submission_metadata before insert or update or delete on faunalogic.submission_metadata for each row execute procedure tr_audit_trigger();
 create trigger audit_submission before insert or update or delete on faunalogic.submission for each row execute procedure tr_audit_trigger();
 create trigger audit_security_transform before insert or update or delete on faunalogic.security_transform for each row execute procedure tr_audit_trigger();
 create trigger audit_security_transform_submission before insert or update or delete on faunalogic.security_transform_submission for each row execute procedure tr_audit_trigger();
 create trigger audit_submission_message before insert or update or delete on faunalogic.submission_message for each row execute procedure tr_audit_trigger();
 create trigger audit_submission_status before insert or update or delete on faunalogic.submission_status for each row execute procedure tr_audit_trigger();
 create trigger audit_system_metadata_constant before insert or update or delete on faunalogic.system_metadata_constant for each row execute procedure tr_audit_trigger();
 create trigger audit_system_constant before insert or update or delete on faunalogic.system_constant for each row execute procedure tr_audit_trigger();
 create trigger audit_submission_observation before insert or update or delete on faunalogic.submission_observation for each row execute procedure tr_audit_trigger();
 create trigger audit_unsecure_spatial_component before insert or update or delete on faunalogic.unsecure_spatial_component for each row execute procedure tr_audit_trigger();
 create trigger audit_user_identity_source before insert or update or delete on faunalogic.user_identity_source for each row execute procedure tr_audit_trigger();
 create trigger audit_tenant before insert or update or delete on faunalogic.tenant for each row execute procedure tr_audit_trigger();
 create trigger audit_source_transform before insert or update or delete on faunalogic.source_transform for each row execute procedure tr_audit_trigger();

