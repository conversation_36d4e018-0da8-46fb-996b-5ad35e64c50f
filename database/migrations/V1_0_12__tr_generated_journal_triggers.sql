 create trigger journal_application_user after insert or update or delete on faunalogic.application_user for each row execute procedure tr_journal_trigger();
 create trigger journal_secure_spatial_component after insert or update or delete on faunalogic.secure_spatial_component for each row execute procedure tr_journal_trigger();
 create trigger journal_application_user_security_exception after insert or update or delete on faunalogic.application_user_security_exception for each row execute procedure tr_journal_trigger();
 create trigger journal_artifact after insert or update or delete on faunalogic.artifact for each row execute procedure tr_journal_trigger();
 create trigger journal_security_reason after insert or update or delete on faunalogic.security_reason for each row execute procedure tr_journal_trigger();
 create trigger journal_spatial_transform after insert or update or delete on faunalogic.spatial_transform for each row execute procedure tr_journal_trigger();
 create trigger journal_spatial_transform_submission after insert or update or delete on faunalogic.spatial_transform_submission for each row execute procedure tr_journal_trigger();
 create trigger journal_submission_metadata after insert or update or delete on faunalogic.submission_metadata for each row execute procedure tr_journal_trigger();
 create trigger journal_submission after insert or update or delete on faunalogic.submission for each row execute procedure tr_journal_trigger();
 create trigger journal_security_transform after insert or update or delete on faunalogic.security_transform for each row execute procedure tr_journal_trigger();
 create trigger journal_security_transform_submission after insert or update or delete on faunalogic.security_transform_submission for each row execute procedure tr_journal_trigger();
 create trigger journal_submission_message after insert or update or delete on faunalogic.submission_message for each row execute procedure tr_journal_trigger();
 create trigger journal_submission_status after insert or update or delete on faunalogic.submission_status for each row execute procedure tr_journal_trigger();
 create trigger journal_system_metadata_constant after insert or update or delete on faunalogic.system_metadata_constant for each row execute procedure tr_journal_trigger();
 create trigger journal_system_constant after insert or update or delete on faunalogic.system_constant for each row execute procedure tr_journal_trigger();
 create trigger journal_submission_observation after insert or update or delete on faunalogic.submission_observation for each row execute procedure tr_journal_trigger();
 create trigger journal_unsecure_spatial_component after insert or update or delete on faunalogic.unsecure_spatial_component for each row execute procedure tr_journal_trigger();
 create trigger journal_user_identity_source after insert or update or delete on faunalogic.user_identity_source for each row execute procedure tr_journal_trigger();
 create trigger journal_tenant after insert or update or delete on faunalogic.tenant for each row execute procedure tr_journal_trigger();
 create trigger journal_source_transform after insert or update or delete on faunalogic.source_transform for each row execute procedure tr_journal_trigger();

