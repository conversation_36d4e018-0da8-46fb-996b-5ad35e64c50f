set search_path = faunalogic_dapi_v1;
set role faunalogic_api;

 create or replace view application_user as select * from faunalogic.application_user;
 create or replace view application_user_security_exception as select * from faunalogic.application_user_security_exception;
 create or replace view artifact as select * from faunalogic.artifact;
 create or replace view secure_spatial_component as select * from faunalogic.secure_spatial_component;
 create or replace view security_reason as select * from faunalogic.security_reason;
 create or replace view security_transform as select * from faunalogic.security_transform;
 create or replace view security_transform_submission as select * from faunalogic.security_transform_submission;
 create or replace view source_transform as select * from faunalogic.source_transform;
 create or replace view spatial_transform as select * from faunalogic.spatial_transform;
 create or replace view spatial_transform_submission as select * from faunalogic.spatial_transform_submission;
 create or replace view submission as select * from faunalogic.submission;
 create or replace view submission_message as select * from faunalogic.submission_message;
 create or replace view submission_metadata as select * from faunalogic.submission_metadata;
 create or replace view submission_observation as select * from faunalogic.submission_observation;
 create or replace view submission_status as select * from faunalogic.submission_status;
 create or replace view system_constant as select * from faunalogic.system_constant;
 create or replace view system_metadata_constant as select * from faunalogic.system_metadata_constant;
 create or replace view tenant as select * from faunalogic.tenant;
 create or replace view unsecure_spatial_component as select * from faunalogic.unsecure_spatial_component;
 create or replace view user_identity_source as select * from faunalogic.user_identity_source;

