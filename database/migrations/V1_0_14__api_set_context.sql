-- api_set_context.sql
drop function if exists api_set_context;

create or replace function api_set_context(p_user_uuid application_user.user_uuid%type) returns application_user.application_user_id%type
language plpgsql
security invoker
set client_min_messages = warning
as
$$
-- *******************************************************************
-- Procedure: api_set_context
-- Purpose: sets the initial context for api users
--
-- MODIFICATION HISTORY
-- Person           Date        Comments
-- ---------------- ----------- --------------------------------------
-- <EMAIL>
--                  2025-07-03  initial release
-- *******************************************************************
declare
  _application_user_id application_user.application_user_id%type;
begin
  select application_user_id into strict _application_user_id from application_user
    where user_uuid = p_user_uuid;

  create temp table if not exists faunalogic_context_temp (tag varchar(200), value varchar(200));
  delete from faunalogic_context_temp where tag = 'user_id';
  insert into faunalogic_context_temp (tag, value) values ('user_id', _application_user_id::varchar(200));

  return _application_user_id;
exception
  when others THEN
    raise;
end;
$$;
