-- api_set_context.sql
drop function if exists api_set_context;

create or replace function api_set_context(p_application_user_identifier application_user.user_identifier%type, p_user_identity_source_name user_identity_source.name%type) returns application_user.application_user_id%type
language plpgsql
security invoker
set client_min_messages = warning
as
$$
-- *******************************************************************
-- Procedure: api_set_context
-- Purpose: sets the initial context for api users
--
-- MODIFICATION HISTORY
-- Person           Date        Comments
-- ---------------- ----------- --------------------------------------
-- charlie.garret<PERSON><EMAIL>
--                  2021-01-03  initial release
--                  2021-04-16  adjusted to accepted defined user identity source
-- *******************************************************************
declare
  _application_user_id application_user.application_user_id%type;
  _user_identity_source_id user_identity_source.user_identity_source_id%type;
begin

  select user_identity_source_id into strict _user_identity_source_id from user_identity_source
    where name = p_user_identity_source_name
    and record_end_date is null;

  select application_user_id into strict _application_user_id from application_user
    where user_identity_source_id = _user_identity_source_id
    and user_identifier = p_application_user_identifier;

  create temp table if not exists faunalogic_context_temp (tag varchar(200), value varchar(200));
  delete from faunalogic_context_temp where tag = 'user_id';
  insert into faunalogic_context_temp (tag, value) values ('user_id', _application_user_id::varchar(200));

  return _application_user_id;
exception
  when others THEN
    raise;
end;
$$;
