-- FaunaLogic API User Setup
-- Version: 1.0.2
-- Description: Create API user and configure permissions

-- Create API user
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '${api_user}') THEN
        CREATE USER ${api_user} PASSWORD '${api_password}';
    END IF;
END
$$;

-- Set schema ownership
ALTER SCHEMA faunalogic_dapi_v1 OWNER TO ${api_user};

-- Grant permissions on schemas
GRANT ALL ON SCHEMA faunalogic_dapi_v1 TO ${api_user};
GRANT USAGE ON SCHEMA faunalogic TO ${api_user};

-- Set default privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA faunalogic_dapi_v1 GRANT ALL ON TABLES TO ${api_user};
ALTER DEFAULT PRIVILEGES IN SCHEMA faunalogic_dapi_v1 GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA faunalogic GRANT ALL ON TABLES TO ${api_user};

-- Configure search path for API user
ALTER ROLE ${api_user} SET search_path TO faunalogic_dapi_v1, faunalogic, public, topology;