--
-- ER/Studio Data Architect SQL Code Generation
-- Project :      FaunaLogic2.DM1
--
-- Date Created : Thursday, July 03, 2025 16:45:50
-- Target DBMS : PostgreSQL 10.x-12.x
--

-- 
-- TABLE: application_user 
--

CREATE TABLE application_user(
    application_user_id        integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    user_identity_source_id    integer           NOT NULL,
    user_identifier            varchar(200)      NOT NULL,
    user_uuid                  uuid              DEFAULT public.gen_random_uuid(),
    record_effective_date      date              DEFAULT now() NOT NULL,
    record_end_date            date,
    create_date                timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                varchar(63)       DEFAULT user NOT NULL,
    update_date                timestamptz(6),
    update_user                varchar(63),
    revision_count             integer           DEFAULT 0 NOT NULL,
    CONSTRAINT application_user_pk PRIMARY KEY (application_user_id)
)
;



-- 
-- TABLE: application_user_security_exception 
--

CREATE TABLE application_user_security_exception(
    application_user_security_exception_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    application_user_id                       integer           NOT NULL,
    security_reason_id                        integer           NOT NULL,
    start_date                                date,
    end_date                                  date,
    notes                                     varchar(3000),
    create_date                               timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                               varchar(63)       DEFAULT user NOT NULL,
    update_date                               timestamptz(6),
    update_user                               varchar(63),
    revision_count                            integer           DEFAULT 0 NOT NULL,
    CONSTRAINT system_user_security_exception_pk PRIMARY KEY (application_user_security_exception_id)
)
;



-- 
-- TABLE: artifact 
--

CREATE TABLE artifact(
    artifact_id                  integer           NOT NULL,
    submission_id                integer           NOT NULL,
    uuid                         uuid              DEFAULT public.gen_random_uuid() NOT NULL,
    file_name                    varchar(300)      NOT NULL,
    file_type                    varchar(300)      NOT NULL,
    title                        varchar(300),
    description                  varchar(3000),
    file_size                    integer,
    key                          varchar(1000),
    curation_review_timestamp    timestamptz(6),
    create_date                  timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                  varchar(63)       DEFAULT user NOT NULL,
    update_date                  timestamptz(6),
    update_user                  varchar(63),
    revision_count               integer           DEFAULT 0 NOT NULL,
    CONSTRAINT artifact_pk PRIMARY KEY (artifact_id)
)
;



-- 
-- TABLE: audit_log 
--

CREATE TABLE audit_log(
    audit_log_id           integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    application_user_id    integer           NOT NULL,
    create_date            timestamptz(6)    DEFAULT now() NOT NULL,
    table_name             varchar(200)      NOT NULL,
    operation              varchar(20)       NOT NULL,
    before_value           json,
    after_value            json,
    create_user            varchar(63)       DEFAULT user NOT NULL,
    update_date            timestamptz(6),
    update_user            varchar(63),
    revision_count         integer           DEFAULT 0 NOT NULL,
    CONSTRAINT audit_log_pk PRIMARY KEY (audit_log_id)
)
;



-- 
-- TABLE: secure_spatial_component 
--

CREATE TABLE secure_spatial_component(
    secure_spatial_component_id    integer                     GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    spatial_component              jsonb                       NOT NULL,
    geometry                       geometry(geometry, 3005),
    geography                      geography(geometry),
    create_date                    timestamptz(6)              DEFAULT now() NOT NULL,
    create_user                    varchar(63)                 DEFAULT user NOT NULL,
    update_date                    timestamptz(6),
    update_user                    varchar(63),
    revision_count                 integer                     DEFAULT 0 NOT NULL,
    CONSTRAINT secure_spatial_component_pk PRIMARY KEY (secure_spatial_component_id)
)
;



-- 
-- TABLE: security_reason 
--

CREATE TABLE security_reason(
    security_reason_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    tenant_id             integer           NOT NULL,
    name                  varchar(300)      NOT NULL,
    description           varchar(3000)     NOT NULL,
    start_date            date,
    end_date              date,
    create_date           timestamptz(6)    DEFAULT now() NOT NULL,
    create_user           varchar(63)       DEFAULT user NOT NULL,
    update_date           timestamptz(6),
    update_user           varchar(63),
    revision_count        integer           DEFAULT 0 NOT NULL,
    CONSTRAINT security_reason_pk PRIMARY KEY (security_reason_id)
)
;



-- 
-- TABLE: security_transform 
--

CREATE TABLE security_transform(
    security_transform_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    security_reason_id       integer           NOT NULL,
    name                     varchar(100)      NOT NULL,
    description              varchar(3000),
    notes                    varchar(3000),
    transform                text              NOT NULL,
    create_date              timestamptz(6)    DEFAULT now() NOT NULL,
    create_user              varchar(63)       DEFAULT user NOT NULL,
    update_date              timestamptz(6),
    update_user              varchar(63),
    revision_count           integer           DEFAULT 0 NOT NULL,
    CONSTRAINT security_transform_pk PRIMARY KEY (security_transform_id)
)
;



-- 
-- TABLE: security_transform_submission 
--

CREATE TABLE security_transform_submission(
    security_transform_submission_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    security_transform_id               integer           NOT NULL,
    secure_spatial_component_id         integer           NOT NULL,
    create_date                         timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                         varchar(63)       DEFAULT user NOT NULL,
    update_date                         timestamptz(6),
    update_user                         varchar(63),
    revision_count                      integer           DEFAULT 0 NOT NULL,
    CONSTRAINT security_transform_submission_pk PRIMARY KEY (security_transform_submission_id)
)
;



-- 
-- TABLE: source_transform 
--

CREATE TABLE source_transform(
    source_transform_id      integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    application_user_id      integer           NOT NULL,
    version                  varchar(20)       NOT NULL,
    metadata_transform       text              NOT NULL,
    metadata_index           varchar(100)      NOT NULL,
    record_effective_date    date              DEFAULT now() NOT NULL,
    record_end_date          date,
    create_date              timestamptz(6)    DEFAULT now() NOT NULL,
    create_user              varchar(63)       DEFAULT user NOT NULL,
    update_date              timestamptz(6),
    update_user              varchar(63),
    revision_count           integer           DEFAULT 0 NOT NULL,
    CONSTRAINT source_transform_pk PRIMARY KEY (source_transform_id)
)
;



-- 
-- TABLE: spatial_transform 
--

CREATE TABLE spatial_transform(
    spatial_transform_id     integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    tenant_id                integer           NOT NULL,
    name                     varchar(100)      NOT NULL,
    description              varchar(3000),
    notes                    varchar(3000),
    transform                text              NOT NULL,
    record_effective_date    date              NOT NULL,
    record_end_date          date,
    create_date              timestamptz(6)    DEFAULT now() NOT NULL,
    create_user              varchar(63)       DEFAULT user NOT NULL,
    update_date              timestamptz(6),
    update_user              varchar(63),
    revision_count           integer           DEFAULT 0 NOT NULL,
    CONSTRAINT spatial_transform_pk PRIMARY KEY (spatial_transform_id)
)
;



-- 
-- TABLE: spatial_transform_submission 
--

CREATE TABLE spatial_transform_submission(
    spatial_transform_submission_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    spatial_transform_id               integer           NOT NULL,
    unsecure_spatial_component_id      integer           NOT NULL,
    create_date                        timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                        varchar(63)       DEFAULT user NOT NULL,
    update_date                        timestamptz(6),
    update_user                        varchar(63),
    revision_count                     integer           DEFAULT 0 NOT NULL,
    CONSTRAINT spatial_transform_submission_pk PRIMARY KEY (spatial_transform_submission_id)
)
;



-- 
-- TABLE: submission 
--

CREATE TABLE submission(
    submission_id          integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    source_transform_id    integer           NOT NULL,
    uuid                   uuid              DEFAULT public.gen_random_uuid() NOT NULL,
    create_date            timestamptz(6)    DEFAULT now() NOT NULL,
    create_user            varchar(63)       DEFAULT user NOT NULL,
    update_date            timestamptz(6),
    update_user            varchar(63),
    revision_count         integer           DEFAULT 0 NOT NULL,
    CONSTRAINT submission_pk PRIMARY KEY (submission_id)
)
;



-- 
-- TABLE: submission_message 
--

CREATE TABLE submission_message(
    submission_message_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    submission_id            integer           NOT NULL,
    event_timestamp          timestamptz(6)    NOT NULL,
    message                  varchar(3000)     NOT NULL,
    create_date              timestamptz(6)    DEFAULT now() NOT NULL,
    create_user              varchar(63)       DEFAULT user NOT NULL,
    update_date              timestamptz(6),
    update_user              varchar(63),
    revision_count           integer           DEFAULT 0 NOT NULL,
    CONSTRAINT submission_message_pk PRIMARY KEY (submission_message_id)
)
;



-- 
-- TABLE: submission_metadata 
--

CREATE TABLE submission_metadata(
    submission_metadata_id        integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    submission_id                 integer           NOT NULL,
    eml_source                    text              NOT NULL,
    eml_json_source               jsonb,
    dataset_search_criteria       jsonb,
    record_effective_timestamp    timestamptz(6),
    record_end_timestamp          timestamptz(6),
    create_date                   timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                   varchar(63)       DEFAULT user NOT NULL,
    update_date                   timestamptz(6),
    update_user                   varchar(63),
    revision_count                integer           DEFAULT 0 NOT NULL,
    CONSTRAINT submission_metadata_pk PRIMARY KEY (submission_metadata_id)
)
;



-- 
-- TABLE: submission_observation 
--

CREATE TABLE submission_observation(
    submission_observation_id      integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    submission_id                  integer           NOT NULL,
    darwin_core_source             jsonb             NOT NULL,
    submission_security_request    jsonb,
    security_review_timestamp      timestamptz(6),
    foi_reason                     jsonb,
    security_reason_name           varchar(300),
    security_reason_description    varchar(3000),
    security_reason_end_date       date,
    record_effective_timestamp     timestamptz(6),
    record_end_timestamp           timestamptz(6),
    create_date                    timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                    varchar(63)       DEFAULT user NOT NULL,
    update_date                    timestamptz(6),
    update_user                    varchar(63),
    revision_count                 integer           DEFAULT 0 NOT NULL,
    CONSTRAINT submission_observation_pk PRIMARY KEY (submission_observation_id)
)
;



-- 
-- TABLE: submission_status 
--

CREATE TABLE submission_status(
    submission_status_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    submission_id           integer           NOT NULL,
    event_timestamp         timestamptz(6)    NOT NULL,
    status                  varchar(3000)     NOT NULL,
    create_date             timestamptz(6)    DEFAULT now() NOT NULL,
    create_user             varchar(63)       DEFAULT user NOT NULL,
    update_date             timestamptz(6),
    update_user             varchar(63),
    revision_count          integer           DEFAULT 0 NOT NULL,
    CONSTRAINT submission_status_pk PRIMARY KEY (submission_status_id)
)
;



-- 
-- TABLE: system_constant 
--

CREATE TABLE system_constant(
    system_constant_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    constant_name         varchar(50)       NOT NULL,
    character_value       varchar(300),
    numeric_value         numeric(10, 0),
    description           varchar(250),
    create_date           timestamptz(6)    DEFAULT now() NOT NULL,
    create_user           varchar(63)       DEFAULT user NOT NULL,
    update_date           timestamptz(6),
    update_user           varchar(63),
    revision_count        integer           DEFAULT 0 NOT NULL,
    CONSTRAINT system_constant_pk PRIMARY KEY (system_constant_id)
)
;



-- 
-- TABLE: system_metadata_constant 
--

CREATE TABLE system_metadata_constant(
    system_metadata_constant_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    tenant_id                      integer           NOT NULL,
    constant_name                  varchar(50)       NOT NULL,
    character_value                varchar(300),
    numeric_value                  numeric(10, 0),
    description                    varchar(250),
    create_date                    timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                    varchar(63)       DEFAULT user NOT NULL,
    update_date                    timestamptz(6),
    update_user                    varchar(63),
    revision_count                 integer           DEFAULT 0 NOT NULL,
    CONSTRAINT system_metadata_constant_pk PRIMARY KEY (system_metadata_constant_id)
)
;



-- 
-- TABLE: tenant 
--

CREATE TABLE tenant(
    tenant_id         integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    name              varchar(300)      NOT NULL,
    description       varchar(3000),
    create_date       timestamptz(6)    DEFAULT now() NOT NULL,
    create_user       varchar(63)       DEFAULT user NOT NULL,
    update_date       timestamptz(6),
    update_user       varchar(63),
    revision_count    integer           DEFAULT 0 NOT NULL,
    CONSTRAINT "PK34" PRIMARY KEY (tenant_id)
)
;



-- 
-- TABLE: unsecure_spatial_component 
--

CREATE TABLE unsecure_spatial_component(
    unsecure_spatial_component_id    integer                     GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    submission_observation_id        integer                     NOT NULL,
    secure_spatial_component_id      integer,
    spatial_component                jsonb                       NOT NULL,
    geometry                         geometry(geometry, 3005),
    geography                        geography(geometry),
    create_date                      timestamptz(6)              DEFAULT now() NOT NULL,
    create_user                      varchar(63)                 DEFAULT user NOT NULL,
    update_date                      timestamptz(6),
    update_user                      varchar(63),
    revision_count                   integer                     DEFAULT 0 NOT NULL,
    CONSTRAINT unsecure_spatial_component_pk PRIMARY KEY (unsecure_spatial_component_id)
)
;



-- 
-- TABLE: user_identity_source 
--

CREATE TABLE user_identity_source(
    user_identity_source_id    integer           GENERATED ALWAYS AS IDENTITY (START WITH 1 INCREMENT BY 1),
    tenant_id                  integer           NOT NULL,
    name                       varchar(50)       NOT NULL,
    record_effective_date      date              DEFAULT now() NOT NULL,
    record_end_date            date,
    description                varchar(250),
    notes                      varchar(3000),
    create_date                timestamptz(6)    DEFAULT now() NOT NULL,
    create_user                varchar(63)       DEFAULT user NOT NULL,
    update_date                timestamptz(6),
    update_user                varchar(63),
    revision_count             integer           DEFAULT 0 NOT NULL,
    CONSTRAINT user_identity_source_pk PRIMARY KEY (user_identity_source_id)
)
;



-- 
-- INDEX: system_user_nuk1 
--

CREATE UNIQUE INDEX system_user_nuk1 ON application_user(user_identifier, user_identity_source_id, record_end_date) NULLS NOT DISTINCT
;
-- 
-- INDEX: system_user_uuid_nuk1 
--

CREATE UNIQUE INDEX system_user_uuid_nuk1 ON application_user(user_uuid, record_end_date) NULLS NOT DISTINCT
;
-- 
-- INDEX: "Ref154" 
--

CREATE INDEX "Ref154" ON application_user(user_identity_source_id)
;
-- 
-- INDEX: system_user_security_exception_uk1 
--

CREATE UNIQUE INDEX system_user_security_exception_uk1 ON application_user_security_exception(security_reason_id, application_user_id)
;
-- 
-- INDEX: "Ref2623" 
--

CREATE INDEX "Ref2623" ON application_user_security_exception(security_reason_id)
;
-- 
-- INDEX: "Ref1643" 
--

CREATE INDEX "Ref1643" ON application_user_security_exception(application_user_id)
;
-- 
-- INDEX: "Ref3114" 
--

CREATE INDEX "Ref3114" ON artifact(submission_id)
;
-- 
-- INDEX: security_reason_uk1 
--

CREATE UNIQUE INDEX security_reason_uk1 ON security_reason(tenant_id, name)
;
-- 
-- INDEX: "Ref3441" 
--

CREATE INDEX "Ref3441" ON security_reason(tenant_id)
;
-- 
-- INDEX: security_transform_uk1 
--

CREATE UNIQUE INDEX security_transform_uk1 ON security_transform(name, security_reason_id)
;
-- 
-- INDEX: "Ref2622" 
--

CREATE INDEX "Ref2622" ON security_transform(security_reason_id)
;
-- 
-- INDEX: security_transform_submission_uk1 
--

CREATE UNIQUE INDEX security_transform_submission_uk1 ON security_transform_submission(secure_spatial_component_id, security_transform_id)
;
-- 
-- INDEX: "Ref212" 
--

CREATE INDEX "Ref212" ON security_transform_submission(security_transform_id)
;
-- 
-- INDEX: "Ref3234" 
--

CREATE INDEX "Ref3234" ON security_transform_submission(secure_spatial_component_id)
;
-- 
-- INDEX: source_transform_nuk1 
--

CREATE UNIQUE INDEX source_transform_nuk1 ON source_transform(application_user_id, version, record_end_date) NULLS NOT DISTINCT
;
-- 
-- INDEX: "Ref168" 
--

CREATE INDEX "Ref168" ON source_transform(application_user_id)
;
-- 
-- INDEX: spatial_transform_nuk1 
--

CREATE UNIQUE INDEX spatial_transform_nuk1 ON spatial_transform(tenant_id, name, record_end_date) NULLS NOT DISTINCT
;
-- 
-- INDEX: "Ref3442" 
--

CREATE INDEX "Ref3442" ON spatial_transform(tenant_id)
;
-- 
-- INDEX: spatial_transform_submission_uk1 
--

CREATE UNIQUE INDEX spatial_transform_submission_uk1 ON spatial_transform_submission(spatial_transform_id, unsecure_spatial_component_id)
;
-- 
-- INDEX: "Ref299" 
--

CREATE INDEX "Ref299" ON spatial_transform_submission(spatial_transform_id)
;
-- 
-- INDEX: "Ref310" 
--

CREATE INDEX "Ref310" ON spatial_transform_submission(unsecure_spatial_component_id)
;
-- 
-- INDEX: submission_uk1 
--

CREATE UNIQUE INDEX submission_uk1 ON submission(uuid)
;
-- 
-- INDEX: "Ref247" 
--

CREATE INDEX "Ref247" ON submission(source_transform_id)
;
-- 
-- INDEX: "Ref3132" 
--

CREATE INDEX "Ref3132" ON submission_message(submission_id)
;
-- 
-- INDEX: submission_metadata_nuk1 
--

CREATE UNIQUE INDEX submission_metadata_nuk1 ON submission_metadata(submission_id, record_end_timestamp)
;
-- 
-- INDEX: "Ref3126" 
--

CREATE INDEX "Ref3126" ON submission_metadata(submission_id)
;
-- 
-- INDEX: "Ref3124" 
--

CREATE INDEX "Ref3124" ON submission_observation(submission_id)
;
-- 
-- INDEX: "Ref3128" 
--

CREATE INDEX "Ref3128" ON submission_status(submission_id)
;
-- 
-- INDEX: system_constant_uk1 
--

CREATE UNIQUE INDEX system_constant_uk1 ON system_constant(constant_name)
;
-- 
-- INDEX: system_metadata_constant_uk1 
--

CREATE UNIQUE INDEX system_metadata_constant_uk1 ON system_metadata_constant(tenant_id, constant_name)
;
-- 
-- INDEX: "Ref3440" 
--

CREATE INDEX "Ref3440" ON system_metadata_constant(tenant_id)
;
-- 
-- INDEX: tenant_uk1 
--

CREATE UNIQUE INDEX tenant_uk1 ON tenant(name)
;
-- 
-- INDEX: "Ref2744" 
--

CREATE INDEX "Ref2744" ON unsecure_spatial_component(submission_observation_id)
;
-- 
-- INDEX: "Ref3246" 
--

CREATE INDEX "Ref3246" ON unsecure_spatial_component(secure_spatial_component_id)
;
-- 
-- INDEX: user_identity_source_nuk1 
--

CREATE UNIQUE INDEX user_identity_source_nuk1 ON user_identity_source(tenant_id, name, record_end_date) NULLS NOT DISTINCT
;
-- 
-- INDEX: "Ref3439" 
--

CREATE INDEX "Ref3439" ON user_identity_source(tenant_id)
;
-- 
-- TABLE: application_user 
--

ALTER TABLE application_user ADD CONSTRAINT "Refuser_identity_source4" 
    FOREIGN KEY (user_identity_source_id)
    REFERENCES user_identity_source(user_identity_source_id)
;


-- 
-- TABLE: application_user_security_exception 
--

ALTER TABLE application_user_security_exception ADD CONSTRAINT "Refsecurity_reason23" 
    FOREIGN KEY (security_reason_id)
    REFERENCES security_reason(security_reason_id)
;

ALTER TABLE application_user_security_exception ADD CONSTRAINT "Refapplication_user43" 
    FOREIGN KEY (application_user_id)
    REFERENCES application_user(application_user_id)
;


-- 
-- TABLE: artifact 
--

ALTER TABLE artifact ADD CONSTRAINT "Refsubmission14" 
    FOREIGN KEY (submission_id)
    REFERENCES submission(submission_id)
;


-- 
-- TABLE: security_reason 
--

ALTER TABLE security_reason ADD CONSTRAINT "Reftenant41" 
    FOREIGN KEY (tenant_id)
    REFERENCES tenant(tenant_id)
;


-- 
-- TABLE: security_transform 
--

ALTER TABLE security_transform ADD CONSTRAINT "Refsecurity_reason22" 
    FOREIGN KEY (security_reason_id)
    REFERENCES security_reason(security_reason_id)
;


-- 
-- TABLE: security_transform_submission 
--

ALTER TABLE security_transform_submission ADD CONSTRAINT "Refsecurity_transform12" 
    FOREIGN KEY (security_transform_id)
    REFERENCES security_transform(security_transform_id)
;

ALTER TABLE security_transform_submission ADD CONSTRAINT "Refsecure_spatial_component34" 
    FOREIGN KEY (secure_spatial_component_id)
    REFERENCES secure_spatial_component(secure_spatial_component_id)
;


-- 
-- TABLE: source_transform 
--

ALTER TABLE source_transform ADD CONSTRAINT "Refapplication_user8" 
    FOREIGN KEY (application_user_id)
    REFERENCES application_user(application_user_id)
;


-- 
-- TABLE: spatial_transform 
--

ALTER TABLE spatial_transform ADD CONSTRAINT "Reftenant42" 
    FOREIGN KEY (tenant_id)
    REFERENCES tenant(tenant_id)
;


-- 
-- TABLE: spatial_transform_submission 
--

ALTER TABLE spatial_transform_submission ADD CONSTRAINT "Refspatial_transform9" 
    FOREIGN KEY (spatial_transform_id)
    REFERENCES spatial_transform(spatial_transform_id)
;

ALTER TABLE spatial_transform_submission ADD CONSTRAINT "Refunsecure_spatial_component10" 
    FOREIGN KEY (unsecure_spatial_component_id)
    REFERENCES unsecure_spatial_component(unsecure_spatial_component_id)
;


-- 
-- TABLE: submission 
--

ALTER TABLE submission ADD CONSTRAINT "Refsource_transform7" 
    FOREIGN KEY (source_transform_id)
    REFERENCES source_transform(source_transform_id)
;


-- 
-- TABLE: submission_message 
--

ALTER TABLE submission_message ADD CONSTRAINT "Refsubmission32" 
    FOREIGN KEY (submission_id)
    REFERENCES submission(submission_id)
;


-- 
-- TABLE: submission_metadata 
--

ALTER TABLE submission_metadata ADD CONSTRAINT "Refsubmission26" 
    FOREIGN KEY (submission_id)
    REFERENCES submission(submission_id)
;


-- 
-- TABLE: submission_observation 
--

ALTER TABLE submission_observation ADD CONSTRAINT "Refsubmission24" 
    FOREIGN KEY (submission_id)
    REFERENCES submission(submission_id)
;


-- 
-- TABLE: submission_status 
--

ALTER TABLE submission_status ADD CONSTRAINT "Refsubmission28" 
    FOREIGN KEY (submission_id)
    REFERENCES submission(submission_id)
;


-- 
-- TABLE: system_metadata_constant 
--

ALTER TABLE system_metadata_constant ADD CONSTRAINT "Reftenant40" 
    FOREIGN KEY (tenant_id)
    REFERENCES tenant(tenant_id)
;


-- 
-- TABLE: unsecure_spatial_component 
--

ALTER TABLE unsecure_spatial_component ADD CONSTRAINT "Refsubmission_observation44" 
    FOREIGN KEY (submission_observation_id)
    REFERENCES submission_observation(submission_observation_id)
;

ALTER TABLE unsecure_spatial_component ADD CONSTRAINT "Refsecure_spatial_component46" 
    FOREIGN KEY (secure_spatial_component_id)
    REFERENCES secure_spatial_component(secure_spatial_component_id)
;


-- 
-- TABLE: user_identity_source 
--

ALTER TABLE user_identity_source ADD CONSTRAINT "Reftenant39" 
    FOREIGN KEY (tenant_id)
    REFERENCES tenant(tenant_id)
;


