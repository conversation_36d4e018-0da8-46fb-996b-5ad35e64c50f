-- populate_system_constant.sql

-- common constants
insert into system_constant (constant_name, character_value, description, create_date, create_user) values ('DATA_NOT_PROVIDED_MESSAGE', 'Not provided', 'A message to insert as appropriate where some data standard defines the data as required but that data is not available.', now(), 1);
-- ISO 8601 date format strings
insert into system_constant (constant_name, character_value, description, create_date, create_user) values ('ISO_8601_DATE_FORMAT_WITH_TIMEZONE', 'YYYY-MM-DD"T"HH24:MI:SS"Z"', 'The ISO 8601 dae format string for timezone.', now(), 1);
insert into system_constant (constant_name, character_value, description, create_date, create_user) values ('ISO_8601_DATE_FORMAT_WITHOUT_TIME_TIMEZONE', 'YYYY-MM-DD', 'The ISO 8601 dae format string without time or timezone.', now(), 1);
-- defines system owner
insert into system_constant (constant_name, character_value, description, create_date, create_user) values ('SYSTEM_OWNER', 'Bayseum Ltd.', 'Defines the system owner.', now(), 1);
