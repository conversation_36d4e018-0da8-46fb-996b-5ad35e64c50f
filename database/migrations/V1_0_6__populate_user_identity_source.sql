-- populate_user_identity_source.sql
-- this script is run before triggers are installed and defines the audit user for initial install of the system
delete from application_user;
delete from user_identity_source;
delete from tenant;

-- initial install of the system user who would administer tenants of the app
insert into tenant (name, description, create_date, create_user) values ('Bayseum Ltd.', 'System owner and administrator of all tenants.', now(), 1);

insert into user_identity_source(tenant_id, name, record_effective_date, description, create_date, create_user) values ((select tenant_id from tenant where name = 'Bayseum Ltd.'), 'DATABASE', now(), 'A machine user for internal use only.', now(), 1);

insert into "application_user" (user_identity_source_id, user_identifier, user_uuid, record_effective_date, create_date, create_user)
  values ((select user_identity_source_id from user_identity_source where name = 'DATABASE' and tenant_id = (select tenant_id from tenant where name = 'Bayseum Ltd.') and record_end_date is null), (select session_user), (select session_user), now(), now(), 1);
