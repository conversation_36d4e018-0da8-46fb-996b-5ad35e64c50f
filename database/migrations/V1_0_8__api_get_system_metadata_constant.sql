-- api_get_system_metadata_constant.sql
drop function if exists api_get_character_system_metadata_constant;

create or replace function api_get_character_system_metadata_constant(_constant_name system_metadata_constant.constant_name%type) returns system_metadata_constant.character_value%type
language plpgsql
security definer
stable
as
$$
-- *******************************************************************
-- Procedure: api_get_character_system_metadata_constant
-- Purpose: returns a text value from the system constants table
--
-- MODIFICATION HISTORY
-- Person           Date        Comments
-- ---------------- ----------- --------------------------------------
-- charlie.garret<PERSON><PERSON><EMAIL>
--                  2021-06-29  initial release
-- *******************************************************************
declare
  _tenant_id tenant.tenant_id%type;
begin
  select tenant_id into _tenant_id from user_identity_source 
    where user_identity_source_id = (
      select user_identity_source_id from application_user where application_user_id = (
        select api_get_context_user_id
      )
      );

  return (select character_value from system_metadata_constant where constant_name = _constant_name and tenant_id = _tenant_id);

exception
  when others then
    raise;
end;
$$;

grant execute on function api_get_character_system_metadata_constant to faunalogic_api;

drop function if exists api_get_numeric_system_metadata_constant;

create or replace function api_get_numeric_system_metadata_constant(_constant_name system_metadata_constant.constant_name%type) returns system_metadata_constant.numeric_value%type
language plpgsql
security definer
stable
as
$$
-- *******************************************************************
-- Procedure: api_get_numeric_system_metadata_constant
-- Purpose: returns a numeric value from the system constants table
--
-- MODIFICATION HISTORY
-- Person           Date        Comments
-- ---------------- ----------- --------------------------------------
-- <EMAIL>
--                  2021-06-29  initial release
-- *******************************************************************
declare
  _tenant_id tenant.tenant_id%type;
begin
  select tenant_id into _tenant_id from user_identity_source 
    where user_identity_source_id = (
      select user_identity_source_id from application_user where application_user_id = (
        select api_get_context_user_id
      )
      );

  return (select numeric_value from system_metadata_constant where constant_name = _constant_name and tenant_id = _tenant_id);

exception
  when others then
    raise;
end;
$$;

grant execute on function api_get_numeric_system_metadata_constant to faunalogic_api;
