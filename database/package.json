{"name": "faunalogic-db", "version": "0.0.0", "description": "Database migrations/seeding for FaunaLogic", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/bcgov/faunalogic-platform.git"}, "engines": {"node": ">= 20.0.0", "npm": ">= 10.0.0"}, "scripts": {"setup": "npm-run-all -l -s migrate-latest seed", "migrate-latest": "knex migrate:latest --knexfile ./src/knexfile.ts", "migrate-rollback": "knex migrate:rollback --knexfile ./src/knexfile.ts", "seed": "knex seed:run --knexfile ./src/knexfile.ts", "lint": "eslint src/ --ext .js,.ts", "lint-fix": "npm run lint -- --fix", "format": "prettier --loglevel=warn --check \"./src/**/*.{js,jsx,ts,tsx,css,scss}\"", "format-fix": "prettier --loglevel=warn --write \"./src/**/*.{js,jsx,ts,tsx,json,css,scss}\"", "fix": "npm-run-all -l -s lint-fix format-fix"}, "dependencies": {"@swc/core": "^1.11.7", "knex": "^2.4.2", "pg": "^8.7.1", "typescript": "^4.2.4"}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@types/node": "^20.17.16", "@types/pg": "^8.11.4", "@typescript-eslint/eslint-plugin": "~7.6.0", "@typescript-eslint/parser": "~7.6.0", "eslint": "~8.56.0", "eslint-config-prettier": "~8.10.0", "eslint-plugin-prettier": "~4.2.1", "geojson-random": "^0.5.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "ts-node": "^10.9.2"}}