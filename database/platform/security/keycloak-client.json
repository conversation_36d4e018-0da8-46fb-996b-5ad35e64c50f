{"clientId": "faunalogic-platform", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The FaunaLogic Platform Client", "rootUrl": "https://*.apps.silver.devops.gov.bc.ca", "adminUrl": "https://*.apps.silver.devops.gov.bc.ca", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.multivalued.roles": "false", "saml.force.post.binding": "false", "saml.encrypt": "false", "login_theme": "bcgov-with-local-login", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "access": {"view": true, "configure": true, "manage": true}}