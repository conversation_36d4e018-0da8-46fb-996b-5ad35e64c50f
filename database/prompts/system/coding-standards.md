# Database Coding Standards

**Version**: 1.0.1  
**Last Updated**: 2025-06-27  
**Status**: ✅ Production Ready  
**Service**: Database Layer

## 🔐 MANDATORY: API Access Standards

### Critical Connection Requirements
**ALL application code MUST use the dedicated API user:**

```sql
-- Connection Parameters (REQUIRED)
Host: localhost
Port: 5432
Database: faunalogic
Username: faunalogic_api
Password: flatpass
Schema: faunalogic_dapi_v1

-- First statement in every session (MANDATORY)
SET search_path = faunalogic_dapi_v1, public;
```

### API-Only Database Access Pattern
```sql
-- ✅ CORRECT: Use API functions through faunalogic_dapi_v1
SELECT * FROM faunalogic_dapi_v1.api_get_user_profile('user-uuid');

-- ❌ WRONG: Direct table access (not allowed for API user)
SELECT * FROM faunalogic.application_user WHERE id = 'user-uuid';

-- ✅ CORRECT: Use API schema views and functions
SELECT * FROM current_user_profile;  -- API schema view
```

## SQL Code Style Standards

### General Principles
- **Readability**: SQL should be self-documenting and easy to understand
- **Consistency**: Follow established patterns throughout the schema
- **Performance**: Write efficient queries with proper indexing
- **Security**: Always use API user and schema for application access
- **API-First**: All application access through faunalogic_dapi_v1 schema

### SQL Formatting Standards

#### Table and Column Naming
```sql
-- Tables: lowercase with underscores
CREATE TABLE application_user (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email_address VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Use descriptive names that explain purpose
CREATE TABLE submission_observation (  -- ✅ Clear purpose
    id UUID PRIMARY KEY,
    submission_id UUID NOT NULL REFERENCES submission(id),
    observation_data JSONB NOT NULL,
    spatial_location GEOMETRY(POINT, 4326)
);

-- Avoid abbreviations unless commonly understood
CREATE TABLE usr (id UUID);  -- ❌ Unclear abbreviation
CREATE TABLE application_user (id UUID);  -- ✅ Clear and descriptive
```

#### Index Naming Conventions
```sql
-- Primary key indexes: pk_{table_name}
ALTER TABLE application_user ADD CONSTRAINT pk_application_user PRIMARY KEY (id);

-- Foreign key indexes: fk_{table_name}_{referenced_table}
CREATE INDEX idx_submission_observation_submission_id 
    ON submission_observation(submission_id);

-- Spatial indexes: idx_{table_name}_{column_name}_spatial
CREATE INDEX idx_spatial_component_geom_spatial 
    ON spatial_component USING GIST (geom);

-- Unique indexes: uk_{table_name}_{column_name}
CREATE UNIQUE INDEX uk_application_user_email 
    ON application_user(email_address);

-- Composite indexes: idx_{table_name}_{col1}_{col2}
CREATE INDEX idx_submission_tenant_created 
    ON submission(tenant_id, created_at);
```

#### Function Naming Conventions
```sql
-- API functions: api_{action}_{entity}
CREATE OR REPLACE FUNCTION api_get_user_profile(p_user_id UUID)
RETURNS TABLE(...) AS $$

-- Trigger functions: tr_{purpose}_{trigger_type}
CREATE OR REPLACE FUNCTION tr_audit_trigger()
RETURNS TRIGGER AS $$

-- Utility functions: util_{purpose}
CREATE OR REPLACE FUNCTION util_validate_spatial_data(p_geometry GEOMETRY)
RETURNS BOOLEAN AS $$

-- Security functions: sec_{purpose}
CREATE OR REPLACE FUNCTION sec_apply_spatial_transform(p_geometry GEOMETRY)
RETURNS GEOMETRY AS $$
```

### SQL Query Structure

#### SELECT Statement Formatting
```sql
-- Format for readability with proper indentation
SELECT 
    u.id,
    u.first_name,
    u.last_name,
    u.email_address,
    t.name AS tenant_name,
    COUNT(s.id) AS submission_count
FROM application_user u
    INNER JOIN tenant t ON u.tenant_id = t.id
    LEFT JOIN submission s ON u.id = s.created_by
WHERE u.created_at >= '2024-01-01'
    AND u.active = true
    AND t.active = true
GROUP BY 
    u.id, 
    u.first_name, 
    u.last_name, 
    u.email_address, 
    t.name
ORDER BY u.last_name, u.first_name
LIMIT 100;
```

#### INSERT Statement Formatting
```sql
-- Explicit column listing for maintainability
INSERT INTO submission (
    id,
    tenant_id,
    created_by,
    submission_type,
    data_package,
    created_at
) VALUES (
    gen_random_uuid(),
    p_tenant_id,
    p_user_id,
    p_submission_type,
    p_data_package,
    NOW()
);

-- Multi-row inserts with proper formatting
INSERT INTO system_constant (name, value, description) VALUES
    ('max_submission_size', '10485760', 'Maximum submission size in bytes'),
    ('default_spatial_srid', '4326', 'Default spatial reference system'),
    ('audit_retention_days', '2555', 'Days to retain audit logs');
```

#### UPDATE Statement Standards
```sql
-- Always include WHERE clause and use explicit conditions
UPDATE application_user 
SET 
    last_login_at = NOW(),
    login_count = login_count + 1,
    updated_at = NOW()
WHERE id = p_user_id
    AND active = true;

-- Use FROM clause for complex updates
UPDATE submission s
SET security_applied = true
FROM security_transform st
WHERE s.id = st.submission_id
    AND st.transform_type = 'spatial_obfuscation';
```

## Spatial Data Standards

### Spatial Column Definitions
```sql
-- Always specify geometry type and SRID
ALTER TABLE spatial_component 
ADD COLUMN geom GEOMETRY(POINT, 4326) NOT NULL;

-- Use appropriate geometry types
ALTER TABLE boundary_data 
ADD COLUMN geom GEOMETRY(POLYGON, 4326) NOT NULL;

ALTER TABLE track_data 
ADD COLUMN geom GEOMETRY(LINESTRING, 4326) NOT NULL;

-- For mixed geometry types, use generic GEOMETRY
ALTER TABLE mixed_spatial_data 
ADD COLUMN geom GEOMETRY(GEOMETRY, 4326) NOT NULL;
```

### Spatial Index Creation
```sql
-- Always create spatial indexes for geometry columns
CREATE INDEX idx_spatial_component_geom 
    ON spatial_component USING GIST (geom);

-- Include additional columns in spatial indexes when needed
CREATE INDEX idx_submission_observation_geom_date 
    ON submission_observation USING GIST (spatial_location, created_at);

-- Use appropriate operator classes for specific use cases
CREATE INDEX idx_spatial_component_geom_nd 
    ON spatial_component USING GIST (geom gist_geometry_ops_nd);
```

### Spatial Query Patterns
```sql
-- Use ST_DWithin for distance queries (automatically uses spatial index)
SELECT * FROM spatial_component 
WHERE ST_DWithin(
    geom, 
    ST_GeogFromText('POINT(-122.4194 37.7749)'), 
    1000  -- 1000 meters
);

-- Use ST_Intersects for intersection queries
SELECT * FROM spatial_component sc
WHERE ST_Intersects(
    sc.geom,
    ST_GeomFromText('POLYGON((-122.5 37.7, -122.4 37.7, -122.4 37.8, -122.5 37.8, -122.5 37.7))', 4326)
);

-- Transform coordinates when necessary
SELECT 
    id,
    ST_Transform(geom, 3857) AS web_mercator_geom,
    ST_Area(ST_Transform(geom, 3857)) AS area_square_meters
FROM boundary_data;
```

## Function Development Standards

### Function Structure Template
```sql
CREATE OR REPLACE FUNCTION api_function_name(
    p_parameter1 UUID,
    p_parameter2 VARCHAR(255) DEFAULT NULL
)
RETURNS TABLE(
    id UUID,
    name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    v_user_id UUID;
    v_tenant_id UUID;
BEGIN
    -- Get context information
    v_user_id := api_get_context_user_id();
    v_tenant_id := api_get_context_tenant_id();
    
    -- Validate parameters
    IF p_parameter1 IS NULL THEN
        RAISE EXCEPTION 'Parameter p_parameter1 cannot be null';
    END IF;
    
    -- Check permissions
    IF NOT api_check_user_permission('required_permission') THEN
        RAISE EXCEPTION 'Insufficient permissions to perform this operation';
    END IF;
    
    -- Main function logic
    RETURN QUERY
    SELECT 
        t.id,
        t.name,
        t.created_at
    FROM some_table t
    WHERE t.tenant_id = v_tenant_id
        AND (p_parameter2 IS NULL OR t.name ILIKE '%' || p_parameter2 || '%')
    ORDER BY t.created_at DESC;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and re-raise with context
        RAISE EXCEPTION 'Error in api_function_name: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Error Handling Standards
```sql
-- Use specific exception handling
BEGIN
    -- Risky operation
    INSERT INTO sensitive_table (...) VALUES (...);
EXCEPTION
    WHEN unique_violation THEN
        RAISE EXCEPTION 'Record already exists with this identifier';
    WHEN foreign_key_violation THEN
        RAISE EXCEPTION 'Referenced record does not exist';
    WHEN check_violation THEN
        RAISE EXCEPTION 'Data validation failed: %', SQLERRM;
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Unexpected error: %', SQLERRM;
END;
```

### Parameter Validation
```sql
-- Always validate input parameters
IF p_user_id IS NULL THEN
    RAISE EXCEPTION 'User ID cannot be null';
END IF;

IF p_geometry IS NOT NULL AND NOT ST_IsValid(p_geometry) THEN
    RAISE EXCEPTION 'Invalid geometry provided';
END IF;

IF p_email IS NOT NULL AND p_email !~ '^[^@]+@[^@]+\.[^@]+$' THEN
    RAISE EXCEPTION 'Invalid email format';
END IF;
```

## Security Implementation Standards

### Row Level Security Patterns
```sql
-- Enable RLS on sensitive tables
ALTER TABLE secure_spatial_component ENABLE ROW LEVEL SECURITY;

-- Create policies with descriptive names
CREATE POLICY tenant_isolation_policy ON secure_spatial_component
    FOR ALL TO application_user
    USING (tenant_id = api_get_context_tenant_id());

CREATE POLICY read_own_data_policy ON submission
    FOR SELECT TO application_user
    USING (created_by = api_get_context_user_id() 
           OR api_check_user_permission('read_all_submissions'));
```

### Security Transform Implementation
```sql
CREATE OR REPLACE FUNCTION sec_apply_spatial_obfuscation(
    p_geometry GEOMETRY,
    p_obfuscation_level INTEGER DEFAULT 1
)
RETURNS GEOMETRY AS $$
BEGIN
    CASE p_obfuscation_level
        WHEN 1 THEN
            -- Light obfuscation: 100m buffer
            RETURN ST_Buffer(p_geometry, 100);
        WHEN 2 THEN
            -- Medium obfuscation: 500m buffer
            RETURN ST_Buffer(p_geometry, 500);
        WHEN 3 THEN
            -- Heavy obfuscation: centroid only
            RETURN ST_Centroid(p_geometry);
        ELSE
            -- Default: return original
            RETURN p_geometry;
    END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
```

## Trigger Development Standards

### Audit Trigger Template
```sql
CREATE OR REPLACE FUNCTION tr_audit_trigger()
RETURNS TRIGGER AS $$
DECLARE
    v_user_id UUID;
    v_old_data JSONB;
    v_new_data JSONB;
BEGIN
    -- Get current user context
    v_user_id := api_get_context_user_id();
    
    -- Prepare audit data
    IF TG_OP = 'DELETE' THEN
        v_old_data := to_jsonb(OLD);
        v_new_data := NULL;
    ELSIF TG_OP = 'UPDATE' THEN
        v_old_data := to_jsonb(OLD);
        v_new_data := to_jsonb(NEW);
    ELSIF TG_OP = 'INSERT' THEN
        v_old_data := NULL;
        v_new_data := to_jsonb(NEW);
    END IF;
    
    -- Insert audit record
    INSERT INTO audit_log (
        table_name,
        operation,
        user_id,
        old_data,
        new_data,
        created_at
    ) VALUES (
        TG_TABLE_NAME,
        TG_OP,
        v_user_id,
        v_old_data,
        v_new_data,
        NOW()
    );
    
    -- Return appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;
```

### Trigger Application Pattern
```sql
-- Apply audit trigger to tables
CREATE TRIGGER audit_trigger_row 
    AFTER INSERT OR UPDATE OR DELETE ON application_user
    FOR EACH ROW 
    EXECUTE FUNCTION tr_audit_trigger();

-- Apply timestamp triggers
CREATE TRIGGER update_timestamp_trigger
    BEFORE UPDATE ON application_user
    FOR EACH ROW
    EXECUTE FUNCTION tr_update_timestamp();
```

## Testing Standards

### Unit Test Structure
```sql
-- Test function with valid inputs
DO $$
DECLARE
    v_result RECORD;
    v_expected_count INTEGER := 5;
    v_actual_count INTEGER;
BEGIN
    -- Setup test data
    INSERT INTO test_table (name) VALUES ('test1'), ('test2'), ('test3'), ('test4'), ('test5');
    
    -- Execute function under test
    SELECT COUNT(*) INTO v_actual_count FROM api_get_test_records();
    
    -- Assert results
    IF v_actual_count != v_expected_count THEN
        RAISE EXCEPTION 'Expected % records, got %', v_expected_count, v_actual_count;
    END IF;
    
    -- Cleanup
    DELETE FROM test_table WHERE name LIKE 'test%';
    
    RAISE NOTICE 'Test passed: api_get_test_records returns correct count';
END $$;
```

### Spatial Data Validation Tests
```sql
-- Test spatial data integrity
DO $$
DECLARE
    v_invalid_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO v_invalid_count 
    FROM spatial_component 
    WHERE NOT ST_IsValid(geom);
    
    IF v_invalid_count > 0 THEN
        RAISE EXCEPTION 'Found % invalid geometries in spatial_component', v_invalid_count;
    END IF;
    
    RAISE NOTICE 'All spatial data is valid';
END $$;
```

## Performance Optimization Standards

### Query Optimization Checklist
- [ ] Use appropriate indexes (B-tree, GIST, GIN)
- [ ] Avoid SELECT * in production functions
- [ ] Use EXPLAIN ANALYZE to verify query plans
- [ ] Limit result sets with appropriate WHERE clauses
- [ ] Use EXISTS instead of IN for subqueries when appropriate
- [ ] Consider partitioning for large tables

### Index Creation Guidelines
```sql
-- Create indexes for frequently queried columns
CREATE INDEX idx_submission_created_by ON submission(created_by);
CREATE INDEX idx_submission_tenant_created ON submission(tenant_id, created_at);

-- Use partial indexes for filtered queries
CREATE INDEX idx_active_users ON application_user(email_address) WHERE active = true;

-- Use expression indexes for computed values
CREATE INDEX idx_submission_year ON submission(EXTRACT(YEAR FROM created_at));
```

---

**Important**: These coding standards ensure consistency, security, and performance across the FaunaLogic database layer. All database development should follow these patterns.