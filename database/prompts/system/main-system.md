# Database Service System Prompt

**Version**: 1.0.1  
**Last Updated**: 2025-06-27  
**Status**: ✅ Production Ready  
**Service**: Database Layer

## Service Overview

### Database Service Description
The FaunaLogic database service provides the foundational data layer for a spatial wildlife data management system. Built on PostgreSQL with PostGIS extensions, it handles spatial data storage, security transformations, multi-tenant architecture, and comprehensive audit logging.

### Service Architecture
- **Database Engine**: PostgreSQL 15 with PostGIS 3.3 spatial extensions
- **Migration System**: Flyway Community Edition with 8 versioned migrations (V1.0.0-V1.0.8)
- **Containerization**: Docker and Docker Compose (V1/V2 compatible)
- **Spatial Capabilities**: Full PostGIS suite including raster, topology, SFCGAL, and routing
- **Security**: Row-level security, data transformation, and access control
- **Multi-tenancy**: Tenant-based data isolation
- **Auditing**: Comprehensive audit logging and journaling system
- **API Access**: Dedicated `faunalogic_api` user with access to `faunalogic_dapi_v1` schema

## 🔐 CRITICAL: API Access Requirements

**⚠️ ALL APPLICATION CONNECTIONS MUST USE THE DEDICATED API USER:**

```sql
-- API Connection Details
Host: localhost (or database host)
Port: 5432
Database: faunalogic
Username: faunalogic_api
Password: flatpass
Schema: faunalogic_dapi_v1
```

**In application code, ALWAYS set the search path:**
```sql
SET search_path = faunalogic_dapi_v1, public;
```

**Why this is mandatory:**
- ✅ **Security**: API user has restricted permissions
- ✅ **Schema isolation**: Uses dedicated API schema
- ✅ **Audit compliance**: All operations are properly logged
- ✅ **Multi-tenant support**: Proper tenant context handling

**🚫 NEVER use the `postgres` superuser for application connections!**

## Database Schema Overview

### Core Tables and Their Purpose
```sql
-- User Management
application_user              -- User accounts and profiles
user_identity_source         -- Authentication source mapping

-- Multi-tenancy
tenant                       -- Tenant isolation and configuration

-- Spatial Data Management
spatial_component            -- Base spatial data storage
secure_spatial_component     -- Security-enhanced spatial data
unsecure_spatial_component   -- Public spatial data

-- Data Submission System
submission                   -- Data submission tracking
submission_observation       -- Individual observations
submission_metadata          -- Submission metadata and context

-- Security Framework
security_reason              -- Security classification reasons
security_transform           -- Data transformation rules
security_transform_submission -- Applied security transformations

-- System Management
system_constant              -- System-wide configuration
system_metadata_constant     -- System metadata configuration
audit_log                    -- Comprehensive audit trail
```

## Development Constraints

### Database-Specific Requirements
- [ ] All schema changes must include migration scripts
- [ ] Spatial indexes must be created for spatial columns
- [ ] All tables must have audit triggers enabled
- [ ] Row-level security policies must be defined for sensitive data
- [ ] Multi-tenant data must be properly isolated
- [ ] All functions must include proper error handling
- [ ] Database functions must follow naming convention: `api_*` for API functions

### Security Requirements
- [ ] All spatial data must be evaluated for security classification
- [ ] User access must respect tenant boundaries
- [ ] Audit trails must be maintained for all data modifications
- [ ] Sensitive spatial data must use security transformations
- [ ] All database functions must validate user permissions

### Performance Requirements
- [ ] Spatial queries must use appropriate indexes (GIST, SP-GIST)
- [ ] Large datasets must be properly partitioned
- [ ] Query performance must be monitored and optimized
- [ ] Connection pooling must be configured for production
- [ ] Database statistics must be regularly updated

## Context Files

### Always Include These Files
When working on database tasks, include these files for context:

**Current Migration Structure (PRODUCTION READY):**
- `database/migrations/V1_0_0__Initial_Database_Setup.sql` - Database and schema creation
- `database/migrations/V1_0_1__Create_Spatial_Extensions.sql` - PostGIS extensions
- `database/migrations/V1_0_2__Create_API_User.sql` - faunalogic_api user setup
- `database/migrations/V1_0_3__Create_Core_Tables.sql` - Core business tables
- `database/migrations/V1_0_4__Create_Spatial_Tables.sql` - Spatial data tables
- `database/migrations/V1_0_5__Create_Security_Tables.sql` - Security framework
- `database/migrations/V1_0_6__Create_API_Functions.sql` - API functions for faunalogic_dapi_v1
- `database/migrations/V1_0_7__Create_Audit_Triggers.sql` - Audit system
- `database/migrations/V1_0_8__Insert_Initial_Data.sql` - System and reference data

**Legacy Files (Reference Only):**
- `database/src/migrations/release.0.1.0/` - Original migration files (converted to Flyway)

### Migration Files Structure
```
database/src/migrations/release.0.1.0/
├── faunalogic.sql                    # Main schema with all tables
├── db_setup_up.sql                   # Database initialization
├── create_spatial_extensions.psql    # PostGIS and spatial extensions
├── api_get_context_user_id.sql      # User context API function
├── api_get_system_constant.sql      # System constants API
├── api_get_system_metadata_constant.sql # System metadata API
├── api_set_context.sql              # Context setting API
├── create_sequences.sql             # Database sequences
├── populate_system_constant.sql     # System data population
├── populate_user_identity_source.sql # Identity source data
├── tr_audit_trigger.sql             # Audit trigger template
├── tr_generated_audit_triggers.sql  # Generated audit triggers
├── tr_generated_journal_triggers.sql # Generated journal triggers
├── tr_journal_trigger.sql           # Journal trigger template
├── vw_generated_dapi_views.sql      # Generated data API views
└── smoketest_release.sql            # Release validation tests
```

## Development Guidelines

### Schema Development Process
1. **Design Review**: All schema changes must be reviewed for spatial and security implications
2. **Migration Script**: Create migration script in appropriate release directory
3. **Backward Compatibility**: Ensure migrations don't break existing data or applications
4. **Testing**: Include validation queries in migration scripts
5. **Documentation**: Update schema documentation and API specifications

### Spatial Data Best Practices
```sql
-- Always use appropriate spatial reference system
ALTER TABLE spatial_table ADD COLUMN geom GEOMETRY(POINT, 4326);

-- Create spatial index for performance
CREATE INDEX idx_spatial_table_geom ON spatial_table USING GIST (geom);

-- Use spatial functions appropriately
SELECT * FROM spatial_table 
WHERE ST_DWithin(geom, ST_GeogFromText('POINT(-122.4194 37.7749)'), 1000);

-- Transform coordinates when necessary
SELECT ST_Transform(geom, 3857) FROM spatial_table;
```

### Security Implementation Patterns
```sql
-- Row Level Security example
ALTER TABLE secure_spatial_component ENABLE ROW LEVEL SECURITY;

CREATE POLICY tenant_isolation ON secure_spatial_component
    FOR ALL TO application_user
    USING (tenant_id = api_get_context_tenant_id());

-- Security transformation application
CREATE OR REPLACE FUNCTION apply_security_transform(
    p_geometry GEOMETRY,
    p_security_reason_id INTEGER
) RETURNS GEOMETRY AS $$
BEGIN
    -- Apply appropriate transformation based on security reason
    RETURN CASE 
        WHEN p_security_reason_id = 1 THEN ST_Buffer(p_geometry, 1000)
        WHEN p_security_reason_id = 2 THEN ST_Centroid(p_geometry)
        ELSE p_geometry
    END;
END;
$$ LANGUAGE plpgsql;
```

### Audit and Journal Patterns
```sql
-- Standard audit trigger implementation
CREATE TRIGGER audit_trigger_row 
    AFTER INSERT OR UPDATE OR DELETE ON table_name
    FOR EACH ROW 
    EXECUTE FUNCTION tr_audit_trigger('application_user');

-- Journal trigger for data tracking
CREATE TRIGGER journal_trigger_row
    AFTER INSERT OR UPDATE OR DELETE ON table_name
    FOR EACH ROW
    EXECUTE FUNCTION tr_journal_trigger();
```

## Integration Points

### API Function Standards
All database API functions should follow these patterns:

```sql
-- Function naming: api_[action]_[entity]
CREATE OR REPLACE FUNCTION api_get_user_profile(p_user_id UUID)
RETURNS TABLE(...) AS $$
BEGIN
    -- Validate permissions
    IF NOT api_check_user_permission('read_profile') THEN
        RAISE EXCEPTION 'Insufficient permissions';
    END IF;
    
    -- Return data with appropriate security filtering
    RETURN QUERY
    SELECT ... FROM application_user
    WHERE id = p_user_id
    AND tenant_id = api_get_context_tenant_id();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Connection and Context Management
```sql
-- ALWAYS connect as API user and set schema
SET search_path = faunalogic_dapi_v1, public;

-- Set user context for session
SELECT api_set_context(
    p_user_id := 'uuid-here',
    p_tenant_id := 'tenant-uuid-here'
);

-- Get current context
SELECT api_get_context_user_id();
SELECT api_get_context_tenant_id();

-- Verify API user connection
SELECT current_user, current_schema();
-- Should return: faunalogic_api, faunalogic_dapi_v1
```

## Testing Standards

### Database Testing Approach
1. **Unit Tests**: Test individual functions and triggers
2. **Integration Tests**: Test API function workflows
3. **Spatial Tests**: Validate spatial operations and indexes
4. **Security Tests**: Verify access controls and transformations
5. **Performance Tests**: Validate query performance and scalability

### Validation Queries
```sql
-- Spatial data validation
SELECT ST_IsValid(geom) FROM spatial_component WHERE NOT ST_IsValid(geom);

-- Security policy validation
SET ROLE application_user;
SELECT api_set_context('user-id', 'tenant-id');
SELECT COUNT(*) FROM secure_spatial_component; -- Should only see tenant data

-- Audit log validation
SELECT COUNT(*) FROM audit_log WHERE table_name = 'test_table';
```

## Monitoring and Maintenance

### Database Health Checks
```sql
-- Connection monitoring
SELECT count(*) FROM pg_stat_activity;

-- Table size monitoring
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Index usage monitoring
SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes ORDER BY idx_tup_read DESC;

-- Spatial index validation
SELECT tablename, indexname FROM pg_indexes 
WHERE indexdef LIKE '%USING gist%' AND schemaname = 'public';
```

### Backup and Recovery
- **Daily backups**: Automated backup of entire database
- **Point-in-time recovery**: WAL archiving enabled
- **Spatial data validation**: PostGIS consistency checks
- **Migration rollback**: Rollback procedures for failed migrations

## Environment-Specific Considerations

### Development Environment
- Enable query logging for debugging
- Use development-specific security transforms
- Allow broader access for testing
- Enable all PostGIS extensions

### Production Environment
- Enable connection pooling (PgBouncer recommended)
- Configure appropriate shared_buffers and work_mem
- Set up monitoring and alerting
- Implement backup and recovery procedures
- Enable SSL connections
- Configure row-level security policies

## Troubleshooting Common Issues

### Spatial Data Issues
```sql
-- Fix invalid geometries
UPDATE spatial_table SET geom = ST_MakeValid(geom) WHERE NOT ST_IsValid(geom);

-- Rebuild spatial indexes
REINDEX INDEX idx_spatial_table_geom;

-- Check spatial reference systems
SELECT ST_SRID(geom) FROM spatial_table GROUP BY ST_SRID(geom);
```

### Performance Issues
```sql
-- Update table statistics
ANALYZE spatial_component;

-- Check slow queries
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;

-- Monitor connection usage
SELECT state, count(*) FROM pg_stat_activity GROUP BY state;
```

---

**Important**: This database service is the foundation for the entire FaunaLogic system. All spatial data operations, security policies, and multi-tenant isolation must be carefully implemented and tested.