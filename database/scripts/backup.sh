#!/bin/bash
# FaunaLogic Database Backup Script

set -e

# Configuration
DB_NAME="${DB_NAME:-faunalogic}"
DB_USER="${DB_USER:-postgres}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
BACKUP_DIR="${BACKUP_DIR:-./backups}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="faunalogic_backup_${TIMESTAMP}.sql"
COMPRESSED_FILE="faunalogic_backup_${TIMESTAMP}.sql.gz"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "Starting database backup for $DB_NAME..."
echo "Backup file: $BACKUP_DIR/$BACKUP_FILE"

# Create backup
pg_dump \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --username="$DB_USER" \
    --dbname="$DB_NAME" \
    --verbose \
    --clean \
    --if-exists \
    --create \
    --format=custom \
    --compress=6 \
    --file="$BACKUP_DIR/${BACKUP_FILE}.backup"

# Also create a plain SQL backup for easier inspection
pg_dump \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --username="$DB_USER" \
    --dbname="$DB_NAME" \
    --verbose \
    --clean \
    --if-exists \
    --create \
    --format=plain \
    --file="$BACKUP_DIR/$BACKUP_FILE"

# Compress the SQL file
gzip "$BACKUP_DIR/$BACKUP_FILE"

echo "Database backup completed successfully!"
echo "Files created:"
echo "  - Custom format: $BACKUP_DIR/${BACKUP_FILE}.backup"
echo "  - Compressed SQL: $BACKUP_DIR/$COMPRESSED_FILE"

# Clean up old backups (keep last 7 days)
find "$BACKUP_DIR" -name "faunalogic_backup_*.sql.gz" -mtime +7 -delete 2>/dev/null || true
find "$BACKUP_DIR" -name "faunalogic_backup_*.backup" -mtime +7 -delete 2>/dev/null || true

echo "Old backups cleaned up (keeping last 7 days)"