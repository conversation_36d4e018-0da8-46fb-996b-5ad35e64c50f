#!/usr/bin/env python3
"""
Script to format JSON objects in SQL INSERT statements for better readability.
This script will reformat the remaining unformatted JSON objects in the seed file.
"""

import re
import json

def format_json_in_sql(content):
    """Format JSON objects in SQL INSERT statements with proper indentation."""
    
    # Pattern to match JSON objects in INSERT statements
    pattern = r"'(\{[^']+\})'::jsonb"
    
    def format_json_match(match):
        json_str = match.group(1)
        try:
            # Parse and reformat the JSON
            json_obj = json.loads(json_str)
            formatted_json = json.dumps(json_obj, indent=2)
            
            # Add proper indentation for SQL context (4 spaces base + 2 for each level)
            lines = formatted_json.split('\n')
            indented_lines = []
            for i, line in enumerate(lines):
                if i == 0:
                    indented_lines.append('    \'{\n      ' + line[1:])  # First line
                elif i == len(lines) - 1:
                    indented_lines.append('    }\'::jsonb')  # Last line
                else:
                    indented_lines.append('      ' + line)  # Middle lines
            
            return '\n'.join(indented_lines)
        except json.JSONDecodeError:
            # If JSON is invalid, return original
            return match.group(0)
    
    # Apply formatting to all JSON objects
    formatted_content = re.sub(pattern, format_json_match, content, flags=re.DOTALL)
    return formatted_content

def main():
    input_file = '../dev-seed/V2_0_8__seed_secure_observations.sql'
    
    # Read the file
    with open(input_file, 'r') as f:
        content = f.read()
    
    # Format JSON objects
    formatted_content = format_json_in_sql(content)
    
    # Write back to file
    with open(input_file, 'w') as f:
        f.write(formatted_content)
    
    print("JSON formatting completed!")

if __name__ == "__main__":
    main()
