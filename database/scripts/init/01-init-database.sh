#!/bin/bash
# FaunaLogic Database Initialization Script
# This script is run during Docker container initialization

set -e

echo "Starting FaunaLogic database initialization..."

# Set up basic database configuration
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Enable logging
    ALTER SYSTEM SET log_statement = 'all';
    ALTER SYSTEM SET log_min_duration_statement = 1000;
    
    -- Configure for spatial data
    ALTER SYSTEM SET shared_preload_libraries = 'postgis-3';
    ALTER SYSTEM SET max_locks_per_transaction = 256;
    
    -- Performance settings for development
    ALTER SYSTEM SET shared_buffers = '256MB';
    ALTER SYSTEM SET effective_cache_size = '1GB';
    ALTER SYSTEM SET work_mem = '16MB';
    ALTER SYSTEM SET maintenance_work_mem = '256MB';
    
    -- Reload configuration
    SELECT pg_reload_conf();
EOSQL

echo "Database configuration completed."

# Create extensions if they don't exist
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE EXTENSION IF NOT EXISTS postgis CASCADE;
    CREATE EXTENSION IF NOT EXISTS postgis_raster CASCADE;
    CREATE EXTENSION IF NOT EXISTS postgis_topology CASCADE;
    CREATE EXTENSION IF NOT EXISTS postgis_sfcgal CASCADE;
    CREATE EXTENSION IF NOT EXISTS pgRouting CASCADE;
    CREATE EXTENSION IF NOT EXISTS fuzzystrmatch CASCADE;
    CREATE EXTENSION IF NOT EXISTS pgcrypto CASCADE;
EOSQL

echo "PostGIS extensions created successfully."

# Verify installation
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    SELECT postgis_version();
    SELECT pgrouting_version();
EOSQL

echo "FaunaLogic database initialization completed successfully."