#!/bin/bash
# FaunaLogic Database Migration Script

set -e

# Configuration
COMPOSE_FILE_FLAG="${COMPOSE_FILE_FLAG:--f docker-compose.yml}"
FLYWAY_SERVICE="${FLYWAY_SERVICE:-flyway}"

# Detect Docker Compose command (V1 vs V2)
if command -v docker-compose >/dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker-compose"
elif command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker compose"
else
    print_error "Docker Compose is not installed. Please install Docker Compose V1 or V2"
    exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if database is ready
wait_for_database() {
    print_status "Waiting for database to be ready..."
    
    until $DOCKER_COMPOSE_CMD $COMPOSE_FILE_FLAG exec postgres pg_isready -U postgres -d faunalogic; do
        echo "Database is not ready - waiting..."
        sleep 2
    done
    
    print_status "Database is ready!"
}

# Function to run Flyway command
run_flyway() {
    local command="$1"
    shift # Remove first argument
    print_status "Running Flyway $command..."
    
    $DOCKER_COMPOSE_CMD $COMPOSE_FILE_FLAG --profile migration run --rm "$FLYWAY_SERVICE" "$command" "$@"
}

# Main script logic
case "${1:-migrate}" in
    "info")
        print_status "Getting migration information..."
        wait_for_database
        shift # Remove command from arguments
        run_flyway "info" "$@"
        ;;
    "validate")
        print_status "Validating migrations..."
        wait_for_database
        shift # Remove command from arguments
        run_flyway "validate" "$@"
        ;;
    "migrate")
        print_status "Running database migrations..."
        wait_for_database
        shift # Remove command from arguments
        run_flyway "migrate" "$@"
        print_status "Migration completed successfully!"
        ;;
    "clean")
        print_warning "This will DROP ALL database objects!"
        read -p "Are you sure you want to continue? (yes/no): " -r
        if [[ $REPLY == "yes" ]]; then
            wait_for_database
            run_flyway "clean"
            print_status "Database cleaned!"
        else
            print_status "Operation cancelled."
        fi
        ;;
    "baseline")
        print_status "Creating baseline..."
        wait_for_database
        run_flyway "baseline"
        print_status "Baseline created!"
        ;;
    "repair")
        print_status "Repairing metadata table..."
        wait_for_database
        run_flyway "repair"
        print_status "Metadata table repaired!"
        ;;
    "undo")
        if [ -z "$2" ]; then
            print_error "Please provide a target version for undo operation"
            print_error "Usage: $0 undo <version>"
            exit 1
        fi
        print_warning "This will undo migrations to version $2"
        read -p "Are you sure you want to continue? (yes/no): " -r
        if [[ $REPLY == "yes" ]]; then
            wait_for_database
            run_flyway "undo -target=$2"
            print_status "Undo completed!"
        else
            print_status "Operation cancelled."
        fi
        ;;
    "help"|"-h"|"--help")
        echo "FaunaLogic Database Migration Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  migrate    Run pending migrations (default)"
        echo "  info       Show migration status"
        echo "  validate   Validate migrations"
        echo "  clean      Drop all database objects (dangerous!)"
        echo "  baseline   Create baseline version"
        echo "  repair     Repair metadata table"
        echo "  undo       Undo migrations to specified version"
        echo "  help       Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 migrate"
        echo "  $0 info"
        echo "  $0 undo 1.0.5"
        ;;
    *)
        print_error "Unknown command: $1"
        print_error "Use '$0 help' to see available commands"
        exit 1
        ;;
esac