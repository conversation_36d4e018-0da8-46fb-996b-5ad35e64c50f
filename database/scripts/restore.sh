#!/bin/bash
# FaunaLogic Database Restore Script

set -e

# Configuration
DB_NAME="${DB_NAME:-faunalogic}"
DB_USER="${DB_USER:-postgres}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
BACKUP_DIR="${BACKUP_DIR:-./backups}"

# Check if backup file is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <backup_file>"
    echo "Available backups:"
    ls -la "$BACKUP_DIR"/faunalogic_backup_*.backup 2>/dev/null || echo "No backup files found"
    exit 1
fi

BACKUP_FILE="$1"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    # Try to find it in the backup directory
    if [ -f "$BACKUP_DIR/$BACKUP_FILE" ]; then
        BACKUP_FILE="$BACKUP_DIR/$BACKUP_FILE"
    else
        echo "Error: Backup file '$BACKUP_FILE' not found"
        exit 1
    fi
fi

echo "Starting database restore for $DB_NAME..."
echo "Backup file: $BACKUP_FILE"

# Determine backup format
if [[ "$BACKUP_FILE" == *.backup ]]; then
    # Custom format backup
    echo "Restoring from custom format backup..."
    pg_restore \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="$DB_NAME" \
        --verbose \
        --clean \
        --if-exists \
        --create \
        --exit-on-error \
        "$BACKUP_FILE"
elif [[ "$BACKUP_FILE" == *.sql.gz ]]; then
    # Compressed SQL backup
    echo "Restoring from compressed SQL backup..."
    gunzip -c "$BACKUP_FILE" | psql \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="postgres" \
        --single-transaction
elif [[ "$BACKUP_FILE" == *.sql ]]; then
    # Plain SQL backup
    echo "Restoring from SQL backup..."
    psql \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="postgres" \
        --single-transaction \
        --file="$BACKUP_FILE"
else
    echo "Error: Unsupported backup format. Supported formats: .backup, .sql, .sql.gz"
    exit 1
fi

echo "Database restore completed successfully!"

# Verify restore
echo "Verifying database..."
psql \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --username="$DB_USER" \
    --dbname="$DB_NAME" \
    --command="SELECT 'Database restored successfully!' as status;"

echo "Database verification completed successfully!"