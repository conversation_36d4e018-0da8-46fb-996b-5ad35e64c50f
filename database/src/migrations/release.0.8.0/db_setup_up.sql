--  db_setup_up.sql
\set ON_ERROR_STOP on
-- drop the database
set role postgres;
\c postgres
drop database faunalogic;
drop role faunalogic_api;
\set ON_ERROR_STOP on
create database faunalogic;
\c faunalogic

set client_min_messages=warning;

-- TODO: lock down public but allow access to postgis installed there
--REVOKE ALL PRIVILEGES ON SCHEMA public FROM PUBLIC;

-- set up spatial extensions
\i create_spatial_extensions.psql

-- set up project management schema
create schema if not exists faunalogic;
GRANT ALL ON SCHEMA faunalogic TO postgres;
set search_path = faunalogic, public;

-- setup faunalogic api schema
create schema if not exists faunalogic_dapi_v1;

-- setup api user
create user faunalogic_api password 'flatpass';
alter schema faunalogic_dapi_v1 owner to faunalogic_api;

-- Grant rights on faunalogic_dapi_v1 to faunalogic_api user
grant all on schema faunalogic_dapi_v1 to faunalogic_api;
grant all on schema faunalogic_dapi_v1 to postgres;
alter DEFAULT PRIVILEGES in SCHEMA faunalogic_dapi_v1 grant ALL on tables to faunalogic_api;
alter DEFAULT PRIVILEGES in SCHEMA faunalogic_dapi_v1 grant ALL on tables to postgres;

-- faunalogic grants
GRANT USAGE ON SCHEMA faunalogic TO faunalogic_api;
ALTER DEFAULT PRIVILEGES IN SCHEMA faunalogic GRANT ALL ON TABLES TO faunalogic_api;

alter role faunalogic_api set search_path to faunalogic_dapi_v1, faunalogic, public, topology;

\i faunalogic.sql
\i populate_user_identity_source.sql
\i api_set_context.sql
\i tr_audit_trigger.sql
\i tr_generated_audit_triggers.sql
\i api_get_context_user_id.sql
\i api_get_context_system_user_role_id.sql
\i api_user_is_administrator.sql
\i tr_journal_trigger.sql
\i tr_generated_journal_triggers.sql
\i api_get_system_constant.sql
\i api_get_system_metadata_constant.sql
\i create_sequences.sql

-- populate look up tables
\i populate_system_constant.sql
\i populate_system_role.sql
\i populate_system_metadata_constant.sql
\i populate_submission_status_type.sql
\i populate_submission_message_class.sql
\i populate_submission_message_type.sql
\i populate_proprietary_type.sql
\i populate_persecution_or_harm_type.sql
\i populate_persecution_or_harm.sql

-- create the views
set search_path = faunalogic_dapi_v1;
set role faunalogic_api;
\i vw_generated_dapi_views.sql

set role postgres;
set search_path = faunalogic;
grant execute on function api_set_context(_system_user_identifier "system_user".user_identifier%type, _user_identity_source_name user_identity_source.name%type) to faunalogic_api;
