{"compilerOptions": {"module": "commonjs", "lib": ["es2020"], "baseUrl": "src", "outDir": "dist", "target": "es2018", "sourceMap": true, "allowJs": false, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitThis": true, "noImplicitAny": true, "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "noFallthroughCasesInSwitch": true, "strict": true, "typeRoots": ["node_modules/@types"]}, "include": ["src"], "ts-node": {"swc": true}}