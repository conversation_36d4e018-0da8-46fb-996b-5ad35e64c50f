# ------------------------------------------------------------------------------
# Notes
#
# - Exposed Ports/URLs
#   - Certain ports/urls are exposed in docker-compose and may conflict with other
#     docker-containers if they are exposing the same ports/urls.
#
#   - If conflicts arise, modify the conflicting values in your `.env` and re-build.
#
#   - List of exposed ports/urls:
#     - APP_PORT
#     - API_PORT
#     - APP_HOST
#     - DB_PORT
#     - CLAMAV_PORT      # Only relevant if you have `ENABLE_FILE_VIRUS_SCAN=true`
#     - DOCKER_NAMESPACE # Only relevant if you wish to run the same repo multiple times
# ------------------------------------------------------------------------------

# ------------------------------------------------------------------------------
# Environment Details
# ------------------------------------------------------------------------------
NODE_ENV=development

# Dictates the max size of the heap (Mb). Should not be greater than 75% of total available memory. The default seems to be 2Gb.
API_NODE_OPTIONS=--max-old-space-size=4096
APP_NODE_OPTIONS=--max-old-space-size=4096

# ------------------------------------------------------------------------------
# App
# ------------------------------------------------------------------------------
APP_PORT=7100

SITEMINDER_LOGOUT_URL=https://logontest7.gov.bc.ca/clp-cgi/logoff.cgi

# ------------------------------------------------------------------------------
# API
# ------------------------------------------------------------------------------
API_HOST=localhost
API_PORT=6100
API_TZ=America/Vancouver

# Used by the API to generate APP urls
APP_HOST=http://localhost:7100

# ------------------------------------------------------------------------------
# API - Logging
# ------------------------------------------------------------------------------
# See `api/utils/logger.ts` for details on LOG_LEVEL and LOG_LEVEL_FILE

# Log level when logging to the console
LOG_LEVEL=debug

# Log level when logging to a persistent file (See `api/data`)
LOG_LEVEL_FILE=debug
# See https://github.com/winstonjs/winston-daily-rotate-file for param details
LOG_FILE_DIR=data/logs
LOG_FILE_NAME=faunalogic-platform-api.log
LOG_FILE_DATE_PATTERN=YYYY-MM-DD
LOG_FILE_MAX_SIZE=45m
LOG_FILE_MAX_FILES=10

# ------------------------------------------------------------------------------
# Postgres Database
# ------------------------------------------------------------------------------
POSTGRES_VERSION=17-bullseye
POSTGIS_VERSION=3
DB_HOST=db
DB_ADMIN=postgres
DB_ADMIN_PASS=postgres
DB_USER_API=faunalogic_api
DB_USER_API_PASS=postgres
DB_PORT=5432
DB_DATABASE=faunalogic
DB_SCHEMA=faunalogic
DB_TZ=America/Vancouver
PGDATA=/var/lib/postgresql/data
# How many connections the pool has
DB_POOL_SIZE=20
# How many uses a pool connection gets before it is re-created
DB_CONNECTION_MAX_USES=7500
# How many milliseconds to wait to establish a connection before failing
DB_CONNECTION_TIMEOUT=0
# How many milliseconds to wait for a query to finish before failing
DB_IDLE_TIMEOUT=10000

# ------------------------------------------------------------------------------
# KeyCloak Configuration for Keycloak Common Hosted Single Sign-on (CSS)
# CSS: https://bcgov.github.io/sso-requests
#
# See `keycloak` secret in openshift
# ------------------------------------------------------------------------------

# The host URL used to authenticate with Keycloak
KEYCLOAK_HOST=https://dev.loginproxy.gov.bc.ca/auth
# The Keycloak Realm used for authentication
KEYCLOAK_REALM=standard
# The identifier for the FaunaLogic Browser Login CSS resource
KEYCLOAK_CLIENT_ID=fauna-logic-browser-4230

# The identifier for the FaunaLogic Service User CSS resource
KEYCLOAK_ADMIN_USERNAME=faunalogic-svc-4466
# The secret key for the FaunaLogic Service User CSS resource
KEYCLOAK_ADMIN_PASSWORD=

# The identifier for the Keycloak CSS API
KEYCLOAK_API_CLIENT_ID=service-account-team-1159-4197
# The secret key for the Keycloak CSS API
KEYCLOAK_API_CLIENT_SECRET=
# The Keycloak API Token URL (only used to generate the Bearer token required to call the KEYCLOAK_API_HOST)
KEYCLOAK_API_TOKEN_URL=https://loginproxy.gov.bc.ca/auth/realms/standard/protocol/openid-connect/token
# The Keycloak API host URL
KEYCLOAK_API_HOST=https://api.loginproxy.gov.bc.ca/api/v1
# The targeted Keycloak environment (dev, test or prod)
KEYCLOAK_API_ENVIRONMENT=dev

# ------------------------------------------------------------------------------
# File Upload
# ------------------------------------------------------------------------------
# Max request size for a non-file-upload request (bytes)
MAX_REQ_BODY_SIZE=********

# Max size of each file in a file-upload request
MAX_UPLOAD_NUM_FILES=10

# Max size of each file in a file-upload request (bytes)
MAX_UPLOAD_FILE_SIZE=********

# ------------------------------------------------------------------------------
# Object Store (S3)
# ------------------------------------------------------------------------------
OBJECT_STORE_URL=nrs.objectstore.gov.bc.ca
OBJECT_STORE_ACCESS_KEY_ID=
OBJECT_STORE_SECRET_KEY_ID=
OBJECT_STORE_BUCKET_NAME=
S3_KEY_PREFIX=local/faunalogic

# ------------------------------------------------------------------------------
# Docker Details
# ------------------------------------------------------------------------------
DOCKER_PROJECT_NAME=platform
DOCKER_NAMESPACE=all

# ------------------------------------------------------------------------------
# Clamav - Virus scanning
# ------------------------------------------------------------------------------
CLAMAV_PORT=3310
CLAMAV_HOST=clamav
ENABLE_FILE_VIRUS_SCAN=false

# ------------------------------------------------------------------------------
# GCNotify - Email and SMS api
# ------------------------------------------------------------------------------
GCNOTIFY_SECRET_API_KEY=
GCNOTIFY_ADMIN_EMAIL=<EMAIL>
GCNOTIFY_REQUEST_ACCESS_SECURE_DOCUMENTS=4bb42a76-f79b-424f-ad0f-ad3671389ec2
GCNOTIFY_ONBOARDING_REQUEST_EMAIL_TEMPLATE=7779a104-b863-40ac-902f-1aa607d2071a
GCNOTIFY_ONBOARDING_REQUEST_SMS_TEMPLATE=af2f1e40-bd72-4612-9c5a-567ee5b26ca5
GCNOTIFY_EMAIL_URL=https://api.notification.canada.ca/v2/notifications/email
GCNOTIFY_SMS_URL=https://api.notification.canada.ca/v2/notifications/sms

# ------------------------------------------------------------------------------
# ITIS Platform API
# ------------------------------------------------------------------------------
ITIS_SOLR_URL=https://services.itis.gov

# ------------------------------------------------------------------------------
# Seeding - Development
# ------------------------------------------------------------------------------
# Enable or disable the mock data seeding in '04_mock_test_data.ts' and '06_submission_data.ts'
ENABLE_MOCK_FEATURE_SEEDING=false
# Configure how many feature submission records to seed in '04_mock_test_data.ts'
NUM_MOCK_FEATURE_SUBMISSIONS=0
