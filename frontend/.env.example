# FaunaLogic Frontend Environment Configuration Template
# Copy this file to .env.local and customize for your local development

# API Configuration
# Override API base URL (optional - defaults to proxy configuration)
# VITE_API_BASE_URL=http://localhost:8000/api/v1

# Development Configuration
VITE_DEV_MODE=true
VITE_ENV=development

# Application Configuration
VITE_APP_TITLE=FaunaLogic
VITE_APP_DESCRIPTION=Wildlife Data Visualization Platform

# Map Configuration
VITE_DEFAULT_MAP_ZOOM=6
VITE_DEFAULT_MAP_CENTER_LAT=54.15
VITE_DEFAULT_MAP_CENTER_LNG=-126.5

# Feature Flags
VITE_ENABLE_DEBUG_LOGGING=true
VITE_ENABLE_DEVELOPMENT_TOOLS=true

# Authentication (when integrated)
# VITE_AUTH_URL=http://localhost:8080
# VITE_AUTH_REALM=faunalogic
# VITE_AUTH_CLIENT_ID=faunalogic-frontend

# External Services
# VITE_SENTRY_DSN=your-sentry-dsn-here
# VITE_GOOGLE_ANALYTICS_ID=your-ga-id-here

# Build Configuration
# VITE_BUILD_TIMESTAMP=auto-generated
# VITE_VERSION=auto-generated

# Note: For development, the Vite dev server proxies API requests
# to localhost:8000, so VITE_API_BASE_URL is typically not needed