# FaunaLogic Frontend Makefile
# Vue.js application with Docker support

.PHONY: help install dev build test lint format clean docker-build docker-run docker-dev docker-logs docker-clean

# Default target
help: ## Show this help message
	@echo "FaunaLogic Frontend - Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development targets
install: ## Install dependencies
	npm ci

dev: ## Start development server
	npm run dev

build: ## Build production application
	npm run build

preview: ## Preview production build
	npm run preview

# Quality assurance targets
test: ## Run tests
	npm run test

test-coverage: ## Run tests with coverage
	npm run test:coverage

test-ui: ## Run tests with UI
	npm run test:ui

lint: ## Run linting
	npm run lint

lint-check: ## Check linting without fixing
	npm run lint:check

format: ## Format code
	npm run format

format-check: ## Check code formatting
	npm run format:check

type-check: ## Type check TypeScript
	npm run type-check

# Utility targets
clean: ## Clean build artifacts and dependencies
	npm run clean
	rm -rf node_modules

# Docker targets
docker-build: ## Build production Docker image
	docker build -t faunalogic-frontend:latest .

docker-build-dev: ## Build development Docker image
	docker build -f Dockerfile.dev -t faunalogic-frontend:dev .

docker-run: ## Run production Docker container
	docker run -d --name faunalogic-frontend -p 3000:8080 faunalogic-frontend:latest

docker-run-dev: ## Run development Docker container with hot reload
	docker run -d --name faunalogic-frontend-dev -p 3000:3000 \
		-v $$(pwd):/app -v /app/node_modules \
		faunalogic-frontend:dev

docker-stop: ## Stop Docker containers
	docker stop faunalogic-frontend faunalogic-frontend-dev 2>/dev/null || true

docker-remove: ## Remove Docker containers
	docker rm faunalogic-frontend faunalogic-frontend-dev 2>/dev/null || true

docker-logs: ## Show Docker container logs
	docker logs -f faunalogic-frontend

docker-logs-dev: ## Show development Docker container logs
	docker logs -f faunalogic-frontend-dev

docker-clean: docker-stop docker-remove ## Clean up Docker containers and images
	docker rmi faunalogic-frontend:latest faunalogic-frontend:dev 2>/dev/null || true

# Docker Compose targets
up: ## Start services with Docker Compose
	docker-compose up -d

up-dev: ## Start development services with Docker Compose
	docker-compose --profile dev up -d frontend-dev

down: ## Stop Docker Compose services
	docker-compose down

logs: ## Show Docker Compose logs
	docker-compose logs -f frontend

logs-dev: ## Show development Docker Compose logs
	docker-compose logs -f frontend-dev

restart: down up ## Restart Docker Compose services

restart-dev: down up-dev ## Restart development Docker Compose services

# Combined targets
ci: install lint-check type-check test-coverage ## Run CI pipeline (install, lint, type-check, test)

local-setup: install ## Setup local development environment
	@echo "Local development environment ready!"
	@echo "Run 'make dev' to start the development server"

docker-setup: docker-build docker-run ## Setup Docker environment
	@echo "Docker environment ready!"
	@echo "Frontend available at http://localhost:3000"

docker-dev-setup: docker-build-dev docker-run-dev ## Setup Docker development environment
	@echo "Docker development environment ready!"
	@echo "Frontend with hot reload available at http://localhost:3000"

# Health check
health: ## Check if application is running
	@curl -f http://localhost:3000/health > /dev/null 2>&1 && echo "✅ Frontend is healthy" || echo "❌ Frontend is not responding"