# FaunaLogic Frontend

Vue.js frontend application for the FaunaLogic spatial wildlife data management system.

## Features

- **Interactive Map**: Leaflet-based map with OpenStreetMap tiles
- **Spatial Data Visualization**: Displays wildlife data points for British Columbia
- **Responsive Design**: Built with Vuetify for mobile-first design
- **Real-time Data**: Fetches data from the FaunaLogic API

## Tech Stack

- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vuetify 3** for UI components
- **Leaflet** for interactive maps
- **Vite** for fast development
- **Vitest** for testing

## Quick Start

```bash
# Install dependencies
make install

# Start development server
make dev

# Build for production
make build
```

## Map Integration

The homepage displays an interactive map centered on British Columbia, Canada. On initial load, it fetches wildlife data from the API using the following bounding box:

```typescript
const bcBounds = {
  min_lng: -139,
  min_lat: 48.3,
  max_lng: -114,
  max_lat: 60,
  limit: 100
}
```

API endpoint: `/api/v1/map/data` (proxied to backend)

## Project Structure

```
src/
├── components/
│   ├── AppToolbar.vue     # Main navigation toolbar
│   └── MapView.vue        # Leaflet map component
├── views/
│   └── HomePage.vue       # Main homepage layout
├── api/
│   └── mapService.ts      # API service for map data
├── types/
│   └── map.ts            # TypeScript type definitions
└── styles/
    └── variables.scss     # Vuetify theme variables
```

## Development Commands

```bash
# Development
make dev                   # Start dev server
make build                 # Build for production
make preview              # Preview production build

# Quality Assurance
make test                 # Run tests
make test-coverage        # Run tests with coverage
make lint                 # Run linting
make format               # Format code
make type-check           # TypeScript type checking

# Docker
make build                 # Build production image
make up                    # Start services with Docker Compose
make down                  # Stop Docker Compose services
make logs                  # View Docker Compose logs
make docker-build-dev      # Build dev image
make docker-run-dev        # Run dev container with hot reload
```

## API Integration

The application uses relative URLs and Vite's proxy configuration to connect to the FaunaLogic API. During development, all `/api/*` requests are proxied to the backend running on `http://localhost:8000`.

**Development Setup:**
1. Start the API service first:
   ```bash
   # In the api directory
   make dev
   ```

2. Start the frontend (it will proxy API calls):
   ```bash
   # In the frontend directory
   make dev
   ```

**Proxy Configuration:**
The Vite development server is configured to proxy API requests:
- Frontend calls: `/api/v1/map/data`
- Proxied to: `http://localhost:8000/api/v1/map/data`

This approach works seamlessly across:
- **Desktop browsers** (localhost)
- **Mobile devices** (same network)
- **Production deployments**

## Environment Variables

The application supports environment-specific configuration:

```bash
# Optional: Override API base URL for production
VITE_API_BASE_URL=https://api.yourdomain.com

# Development uses relative URLs with proxy (recommended)
# No environment variables needed
```

**Available Environment Files:**
- `.env.local` - Local development overrides
- `.env.production` - Production configuration  
- `.env.test` - Test environment configuration

## Production Deployment

```bash
# Build and run with Docker
make docker-build
make docker-run

# Or with Docker Compose
make up
```

The production build is served by nginx and available on port 3000.