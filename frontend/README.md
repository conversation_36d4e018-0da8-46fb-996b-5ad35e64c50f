# FaunaLogic Frontend

Vue.js frontend application for the FaunaLogic spatial wildlife data management system.

## Features

- **Interactive Map**: Leaflet-based map with OpenStreetMap tiles
- **Spatial Data Visualization**: Displays wildlife data points for British Columbia
- **Responsive Design**: Built with Vuetify for mobile-first design
- **Real-time Data**: Fetches data from the FaunaLogic API

## Tech Stack

- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vuetify 3** for UI components
- **Leaflet** for interactive maps
- **Vite** for fast development
- **Vitest** for testing

## Quick Start

```bash
# Install dependencies
make install

# Start development server
make dev

# Build for production
make build
```

## Map Integration

The homepage displays an interactive map centered on British Columbia, Canada. On initial load, it fetches wildlife data from the API using the following bounding box:

```typescript
const bcBounds = {
  min_lng: -139,
  min_lat: 48.3,
  max_lng: -114,
  max_lat: 60,
  limit: 100
}
```

API endpoint: `http://localhost:8000/api/v1/map/data`

## Project Structure

```
src/
├── components/
│   ├── AppToolbar.vue     # Main navigation toolbar
│   └── MapView.vue        # Leaflet map component
├── views/
│   └── HomePage.vue       # Main homepage layout
├── api/
│   └── mapService.ts      # API service for map data
├── types/
│   └── map.ts            # TypeScript type definitions
└── styles/
    └── variables.scss     # Vuetify theme variables
```

## Development Commands

```bash
# Development
make dev                   # Start dev server
make build                 # Build for production
make preview              # Preview production build

# Quality Assurance
make test                 # Run tests
make test-coverage        # Run tests with coverage
make lint                 # Run linting
make format               # Format code
make type-check           # TypeScript type checking

# Docker
make docker-build         # Build production image
make docker-run           # Run production container
make docker-build-dev     # Build dev image
make docker-run-dev       # Run dev container with hot reload
```

## API Integration

The application connects to the FaunaLogic API running on `http://localhost:8000`. The API provides spatial wildlife data that is visualized on the map.

Ensure the API service is running before starting the frontend:

```bash
# In the api directory
make dev
```

## Environment Variables

The application uses Vite's proxy configuration to connect to the API. The default API URL is `http://localhost:3001` but can be overridden:

```bash
export VITE_API_BASE_URL=http://localhost:8000
```

## Production Deployment

```bash
# Build and run with Docker
make docker-build
make docker-run

# Or with Docker Compose
make up
```

The production build is served by nginx and available on port 3000.