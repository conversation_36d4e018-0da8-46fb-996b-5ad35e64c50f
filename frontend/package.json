{"name": "faunalogic-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write src/", "format:check": "prettier --check src/", "type-check": "vue-tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "prepare": "husky install", "docker:build": "docker build -t faunalogic-frontend .", "docker:build:dev": "docker build -f Dockerfile.dev -t faunalogic-frontend:dev .", "docker:run": "docker run -p 3000:8080 faunalogic-frontend", "docker:run:dev": "docker run -p 3000:3000 -v $(pwd):/app -v /app/node_modules faunalogic-frontend:dev", "docker:up": "docker-compose up -d", "docker:up:dev": "docker-compose --profile dev up -d frontend-dev", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f frontend", "docker:logs:dev": "docker-compose logs -f frontend-dev"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vuetify": "^3.4.4", "@mdi/font": "^7.3.67", "axios": "^1.6.1", "leaflet": "^1.9.4", "@vue-leaflet/vue-leaflet": "^0.10.1", "vee-validate": "^4.11.8", "@vee-validate/yup": "^4.11.8", "yup": "^1.3.3", "date-fns": "^2.30.0"}, "devDependencies": {"@types/leaflet": "^1.9.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.2", "@vue/tsconfig": "^0.4.0", "eslint": "^8.54.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-vue": "^9.18.1", "husky": "^8.0.3", "jsdom": "^23.0.1", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "sass": "^1.69.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-vuetify": "^2.0.1", "vitest": "^0.34.6", "vitest-sonar-reporter": "^0.4.1", "vue-tsc": "^1.8.22"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{css,scss,md,json}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}