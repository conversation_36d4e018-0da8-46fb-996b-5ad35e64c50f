# Frontend Service Coding Standards

**Version**: 1.0.0  
**Last Updated**: 2025-06-27  
**Status**: ✅ Active  
**Service**: Frontend Layer

## Vue.js and TypeScript Standards

### General Principles
- **Component-First Design**: Build reusable, composable Vue components
- **Type Safety**: Use TypeScript strictly with proper type definitions
- **Composition API**: Prefer Composition API over Options API
- **Material Design 3**: Follow Google Material Design 3 (Material You) guidelines
- **Vuetify 3**: Use Vuetify 3 components with Material Design 3 implementation
- **Performance**: Optimize rendering and bundle size
- **Accessibility**: Ensure WCAG 2.1 AA compliance
- **Responsive Design**: Mobile-first approach for all components

### File and Directory Naming
```typescript
// Components: PascalCase
UserProfileCard.tsx
SpatialDataMap.tsx
NavigationMenu.tsx

// Hooks: camelCase starting with 'use'
useAuth.ts
useApi.ts
useLocalStorage.ts

// Utilities: camelCase
formatDate.ts
validateForm.ts
spatialUtils.ts

// Types: PascalCase with descriptive names
UserProfile.ts
SpatialData.ts
ApiResponse.ts

// Constants: SCREAMING_SNAKE_CASE
API_ENDPOINTS.ts
DEFAULT_VALUES.ts
ERROR_MESSAGES.ts
```

### Component Structure Standards
```tsx
// Component file template
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { Button, Card, LoadingSpinner } from '../common';
import { UserProfile } from '../../types/user';
import { fetchUser, updateUser } from '../../store/userSlice';

// Props interface with clear documentation
interface UserProfileCardProps {
  /** Unique identifier for the user */
  userId: string;
  /** Callback fired when user is updated */
  onUpdate?: (user: UserProfile) => void;
  /** Whether the card is in read-only mode */
  readonly?: boolean;
  /** Additional CSS class name */
  className?: string;
  /** Test ID for testing purposes */
  testId?: string;
}

// Main component with proper TypeScript
const UserProfileCard: React.FC<UserProfileCardProps> = ({
  userId,
  onUpdate,
  readonly = false,
  className,
  testId = 'user-profile-card'
}) => {
  // Local state declarations
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Partial<UserProfile>>({});

  // Redux hooks
  const dispatch = useAppDispatch();
  const { user, loading, error } = useAppSelector(state => state.users);

  // Memoized calculations
  const displayName = useMemo(() => {
    if (!user) return '';
    return `${user.firstName} ${user.lastName}`.trim();
  }, [user]);

  // Callback handlers
  const handleEdit = useCallback(() => {
    if (user) {
      setFormData({ firstName: user.firstName, lastName: user.lastName });
      setIsEditing(true);
    }
  }, [user]);

  const handleSave = useCallback(async () => {
    if (!user || !formData) return;

    try {
      const updatedUser = await dispatch(updateUser({
        id: user.id,
        ...formData
      })).unwrap();
      
      onUpdate?.(updatedUser);
      setIsEditing(false);
    } catch (error) {
      // Error handling is done by Redux middleware
    }
  }, [dispatch, user, formData, onUpdate]);

  const handleCancel = useCallback(() => {
    setIsEditing(false);
    setFormData({});
  }, []);

  // Effects
  useEffect(() => {
    if (userId) {
      dispatch(fetchUser(userId));
    }
  }, [dispatch, userId]);

  // Early returns for loading/error states
  if (loading) {
    return (
      <StyledCard className={className} data-testid={testId}>
        <LoadingSpinner />
      </StyledCard>
    );
  }

  if (error) {
    return (
      <StyledCard className={className} data-testid={testId}>
        <ErrorMessage>Failed to load user profile</ErrorMessage>
      </StyledCard>
    );
  }

  if (!user) {
    return (
      <StyledCard className={className} data-testid={testId}>
        <NotFoundMessage>User not found</NotFoundMessage>
      </StyledCard>
    );
  }

  // Main render
  return (
    <StyledCard className={className} data-testid={testId}>
      <CardHeader>
        <UserAvatar
          src={user.avatarUrl}
          alt={`Avatar for ${displayName}`}
          loading="lazy"
        />
        <UserInfo>
          {isEditing ? (
            <EditForm>
              <Input
                value={formData.firstName || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                placeholder="First name"
                aria-label="First name"
              />
              <Input
                value={formData.lastName || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                placeholder="Last name"
                aria-label="Last name"
              />
            </EditForm>
          ) : (
            <>
              <UserName>{displayName}</UserName>
              <UserEmail>{user.email}</UserEmail>
            </>
          )}
        </UserInfo>
        
        {!readonly && (
          <ActionButtons>
            {isEditing ? (
              <>
                <Button variant="primary" onClick={handleSave} size="small">
                  Save
                </Button>
                <Button variant="secondary" onClick={handleCancel} size="small">
                  Cancel
                </Button>
              </>
            ) : (
              <Button variant="ghost" onClick={handleEdit} size="small">
                Edit
              </Button>
            )}
          </ActionButtons>
        )}
      </CardHeader>
    </StyledCard>
  );
};

// Styled components with proper theme usage
const StyledCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    padding: ${({ theme }) => theme.spacing.md};
  }
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    align-items: flex-start;
  }
`;

const UserAvatar = styled.img`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0; // Allows text truncation
`;

const UserName = styled.h3`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.sizes.lg};
  font-weight: ${({ theme }) => theme.typography.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`;

const UserEmail = styled.p`
  margin: 4px 0 0 0;
  font-size: ${({ theme }) => theme.typography.sizes.sm};
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const EditForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  flex-shrink: 0;
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  text-align: center;
  padding: ${({ theme }) => theme.spacing.lg};
`;

const NotFoundMessage = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  text-align: center;
  padding: ${({ theme }) => theme.spacing.lg};
`;

export default UserProfileCard;
```

## TypeScript Type Definitions

### Component Props Patterns
```typescript
// types/component.ts
import { ReactNode, HTMLAttributes, AriaAttributes } from 'react';

// Base props that all components should extend
interface BaseComponentProps extends HTMLAttributes<HTMLElement>, AriaAttributes {
  className?: string;
  testId?: string;
  children?: ReactNode;
}

// Size variants for consistent sizing
type Size = 'small' | 'medium' | 'large';
type Variant = 'primary' | 'secondary' | 'danger' | 'ghost';

// Button component props
interface ButtonProps extends BaseComponentProps {
  variant?: Variant;
  size?: Size;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
}

// Form input props
interface InputProps extends BaseComponentProps {
  value: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  helperText?: string;
  required?: boolean;
  type?: 'text' | 'email' | 'password' | 'number' | 'url' | 'tel';
  autoComplete?: string;
  name?: string;
}

// Modal/Dialog props
interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  closeOnBackdropClick?: boolean;
  showCloseButton?: boolean;
}
```

### API and Data Types
```typescript
// types/api.ts
// Standard API response wrapper
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

// Paginated response
interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API client methods
interface ApiClient {
  get<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
  post<T>(url: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>>;
  put<T>(url: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>>;
  delete<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
}

// Request configuration
interface RequestConfig {
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}
```

### Spatial Data Types
```typescript
// types/spatial.ts
// GeoJSON type definitions
interface GeoJSONGeometry {
  type: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
  coordinates: number[] | number[][] | number[][][];
}

interface GeoJSONFeature {
  type: 'Feature';
  id?: string | number;
  geometry: GeoJSONGeometry;
  properties: Record<string, unknown>;
}

interface GeoJSONFeatureCollection {
  type: 'FeatureCollection';
  features: GeoJSONFeature[];
}

// Application-specific spatial types
interface SpatialData {
  id: string;
  geometry: GeoJSONGeometry;
  properties: {
    name?: string;
    description?: string;
    type: string;
    color?: string;
    [key: string]: unknown;
  };
  metadata: {
    collector: string;
    collectionDate: string;
    accuracy?: number;
    equipment?: string;
  };
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

interface MapViewport {
  center: [number, number];
  zoom: number;
  bounds?: MapBounds;
}
```

## State Management Standards

### Redux Slice Patterns
```typescript
// store/spatialSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SpatialData, MapBounds } from '../types/spatial';
import { spatialApi } from '../api/spatialApi';

// Async thunk with proper typing
export const fetchSpatialDataInBounds = createAsyncThunk(
  'spatial/fetchInBounds',
  async (bounds: MapBounds, { rejectWithValue }) => {
    try {
      const response = await spatialApi.getSpatialDataInBounds(bounds);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to fetch spatial data');
    }
  }
);

// State interface
interface SpatialState {
  data: SpatialData[];
  selectedFeature: SpatialData | null;
  loading: boolean;
  error: string | null;
  mapBounds: MapBounds | null;
  filters: {
    dateRange?: [string, string];
    dataTypes?: string[];
    collectors?: string[];
  };
}

const initialState: SpatialState = {
  data: [],
  selectedFeature: null,
  loading: false,
  error: null,
  mapBounds: null,
  filters: {}
};

// Slice with type-safe reducers
const spatialSlice = createSlice({
  name: 'spatial',
  initialState,
  reducers: {
    setSelectedFeature: (state, action: PayloadAction<SpatialData | null>) => {
      state.selectedFeature = action.payload;
    },
    setMapBounds: (state, action: PayloadAction<MapBounds>) => {
      state.mapBounds = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<SpatialState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    clearData: (state) => {
      state.data = [];
      state.selectedFeature = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSpatialDataInBounds.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSpatialDataInBounds.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchSpatialDataInBounds.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { setSelectedFeature, setMapBounds, updateFilters, clearError, clearData } = spatialSlice.actions;
export default spatialSlice.reducer;
```

### Custom Hooks Standards
```typescript
// hooks/useApi.ts
import { useState, useCallback } from 'react';
import { ApiResponse } from '../types/api';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiReturn<T> extends UseApiState<T> {
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
}

export function useApi<T, Args extends any[]>(
  apiFunction: (...args: Args) => Promise<ApiResponse<T>>
): UseApiReturn<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null
  });

  const execute = useCallback(async (...args: Args): Promise<T> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await apiFunction(...args);
      
      if (response.success) {
        setState({
          data: response.data,
          loading: false,
          error: null
        });
        return response.data;
      } else {
        throw new Error(response.error?.message || 'API call failed');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'An unexpected error occurred';
      setState({
        data: null,
        loading: false,
        error: errorMessage
      });
      throw error;
    }
  }, [apiFunction]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null
    });
  }, []);

  return {
    ...state,
    execute,
    reset
  };
}

// Usage example
const useUserApi = () => {
  const createUserMutation = useApi(userApi.createUser);
  const updateUserMutation = useApi(userApi.updateUser);
  const deleteUserMutation = useApi(userApi.deleteUser);

  return {
    createUser: createUserMutation,
    updateUser: updateUserMutation,
    deleteUser: deleteUserMutation
  };
};
```

## Styling and Theme Standards

### Material Design 3 with Vuetify Theme

The frontend application follows Google Material Design 3 (Material You) guidelines implemented through Vuetify 3. This ensures a modern, accessible, and consistent user interface that adapts to user preferences and system themes.

#### Material Design 3 Principles
- **Dynamic Color**: Support for user-generated color schemes
- **Accessibility**: Built-in high contrast and large text support  
- **Adaptability**: Responsive design across all device sizes
- **Expressiveness**: Personal and adaptive design language

### CSS Variables and Vuetify Theme
```typescript
// styles/theme.ts
export const theme = {
  colors: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      500: '#0ea5e9',
      600: '#0284c7',
      900: '#0c4a6e'
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      500: '#6b7280',
      900: '#111827'
    },
    text: {
      primary: '#111827',
      secondary: '#6b7280',
      disabled: '#d1d5db'
    },
    background: {
      primary: '#ffffff',
      secondary: '#f9fafb',
      disabled: '#f3f4f6'
    },
    error: '#ef4444',
    warning: '#f59e0b',
    success: '#10b981',
    info: '#3b82f6'
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['Fira Code', 'monospace']
    },
    sizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem'
    },
    weights: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    },
    lineHeights: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem'
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px'
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
  },
  breakpoints: {
    mobile: '640px',
    tablet: '768px',
    desktop: '1024px',
    wide: '1280px'
  },
  transitions: {
    fast: '150ms ease-in-out',
    normal: '300ms ease-in-out',
    slow: '500ms ease-in-out'
  },
  zIndex: {
    dropdown: 1000,
    sticky: 1010,
    modal: 1020,
    popover: 1030,
    tooltip: 1040,
    toast: 1050
  }
} as const;

// Type the theme for TypeScript usage
export type Theme = typeof theme;

// Styled component helpers
export const mediaQueries = {
  mobile: `@media (max-width: ${theme.breakpoints.mobile})`,
  tablet: `@media (max-width: ${theme.breakpoints.tablet})`,
  desktop: `@media (min-width: ${theme.breakpoints.desktop})`,
  wide: `@media (min-width: ${theme.breakpoints.wide})`
};
```

### Component Styling Patterns
```typescript
// components/common/Button.tsx
import styled, { css } from 'styled-components';
import { ButtonProps } from '../../types/component';

// Base button styles
const BaseButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.sm};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-family: ${({ theme }) => theme.typography.fontFamily.sans};
  font-weight: ${({ theme }) => theme.typography.weights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};
  cursor: pointer;
  text-decoration: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary[500]};
    outline-offset: 2px;
  }
`;

// Size variants
const sizeStyles = {
  small: css`
    padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
    font-size: ${({ theme }) => theme.typography.sizes.sm};
    min-height: 32px;
  `,
  medium: css`
    padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};
    font-size: ${({ theme }) => theme.typography.sizes.base};
    min-height: 40px;
  `,
  large: css`
    padding: ${({ theme }) => theme.spacing.lg} ${({ theme }) => theme.spacing.xl};
    font-size: ${({ theme }) => theme.typography.sizes.lg};
    min-height: 48px;
  `
};

// Variant styles
const variantStyles = {
  primary: css`
    background-color: ${({ theme }) => theme.colors.primary[600]};
    color: white;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary[700]};
    }
    
    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary[800]};
    }
  `,
  secondary: css`
    background-color: ${({ theme }) => theme.colors.gray[100]};
    color: ${({ theme }) => theme.colors.gray[900]};
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.gray[200]};
    }
    
    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.gray[300]};
    }
  `,
  danger: css`
    background-color: ${({ theme }) => theme.colors.error};
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #dc2626;
    }
    
    &:active:not(:disabled) {
      background-color: #b91c1c;
    }
  `,
  ghost: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.text.primary};
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.gray[100]};
    }
    
    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.gray[200]};
    }
  `
};

// Full width style
const fullWidthStyle = css`
  width: 100%;
`;

// Final styled button with all variants
export const StyledButton = styled(BaseButton)`
  ${({ size = 'medium' }) => sizeStyles[size]}
  ${({ variant = 'primary' }) => variantStyles[variant]}
  ${({ fullWidth }) => fullWidth && fullWidthStyle}
`;

// Button component
export const Button: React.FC<ButtonProps> = ({
  children,
  startIcon,
  endIcon,
  loading,
  disabled,
  testId = 'button',
  ...props
}) => {
  return (
    <StyledButton
      {...props}
      disabled={disabled || loading}
      data-testid={testId}
      aria-busy={loading}
    >
      {loading ? (
        <LoadingSpinner size="small" />
      ) : (
        <>
          {startIcon && <IconWrapper>{startIcon}</IconWrapper>}
          {children}
          {endIcon && <IconWrapper>{endIcon}</IconWrapper>}
        </>
      )}
    </StyledButton>
  );
};

const IconWrapper = styled.span`
  display: flex;
  align-items: center;
`;
```

## Performance and Optimization Standards

### Memoization Patterns
```typescript
// Performance optimization patterns
import React, { memo, useMemo, useCallback, useState } from 'react';

// Memo for expensive components
const ExpensiveComponent = memo<ExpensiveComponentProps>(({ data, onUpdate }) => {
  // Memoize expensive calculations
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      calculated: expensiveCalculation(item)
    }));
  }, [data]);

  // Memoize callback functions
  const handleItemClick = useCallback((id: string) => {
    const item = processedData.find(item => item.id === id);
    if (item && onUpdate) {
      onUpdate(item);
    }
  }, [processedData, onUpdate]);

  return (
    <div>
      {processedData.map(item => (
        <ItemComponent
          key={item.id}
          item={item}
          onClick={handleItemClick}
        />
      ))}
    </div>
  );
});

// Custom comparison function for memo
const DataVisualization = memo<DataVisualizationProps>(
  ({ chartData, settings }) => {
    // Component implementation
  },
  (prevProps, nextProps) => {
    // Custom comparison logic
    return (
      prevProps.chartData.length === nextProps.chartData.length &&
      prevProps.settings.theme === nextProps.settings.theme
    );
  }
);
```

### Code Splitting and Lazy Loading
```typescript
// Lazy loading patterns
import { lazy, Suspense } from 'react';
import { LoadingSpinner } from '../components/common';

// Lazy load heavy components
const HeavyMapComponent = lazy(() => import('../components/maps/HeavyMapComponent'));
const DataVisualization = lazy(() => import('../components/charts/DataVisualization'));

// Component with lazy loading
const Dashboard: React.FC = () => {
  return (
    <div>
      <h1>Dashboard</h1>
      
      <Suspense fallback={<LoadingSpinner />}>
        <HeavyMapComponent />
      </Suspense>
      
      <Suspense fallback={<div>Loading charts...</div>}>
        <DataVisualization />
      </Suspense>
    </div>
  );
};

// Preload components on hover
const PreloadOnHover: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const handleMouseEnter = () => {
    // Preload the component
    import('../components/heavy/HeavyComponent');
  };

  return (
    <div onMouseEnter={handleMouseEnter}>
      {children}
    </div>
  );
};
```

---

**Important**: These coding standards ensure consistency, performance, and maintainability across the FaunaLogic frontend application. All component development should follow these patterns to maintain code quality and user experience.