# Frontend Service System Prompt

**Version**: 1.0.0  
**Last Updated**: 2025-06-27  
**Status**: 🔄 Draft  
**Service**: Frontend Layer

## Service Overview

### Frontend Service Description
The FaunaLogic frontend service provides a modern, responsive web application for visualizing and managing spatial wildlife data. Built with Vue.js and TypeScript, it offers interactive mapping, data visualization, and administrative interfaces for the spatial data management system.

### Service Architecture
- **Framework**: Vue 3 with TypeScript
- **State Management**: Pinia for Vue 3
- **Routing**: Vue Router v4
- **Mapping**: Leaflet with Vue-Leaflet
- **UI Components**: Vuetify 3 with Material Design 3 (Material You)
- **Styling**: CSS Modules or Styled Components for Vue
- **Build Tool**: Vite for fast development and building
- **Testing**: Vitest and Vue Testing Library

## Technology Stack

### Core Dependencies
```json
{
  "dependencies": {
    "vue": "^3.3.0",
    "typescript": "^5.0.0",
    "pinia": "^2.1.0",
    "vue-router": "^4.2.0",
    "leaflet": "^1.9.0",
    "@vue-leaflet/vue-leaflet": "^0.10.0",
    "vuetify": "^3.3.0",
    "axios": "^1.4.0",
    "vee-validate": "^4.10.0",
    "date-fns": "^2.30.0"
  },
  "devDependencies": {
    "@types/leaflet": "^1.9.0",
    "vite": "^4.4.0",
    "@vitejs/plugin-vue": "^4.2.0",
    "@vue/typescript-config": "^1.0.0",
    "vitest": "^0.34.0",
    "@vue/test-utils": "^2.4.0",
    "@testing-library/vue": "^7.0.0",
    "eslint": "^8.45.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@vue/eslint-config-typescript": "^11.0.0",
    "prettier": "^3.0.0"
  }
}
```

## Development Constraints

### Frontend-Specific Requirements
- [ ] All components must be fully responsive (mobile-first design)
- [ ] Spatial data must be visualized using interactive maps
- [ ] Application must work offline with service workers
- [ ] All user interactions must provide appropriate feedback
- [ ] Forms must include real-time validation
- [ ] Navigation must be intuitive and accessible
- [ ] Performance must meet Core Web Vitals standards
- [ ] UI must follow Google Material Design 3 (Material You) guidelines
- [ ] Components must use Vuetify 3's Material Design 3 implementation
- [ ] Color schemes must support dynamic theming and accessibility

### Accessibility Requirements
- [ ] WCAG 2.1 AA compliance for all components
- [ ] Keyboard navigation support throughout the application
- [ ] Screen reader compatibility with ARIA labels
- [ ] High contrast mode support
- [ ] Focus management for complex interactions
- [ ] Alternative text for all images and maps
- [ ] Semantic HTML structure throughout

### Performance Requirements
- [ ] Initial page load under 3 seconds on 3G
- [ ] First Contentful Paint under 1.5 seconds
- [ ] Largest Contentful Paint under 2.5 seconds
- [ ] Interactive maps must be responsive to user input
- [ ] Bundle size optimized with code splitting
- [ ] Images optimized and served in modern formats

## Context Files

### Always Include These Files
When working on frontend tasks, include these files for context:

- `frontend/src/stores/` - Pinia store configuration
- `frontend/src/api/apiClient.ts` - API client configuration
- `frontend/src/types/` - TypeScript type definitions
- `frontend/src/components/` - Vue component library
- `frontend/src/composables/` - Vue composables
- `frontend/src/utils/` - Utility functions

### Project Structure
```
frontend/
├── public/
│   ├── index.html               # Main HTML template
│   ├── manifest.json            # PWA manifest
│   └── icons/                   # App icons and favicons
├── src/
│   ├── components/
│   │   ├── common/              # Reusable UI components
│   │   ├── forms/               # Form components
│   │   ├── maps/                # Mapping components
│   │   ├── layout/              # Layout components
│   │   └── charts/              # Data visualization components
│   ├── views/
│   │   ├── Dashboard/           # Dashboard page
│   │   ├── Submissions/         # Data submission pages
│   │   ├── Users/               # User management pages
│   │   └── Settings/            # Application settings
│   ├── stores/
│   │   ├── index.ts             # Pinia store configuration
│   │   ├── auth.ts              # Authentication state
│   │   ├── user.ts              # User management state
│   │   └── spatial.ts           # Spatial data state
│   ├── api/
│   │   ├── apiClient.ts         # Axios API client
│   │   ├── authApi.ts           # Authentication API calls
│   │   ├── userApi.ts           # User management API
│   │   └── spatialApi.ts        # Spatial data API
│   ├── composables/
│   │   ├── useAuth.ts           # Authentication composable
│   │   ├── useApi.ts            # API calling composable
│   │   └── useMap.ts            # Map interaction composable
│   ├── types/
│   │   ├── auth.ts              # Authentication types
│   │   ├── user.ts              # User types
│   │   ├── spatial.ts           # Spatial data types
│   │   └── api.ts               # API response types
│   ├── utils/
│   │   ├── validation.ts        # Form validation utilities
│   │   ├── formatting.ts        # Data formatting utilities
│   │   ├── spatial.ts           # Spatial data utilities
│   │   └── constants.ts         # Application constants
│   ├── styles/
│   │   ├── main.css             # Global CSS styles
│   │   ├── variables.css        # CSS custom properties
│   │   └── components.css       # Component styles
│   ├── App.vue                  # Main App component
│   ├── main.ts                  # Vue app initialization
│   └── router/
│       └── index.ts             # Vue Router configuration
├── tests/
│   ├── __mocks__/               # Test mocks
│   ├── components/              # Component tests
│   ├── hooks/                   # Hook tests
│   ├── utils/                   # Utility tests
│   └── setup.ts                 # Test setup configuration
├── vite.config.ts               # Vite configuration
├── vitest.config.ts             # Vitest test configuration
├── package.json
├── tsconfig.json
├── Dockerfile                   # Production Docker image
├── Dockerfile.dev               # Development Docker image
├── docker-compose.yml           # Docker Compose configuration
├── nginx.conf                   # Nginx configuration for production
└── .dockerignore               # Docker build context exclusions
```

## Development Guidelines

### Component Development Patterns
```tsx
// Component structure template
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAppSelector, useAppDispatch } from '../hooks/redux';
import { Button, Card, TextField } from '../components/common';
import { UserProfile } from '../types/user';

interface UserProfileCardProps {
  userId: string;
  onUpdate?: (user: UserProfile) => void;
  readonly?: boolean;
}

const UserProfileCard: React.FC<UserProfileCardProps> = ({
  userId,
  onUpdate,
  readonly = false
}) => {
  const dispatch = useAppDispatch();
  const { user, loading, error } = useAppSelector(state => state.users);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (userId) {
      dispatch(fetchUser(userId));
    }
  }, [dispatch, userId]);

  const handleSave = async (userData: Partial<UserProfile>) => {
    try {
      const updatedUser = await dispatch(updateUser({ id: userId, ...userData })).unwrap();
      onUpdate?.(updatedUser);
      setIsEditing(false);
    } catch (error) {
      // Error is handled by the global error handler
    }
  };

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!user) return <NotFoundMessage />;

  return (
    <StyledCard>
      <CardHeader>
        <UserAvatar src={user.avatarUrl} alt={`${user.firstName} ${user.lastName}`} />
        <UserInfo>
          <UserName>{user.firstName} {user.lastName}</UserName>
          <UserEmail>{user.email}</UserEmail>
        </UserInfo>
        {!readonly && (
          <EditButton
            variant="ghost"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? 'Cancel' : 'Edit'}
          </EditButton>
        )}
      </CardHeader>
      
      {isEditing ? (
        <UserEditForm user={user} onSave={handleSave} />
      ) : (
        <UserDetails user={user} />
      )}
    </StyledCard>
  );
};

// Styled components
const StyledCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const UserAvatar = styled.img`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: ${({ theme }) => theme.spacing.md};
`;

export default UserProfileCard;
```

### State Management with Redux Toolkit
```typescript
// store/userSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { userApi } from '../api/userApi';
import { UserProfile, CreateUserRequest, UpdateUserRequest } from '../types/user';

interface UserState {
  users: UserProfile[];
  currentUser: UserProfile | null;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: UserState = {
  users: [],
  currentUser: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  }
};

// Async thunks
export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params: { page?: number; limit?: number; search?: string }) => {
    const response = await userApi.getUsers(params);
    return response.data;
  }
);

export const fetchUser = createAsyncThunk(
  'users/fetchUser',
  async (userId: string) => {
    const response = await userApi.getUserById(userId);
    return response.data;
  }
);

export const createUser = createAsyncThunk(
  'users/createUser',
  async (userData: CreateUserRequest) => {
    const response = await userApi.createUser(userData);
    return response.data;
  }
);

export const updateUser = createAsyncThunk(
  'users/updateUser',
  async ({ id, ...userData }: UpdateUserRequest & { id: string }) => {
    const response = await userApi.updateUser(id, userData);
    return response.data;
  }
);

// Slice
const userSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentUser: (state, action) => {
      state.currentUser = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch users
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.users = action.payload.items;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch users';
      })
      // Fetch single user
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.currentUser = action.payload;
        // Update user in list if it exists
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
      })
      // Create user
      .addCase(createUser.fulfilled, (state, action) => {
        state.users.unshift(action.payload);
        state.pagination.total += 1;
      })
      // Update user
      .addCase(updateUser.fulfilled, (state, action) => {
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
        if (state.currentUser?.id === action.payload.id) {
          state.currentUser = action.payload;
        }
      });
  }
});

export const { clearError, setCurrentUser } = userSlice.actions;
export default userSlice.reducer;
```

### API Client Configuration
```typescript
// api/apiClient.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '../store/store';
import { logout } from '../store/authSlice';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - add auth token
    this.client.interceptors.request.use(
      (config) => {
        const state = store.getState();
        const token = state.auth.accessToken;
        
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor - handle common errors
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors (token expired)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            const state = store.getState();
            const refreshToken = state.auth.refreshToken;
            
            if (refreshToken) {
              // Try to refresh token
              const response = await this.post('/auth/refresh', { refreshToken });
              const { accessToken } = response.data;
              
              // Update token in store
              store.dispatch(setTokens({ accessToken, refreshToken }));
              
              // Retry original request
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, logout user
            store.dispatch(logout());
            window.location.href = '/login';
          }
        }

        // Handle other errors
        if (error.response?.status >= 500) {
          // Show global error notification
          store.dispatch(showNotification({
            type: 'error',
            message: 'Server error occurred. Please try again later.'
          }));
        }

        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get(url, config);
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post(url, data, config);
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put(url, data, config);
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete(url, config);
  }
}

export const apiClient = new ApiClient();
```

## Mapping and Spatial Visualization

### Map Component Implementation
```tsx
// components/maps/InteractiveMap.tsx
import React, { useRef, useEffect, useState } from 'react';
import { MapContainer, TileLayer, GeoJSON, Marker, Popup } from 'react-leaflet';
import { LatLngBounds, Layer } from 'leaflet';
import styled from 'styled-components';
import { SpatialData, MapBounds } from '../../types/spatial';
import { MapControls } from './MapControls';
import { LoadingOverlay } from '../common/LoadingOverlay';

interface InteractiveMapProps {
  spatialData: SpatialData[];
  loading?: boolean;
  onBoundsChange?: (bounds: MapBounds) => void;
  onFeatureClick?: (feature: SpatialData) => void;
  initialBounds?: MapBounds;
  height?: string;
}

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  spatialData,
  loading = false,
  onBoundsChange,
  onFeatureClick,
  initialBounds,
  height = '500px'
}) => {
  const mapRef = useRef<L.Map>(null);
  const [mapBounds, setMapBounds] = useState<LatLngBounds | null>(null);

  useEffect(() => {
    if (mapRef.current && initialBounds) {
      const bounds = new LatLngBounds(
        [initialBounds.south, initialBounds.west],
        [initialBounds.north, initialBounds.east]
      );
      mapRef.current.fitBounds(bounds);
    }
  }, [initialBounds]);

  const handleMoveEnd = () => {
    if (mapRef.current && onBoundsChange) {
      const bounds = mapRef.current.getBounds();
      onBoundsChange({
        north: bounds.getNorth(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        west: bounds.getWest()
      });
    }
  };

  const onEachFeature = (feature: any, layer: Layer) => {
    layer.on('click', () => {
      if (onFeatureClick) {
        onFeatureClick(feature.properties);
      }
    });

    // Add popup with feature info
    if (feature.properties) {
      const popupContent = `
        <div>
          <h4>${feature.properties.name || 'Spatial Feature'}</h4>
          <p>Type: ${feature.properties.type}</p>
          <p>Date: ${new Date(feature.properties.createdAt).toLocaleDateString()}</p>
        </div>
      `;
      layer.bindPopup(popupContent);
    }
  };

  const getFeatureStyle = (feature: any) => {
    return {
      color: feature.properties.color || '#3388ff',
      weight: 2,
      opacity: 0.8,
      fillOpacity: 0.3
    };
  };

  return (
    <MapContainer>
      {loading && <LoadingOverlay />}
      
      <MapContainer
        ref={mapRef}
        center={[39.8283, -98.5795]} // Center of US as default
        zoom={4}
        style={{ height, width: '100%' }}
        onMoveEnd={handleMoveEnd}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {spatialData.map((data) => (
          <GeoJSON
            key={data.id}
            data={data.geometry}
            onEachFeature={onEachFeature}
            style={getFeatureStyle}
          />
        ))}
        
        <MapControls onBoundsChange={onBoundsChange} />
      </MapContainer>
    </MapContainer>
  );
};

const MapContainer = styled.div`
  position: relative;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.md};
`;

export default InteractiveMap;
```

### Custom Hooks for Map Interactions
```typescript
// hooks/useMap.ts
import { useState, useCallback, useRef } from 'react';
import { MapBounds, SpatialData } from '../types/spatial';
import { useAppDispatch } from './redux';
import { fetchSpatialDataInBounds } from '../store/spatialSlice';

export const useMap = () => {
  const dispatch = useAppDispatch();
  const [selectedFeature, setSelectedFeature] = useState<SpatialData | null>(null);
  const [mapLoading, setMapLoading] = useState(false);
  const debounceTimer = useRef<NodeJS.Timeout>();

  const handleBoundsChange = useCallback((bounds: MapBounds) => {
    // Debounce bounds changes to avoid too many API calls
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      setMapLoading(true);
      dispatch(fetchSpatialDataInBounds(bounds))
        .finally(() => setMapLoading(false));
    }, 300);
  }, [dispatch]);

  const handleFeatureClick = useCallback((feature: SpatialData) => {
    setSelectedFeature(feature);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedFeature(null);
  }, []);

  return {
    selectedFeature,
    mapLoading,
    handleBoundsChange,
    handleFeatureClick,
    clearSelection
  };
};
```

## Testing Standards

### Component Testing
```tsx
// tests/components/UserProfileCard.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from 'styled-components';
import UserProfileCard from '../UserProfileCard';
import { theme } from '../../styles/theme';
import userReducer from '../../store/userSlice';

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      users: userReducer
    },
    preloadedState: {
      users: {
        users: [],
        currentUser: null,
        loading: false,
        error: null,
        pagination: { page: 1, limit: 50, total: 0, totalPages: 0 },
        ...initialState
      }
    }
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createTestStore(initialState);
  
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </Provider>
  );
};

describe('UserProfileCard', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    tenantId: 'tenant-123',
    permissions: ['read_users'],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  };

  it('renders user profile information', () => {
    renderWithProviders(
      <UserProfileCard userId="user-123" />,
      { currentUser: mockUser }
    );

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('shows loading state when user is being fetched', () => {
    renderWithProviders(
      <UserProfileCard userId="user-123" />,
      { loading: true }
    );

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('allows editing when not readonly', async () => {
    renderWithProviders(
      <UserProfileCard userId="user-123" />,
      { currentUser: mockUser }
    );

    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    });
  });

  it('does not show edit button when readonly', () => {
    renderWithProviders(
      <UserProfileCard userId="user-123" readonly />,
      { currentUser: mockUser }
    );

    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });
});
```

### Hook Testing
```typescript
// tests/hooks/useAuth.test.ts
import { renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useAuth } from '../useAuth';
import authReducer from '../../store/authSlice';

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer
    },
    preloadedState: {
      auth: {
        isAuthenticated: false,
        user: null,
        accessToken: null,
        refreshToken: null,
        loading: false,
        error: null,
        ...initialState
      }
    }
  });
};

const wrapper = ({ children, initialState = {} }) => (
  <Provider store={createTestStore(initialState)}>
    {children}
  </Provider>
);

describe('useAuth', () => {
  it('returns authentication state', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: (props) => wrapper({ ...props, initialState: { isAuthenticated: true } })
    });

    expect(result.current.isAuthenticated).toBe(true);
  });

  it('provides login function', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.login({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    // Assertions would depend on your auth implementation
  });
});
```

## Performance Optimization

### Code Splitting and Lazy Loading
```tsx
// App.tsx with lazy loading
import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LoadingSpinner } from './components/common/LoadingSpinner';

// Lazy load page components
const Dashboard = React.lazy(() => import('./pages/Dashboard/Dashboard'));
const Submissions = React.lazy(() => import('./pages/Submissions/Submissions'));
const Users = React.lazy(() => import('./pages/Users/<USER>'));
const Settings = React.lazy(() => import('./pages/Settings/Settings'));

function App() {
  return (
    <Router>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/submissions/*" element={<Submissions />} />
          <Route path="/users/*" element={<Users />} />
          <Route path="/settings/*" element={<Settings />} />
        </Routes>
      </Suspense>
    </Router>
  );
}

export default App;
```

### Memoization and Performance Optimization
```tsx
// components/common/DataTable.tsx
import React, { useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (item: T) => void;
  height?: number;
}

const DataTable = React.memo(<T,>({
  data,
  columns,
  onRowClick,
  height = 400
}: DataTableProps<T>) => {
  // Memoize expensive calculations
  const sortedData = useMemo(() => {
    // Sorting logic here
    return data.slice().sort((a, b) => {
      // Sort implementation
    });
  }, [data]);

  const handleRowClick = useCallback((index: number) => {
    if (onRowClick) {
      onRowClick(sortedData[index]);
    }
  }, [sortedData, onRowClick]);

  const Row = useCallback(({ index, style }) => (
    <div
      style={style}
      onClick={() => handleRowClick(index)}
      className="table-row"
    >
      {columns.map(column => (
        <div key={column.key} className="table-cell">
          {column.render(sortedData[index])}
        </div>
      ))}
    </div>
  ), [sortedData, columns, handleRowClick]);

  return (
    <List
      height={height}
      itemCount={sortedData.length}
      itemSize={50}
    >
      {Row}
    </List>
  );
});

export default DataTable;
```

## Docker Deployment

### Production Deployment
The frontend application is containerized using Docker with a multi-stage build process for optimal performance and security.

#### Building and Running
```bash
# Build production image
npm run docker:build

# Run production container
npm run docker:run

# Or use Docker Compose
npm run docker:up
```

#### Production Features
- **Multi-stage build**: Optimized Docker image with minimal attack surface
- **Nginx server**: High-performance static file serving with compression
- **Security headers**: CSP, HSTS, and other security configurations
- **Health checks**: Built-in health monitoring endpoints
- **Non-root user**: Container runs with limited privileges
- **API proxy**: Configured to proxy API requests to backend services

### Development with Docker
```bash
# Build development image
npm run docker:build:dev

# Run development container with hot reload
npm run docker:up:dev

# View logs
npm run docker:logs:dev
```

#### Development Features
- **Hot reload**: Changes reflect immediately during development
- **Volume mounting**: Source code mounted for live editing
- **Signal handling**: Proper process management with dumb-init
- **Development optimizations**: Faster builds and startup times

### Environment Variables
- `VITE_API_BASE_URL`: Backend API endpoint URL
- `NODE_ENV`: Environment mode (development/production)
- `CHOKIDAR_USEPOLLING`: Enable file watching in containers

### Network Configuration
The frontend service connects to the `faunalogic-network` for communication with other services in the stack.

---

**Important**: This frontend service will provide the user interface for the FaunaLogic spatial data management system. All components must be accessible, performant, and provide excellent user experience for spatial data visualization and management tasks.