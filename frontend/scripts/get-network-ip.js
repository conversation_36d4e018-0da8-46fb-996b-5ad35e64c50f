#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to get the local network IP address for mobile device access
 * Run with: node scripts/get-network-ip.js
 */

const os = require('os');

function getNetworkIP() {
  const interfaces = os.networkInterfaces();
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip internal (loopback) and non-IPv4 addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  
  return 'localhost';
}

const networkIP = getNetworkIP();

console.log('\n🌐 Network Configuration for Mobile Device Access');
console.log('================================================');
console.log(`Your machine's network IP: ${networkIP}`);
console.log('');
console.log('📱 To access from mobile devices:');
console.log(`   Frontend: http://${networkIP}:3000`);
console.log(`   API:      http://${networkIP}:8000`);
console.log('');
console.log('⚙️  Update your .env file:');
console.log(`   VITE_API_BASE_URL=http://${networkIP}:8000/api`);
console.log('');
console.log('🔧 Make sure both services are running:');
console.log('   1. API:      make dev (in api/ directory)');
console.log('   2. Frontend: make dev (in frontend/ directory)');
console.log('');
console.log('📋 API should be configured to accept connections from all interfaces:');
console.log('   - Check that API is running with --host 0.0.0.0');
console.log('   - Verify CORS allows your frontend domain');
console.log('');
