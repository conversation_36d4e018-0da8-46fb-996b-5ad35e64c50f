import axios from 'axios'
import type { BoundingBox } from '@/types/map'

const API_BASE = 'http://localhost:8000/api/v1'

export const mapService = {
  async getMapData(bounds: BoundingBox) {
    try {
      const response = await axios.get(`${API_BASE}/map/data`, {
        params: bounds
      })
      
      // Return the GeoJSON FeatureCollection directly
      return response.data?.data || { type: 'FeatureCollection', features: [] }
    } catch (error) {
      console.error('Error fetching map data:', error)
      throw error
    }
  }
}