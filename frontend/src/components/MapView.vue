<template>
  <div class="map-container">
    <l-map
      ref="map"
      :zoom="6"
      :center="center"
      :options="mapOptions"
      @ready="onMapReady"
      style="height: 100%; width: 100%"
    >
      <l-tile-layer
        :url="tileUrl"
        :attribution="attribution"
      />
      
      <l-geo-json
        v-if="geoJsonData"
        :geojson="geoJsonData"
        :options="geoJsonOptions"
      />
    </l-map>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { LMap, LTileLayer, LGeoJson } from '@vue-leaflet/vue-leaflet'
import { mapService } from '@/api/mapService'
import type { BoundingBox } from '@/types/map'

const geoJsonData = ref(null)
const center = ref<[number, number]>([54.15, -126.5])

const tileUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
const attribution = '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'

const mapOptions = {
  zoomSnap: 0.5,
  zoomDelta: 0.5,
  wheelPxPerZoomLevel: 120
}

const geoJsonOptions = {
  onEachFeature: (feature: any, layer: any) => {
    // Create popup content from feature properties
    const props = feature.properties
    const coords = feature.geometry.coordinates
    
    // Parse spatial_component if it exists and is a string
    let spatialData = null
    if (props.spatial_component) {
      try {
        spatialData = typeof props.spatial_component === 'string' 
          ? JSON.parse(props.spatial_component) 
          : props.spatial_component
      } catch (e) {
        console.warn('Failed to parse spatial_component:', e)
      }
    }
    
    // Create sections for different data sources
    let popupContent = `<div style="max-width: 300px; max-height: 400px; overflow-y: auto;">
      <h3 style="margin-top: 0; color: #2c3e50;">Observation ${props.id}</h3>
      
      <div style="margin-bottom: 15px;">
        <h4 style="margin: 10px 0 5px 0; color: #34495e; border-bottom: 1px solid #bdc3c7;">Location</h4>
        <p style="margin: 5px 0;"><strong>Coordinates:</strong> ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}</p>
      </div>
      
      <div style="margin-bottom: 15px;">
        <h4 style="margin: 10px 0 5px 0; color: #34495e; border-bottom: 1px solid #bdc3c7;">Basic Properties</h4>`
    
    // Add all basic properties (excluding spatial_component for now)
    Object.entries(props).forEach(([key, value]: [string, any]) => {
      if (key !== 'spatial_component' && value !== null && value !== undefined) {
        let displayValue = value
        
        // Format dates
        if (key.includes('date') && typeof value === 'string') {
          try {
            displayValue = new Date(value).toLocaleString()
          } catch (e) {
            displayValue = value
          }
        }
        
        // Format long strings
        if (typeof displayValue === 'string' && displayValue.length > 50) {
          displayValue = displayValue.substring(0, 50) + '...'
        }
        
        popupContent += `<p style="margin: 5px 0;"><strong>${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</strong> ${displayValue}</p>`
      }
    })
    
    popupContent += `</div>`
    
    // Add spatial component data if available
    if (spatialData?.properties) {
      popupContent += `
        <div style="margin-bottom: 15px;">
          <h4 style="margin: 10px 0 5px 0; color: #34495e; border-bottom: 1px solid #bdc3c7;">Observation Details</h4>`
      
      Object.entries(spatialData.properties).forEach(([key, value]: [string, any]) => {
        if (value !== null && value !== undefined) {
          let displayValue = value
          
          // Handle arrays (like measurements)
          if (Array.isArray(value)) {
            if (key === 'measurements' && value.length > 0) {
              displayValue = value.map((m: any) => 
                `${m.measurementType}: ${m.measurementValue} ${m.measurementUnit || ''}`
              ).join('<br/>')
            } else {
              displayValue = value.join(', ')
            }
          }
          
          // Format dates
          if (key.includes('Date') && typeof value === 'string') {
            try {
              displayValue = new Date(value).toLocaleDateString()
            } catch (e) {
              displayValue = value
            }
          }
          
          popupContent += `<p style="margin: 5px 0;"><strong>${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong> ${displayValue}</p>`
        }
      })
      
      popupContent += `</div>`
    }
    
    popupContent += `</div>`
    
    layer.bindPopup(popupContent, {
      maxWidth: 350,
      maxHeight: 500
    })
  }
}

const onMapReady = () => {
  console.log('Map is ready')
}

const loadMapData = async () => {
  try {
    const bcBounds: BoundingBox = {
      min_lng: -139,
      min_lat: 48.3,
      max_lng: -114,
      max_lat: 60,
      limit: 100
    }
    
    geoJsonData.value = await mapService.getMapData(bcBounds)
    console.log('Loaded GeoJSON data:', geoJsonData.value?.features?.length || 0, 'features')
  } catch (error) {
    console.error('Failed to load map data:', error)
  }
}

onMounted(() => {
  loadMapData()
})
</script>

<style scoped>
.map-container {
  height: 100%;
  width: 100%;
}
</style>