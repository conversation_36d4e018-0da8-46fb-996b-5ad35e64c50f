<template>
  <div class="map-container">
    <div ref="mapContainer" class="map"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import maplibregl from 'maplibre-gl'
import { mapService } from '@/api/mapService'
import type { BoundingBox } from '@/types/map'

const mapContainer = ref<HTMLElement>()
let map: maplibregl.Map | null = null

const initMap = () => {
  if (!mapContainer.value) return

  map = new maplibregl.Map({
    container: mapContainer.value,
    style: {
      version: 8,
      sources: {
        'osm-tiles': {
          type: 'raster',
          tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
          tileSize: 256,
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }
      },
      layers: [
        {
          id: 'osm-tiles',
          type: 'raster',
          source: 'osm-tiles'
        }
      ]
    },
    center: [-126.5, 54.15], // British Columbia center
    zoom: 6
  })

  map.on('load', () => {
    console.log('Map loaded')
    loadMapData()
  })
}

const loadMapData = async () => {
  if (!map) return

  try {
    const bcBounds: BoundingBox = {
      min_lng: -139,
      min_lat: 48.3,
      max_lng: -114,
      max_lat: 60,
      limit: 100
    }

    const geoJsonData = await mapService.getMapData(bcBounds)
    console.log('Loaded GeoJSON data:', geoJsonData?.features?.length || 0, 'features')

    if (geoJsonData?.features) {
      // Remove existing data source and all layers if they exist
      if (map!.getSource('features')) {
        // Remove all potential layers
        const layersToRemove = [
          'polygon-fills', 'polygon-strokes', 'linestring-lines', 
          'point-circles', 'point-symbols', 'point-labels'
        ]
        layersToRemove.forEach(layerId => {
          if (map!.getLayer(layerId)) {
            map!.removeLayer(layerId)
          }
        })
        map!.removeSource('features')
      }

      // Add single GeoJSON source for all features
      map!.addSource('features', {
        type: 'geojson',
        data: geoJsonData
      })

      // Polygon fill layer
      map!.addLayer({
        id: 'polygon-fills',
        type: 'fill',
        source: 'features',
        filter: ['==', '$type', 'Polygon'],
        paint: {
          'fill-color': [
            'case',
            ['has', 'fillColor'],
            ['get', 'fillColor'],
            '#3388ff'
          ],
          'fill-opacity': [
            'case',
            ['has', 'fillOpacity'],
            ['get', 'fillOpacity'],
            0.6
          ]
        }
      })

      // Polygon stroke layer
      map!.addLayer({
        id: 'polygon-strokes',
        type: 'line',
        source: 'features',
        filter: ['==', '$type', 'Polygon'],
        paint: {
          'line-color': [
            'case',
            ['has', 'strokeColor'],
            ['get', 'strokeColor'],
            '#0066cc'
          ],
          'line-width': [
            'case',
            ['has', 'strokeWidth'],
            ['get', 'strokeWidth'],
            2
          ],
          'line-opacity': [
            'case',
            ['has', 'strokeOpacity'],
            ['get', 'strokeOpacity'],
            1
          ]
        }
      })

      // LineString layer
      map!.addLayer({
        id: 'linestring-lines',
        type: 'line',
        source: 'features',
        filter: ['==', '$type', 'LineString'],
        paint: {
          'line-color': [
            'case',
            ['has', 'color'],
            ['get', 'color'],
            '#0066cc'
          ],
          'line-width': [
            'case',
            ['has', 'weight'],
            ['get', 'weight'],
            3
          ],
          'line-opacity': [
            'case',
            ['has', 'opacity'],
            ['get', 'opacity'],
            1
          ]
        }
      })

      // Point circles layer (for markerType: 'circle' or default)
      map!.addLayer({
        id: 'point-circles',
        type: 'circle',
        source: 'features',
        filter: [
          'all',
          ['==', '$type', 'Point'],
          [
            'any',
            ['==', ['get', 'markerType'], 'circle'],
            ['!', ['has', 'markerType']] // default to circles if no markerType specified
          ]
        ],
        paint: {
          'circle-radius': [
            'case',
            ['has', 'radius'],
            ['get', 'radius'],
            15
          ],
          'circle-color': [
            'case',
            ['has', 'fillColor'],
            ['get', 'fillColor'],
            '#3388ff'
          ],
          'circle-stroke-color': [
            'case',
            ['has', 'strokeColor'],
            ['get', 'strokeColor'],
            '#0066cc'
          ],
          'circle-stroke-width': [
            'case',
            ['has', 'strokeWidth'],
            ['get', 'strokeWidth'],
            2
          ],
          'circle-opacity': [
            'case',
            ['has', 'fillOpacity'],
            ['get', 'fillOpacity'],
            0.8
          ],
          'circle-stroke-opacity': [
            'case',
            ['has', 'strokeOpacity'],
            ['get', 'strokeOpacity'],
            1
          ]
        }
      })

      // Point symbols layer (for markerType: 'pin' or 'icon')
      map!.addLayer({
        id: 'point-symbols',
        type: 'symbol',
        source: 'features',
        filter: [
          'all',
          ['==', '$type', 'Point'],
          [
            'any',
            ['==', ['get', 'markerType'], 'pin'],
            ['==', ['get', 'markerType'], 'icon']
          ]
        ],
        layout: {
          'icon-image': [
            'case',
            ['has', 'iconImage'],
            ['get', 'iconImage'],
            'marker-15' // default icon
          ],
          'icon-size': [
            'case',
            ['has', 'iconSize'],
            ['get', 'iconSize'],
            1.5
          ],
          'icon-anchor': [
            'case',
            ['has', 'iconAnchor'],
            ['get', 'iconAnchor'],
            'bottom'
          ]
        },
        paint: {
          'icon-opacity': [
            'case',
            ['has', 'iconOpacity'],
            ['get', 'iconOpacity'],
            1
          ]
        }
      })

      // Point labels layer (for text on points)
      map!.addLayer({
        id: 'point-labels',
        type: 'symbol',
        source: 'features',
        filter: [
          'all',
          ['==', '$type', 'Point'],
          ['has', 'text']
        ],
        layout: {
          'text-field': ['get', 'text'],
          'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
          'text-size': [
            'case',
            ['has', 'textSize'],
            ['get', 'textSize'],
            12
          ],
          'text-anchor': [
            'case',
            ['has', 'textAnchor'],
            ['get', 'textAnchor'],
            'center'
          ],
          'text-offset': [
            'case',
            ['has', 'textOffset'],
            ['get', 'textOffset'],
            [0, 0]
          ],
          'text-allow-overlap': true,
          'text-ignore-placement': true
        },
        paint: {
          'text-color': [
            'case',
            ['has', 'textColor'],
            ['get', 'textColor'],
            'white'
          ],
          'text-halo-color': [
            'case',
            ['has', 'textHaloColor'],
            ['get', 'textHaloColor'],
            'rgba(0, 0, 0, 0.8)'
          ],
          'text-halo-width': [
            'case',
            ['has', 'textHaloWidth'],
            ['get', 'textHaloWidth'],
            1
          ],
          'text-opacity': [
            'case',
            ['has', 'textOpacity'],
            ['get', 'textOpacity'],
            1
          ]
        }
      })

      // Add click handlers for all interactive layers
      const interactiveLayers = ['polygon-fills', 'linestring-lines', 'point-circles', 'point-symbols']
      
      interactiveLayers.forEach(layerId => {
        map!.on('click', layerId, (e) => {
          if (e.features && e.features.length > 0) {
            const feature = e.features[0]
            const properties = feature.properties
            const spatialProps = properties?.spatial_component?.properties || properties
            const coordinates = getFeatureCoordinates(feature)

            // Create popup content using all available data
            const popupContent = createPopupContent(properties, spatialProps, coordinates)

            // Create and show popup
            new maplibregl.Popup({
              offset: 25,
              closeButton: true,
              closeOnClick: true
            })
              .setLngLat(coordinates)
              .setHTML(popupContent)
              .addTo(map!)
          }
        })

        // Change cursor on hover
        map!.on('mouseenter', layerId, () => {
          map!.getCanvas().style.cursor = 'pointer'
        })

        map!.on('mouseleave', layerId, () => {
          map!.getCanvas().style.cursor = ''
        })
      })
    }
  } catch (error) {
    console.error('Failed to load map data:', error)
  }
}

// Helper function to get coordinates from different geometry types
const getFeatureCoordinates = (feature: any): [number, number] => {
  const geometry = feature.geometry
  switch (geometry.type) {
    case 'Point':
      return geometry.coordinates
    case 'Polygon':
      // Return centroid of first ring
      const ring = geometry.coordinates[0]
      const centroid = ring.reduce((acc: [number, number], coord: [number, number]) => {
        return [acc[0] + coord[0], acc[1] + coord[1]]
      }, [0, 0])
      return [centroid[0] / ring.length, centroid[1] / ring.length]
    case 'LineString':
      // Return midpoint of line
      const coords = geometry.coordinates
      const midIndex = Math.floor(coords.length / 2)
      return coords[midIndex]
    default:
      return [0, 0]
  }
}

// Helper function to create popup content
const createPopupContent = (properties: any, spatialProps: any, coordinates: [number, number]): string => {
  const featureType = properties?.$type || 'Feature'
  const markerType = properties?.markerType || 'default'
  
  return `
    <div class="popup-content">
      <h3>${featureType} ${properties?.id || 'Unknown'}</h3>
      <p><strong>Type:</strong> ${featureType}${markerType !== 'default' ? ` (${markerType})` : ''}</p>
      ${spatialProps?.individualCount ? `<p><strong>Individual Count:</strong> ${spatialProps.individualCount}</p>` : ''}
      ${spatialProps?.scientificName ? `<p><strong>Species:</strong> ${spatialProps.scientificName}</p>` : ''}
      ${spatialProps?.habitat ? `<p><strong>Habitat:</strong> ${spatialProps.habitat}</p>` : ''}
      ${spatialProps?.eventDate ? `<p><strong>Date:</strong> ${spatialProps.eventDate}</p>` : ''}
      ${spatialProps?.sex ? `<p><strong>Sex:</strong> ${spatialProps.sex}</p>` : ''}
      ${spatialProps?.eventID ? `<p><strong>Event ID:</strong> ${spatialProps.eventID}</p>` : ''}
      <p><strong>Coordinates:</strong> ${coordinates[1]?.toFixed(4)}, ${coordinates[0]?.toFixed(4)}</p>
      ${properties?.submission_observation_id ? `<p><strong>Submission ID:</strong> ${properties.submission_observation_id}</p>` : ''}
      ${spatialProps?.measurements ? `
        <div><strong>Measurements:</strong>
          ${spatialProps.measurements.map((m: any) => 
            `<br>&nbsp;&nbsp;${m.measurementType}: ${m.measurementValue} ${m.measurementUnit}`
          ).join('')}
        </div>
      ` : ''}
      ${properties?.description ? `<p><strong>Description:</strong> ${properties.description}</p>` : ''}
    </div>
  `
}

onMounted(() => {
  initMap()
})

onUnmounted(() => {
  if (map) {
    map.remove()
    map = null
  }
})
</script>

<style scoped>
.map-container {
  height: 100%;
  width: 100%;
  position: relative;
}

.map {
  height: 100%;
  width: 100%;
}
</style>

<style>
/* Import MapLibre GL CSS */
@import 'maplibre-gl/dist/maplibre-gl.css';

/* Removed custom marker styles - now using data-driven MapLibre GL styling */

/* Popup styles */
.popup-content {
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  max-width: 250px;
}

.popup-content h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.popup-content p {
  margin: 4px 0;
  color: #666;
}

.popup-content strong {
  color: #333;
}

/* MapLibre GL popup customization */
.maplibregl-popup-content {
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.maplibregl-popup-close-button {
  font-size: 18px;
  padding: 5px;
  color: #999;
}

.maplibregl-popup-close-button:hover {
  color: #333;
}
</style>