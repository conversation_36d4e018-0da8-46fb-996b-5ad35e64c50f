apiVersion: template.openshift.io/v1
kind: Template
labels:
  template: allow-same-namespace
metadata:
  name: allow-same-namespace
  annotations:
    description: >-
      This Template creates several network policy items. Each one grants all resources the ability to communicate with 
      all other resources in the namespace. This is the simplest way to blanket ensure all pods ar able to communicate 
      with all other pods, without having to specify a permission for each pair of pods that should be allowed to 
      communicate.
parameters:
  - name: NAMESPACE
    description: The OpenShift Namespace license plate
    value: "a0ec71"
objects:
  ## Dev
  - kind: NetworkPolicy
    apiVersion: networking.k8s.io/v1
    metadata:
      name: allow-same-namespace
      namespace: "${NAMESPACE}-dev"
    spec:
      podSelector: {}
      ingress:
        - from:
            - podSelector: {}
      policyTypes:
        - Ingress
  ## Test
  - kind: NetworkPolicy
    apiVersion: networking.k8s.io/v1
    metadata:
      name: allow-same-namespace
      namespace: "${NAMESPACE}-test"
    spec:
      podSelector: {}
      ingress:
        - from:
            - podSelector: {}
      policyTypes:
        - Ingress
  ## Prod
  - kind: NetworkPolicy
    apiVersion: networking.k8s.io/v1
    metadata:
      name: allow-same-namespace
      namespace: "${NAMESPACE}-prod"
    spec:
      podSelector: {}
      ingress:
        - from:
            - podSelector: {}
      policyTypes:
        - Ingress
  ## Tools
  - kind: NetworkPolicy
    apiVersion: networking.k8s.io/v1
    metadata:
      name: allow-same-namespace
      namespace: "${NAMESPACE}-tools"
    spec:
      podSelector: {}
      ingress:
        - from:
            - podSelector: {}
      policyTypes:
        - Ingress
