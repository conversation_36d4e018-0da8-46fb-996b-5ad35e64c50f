apiVersion: template.openshift.io/v1
kind: Template
labels:
  template: app-to-api-to-db
metadata:
  name: app-to-api-to-db
  annotations:
    description: >-
      This Template creates various network policy items. Each one enables communication between two OpenShift resources.
      For example: `api-to-db` allows the api pods to communicate with the database pods. The pods are identified based 
      on a selector, in this case the `role` field.
parameters:
  - description: The OpenShift Namespace license plate
    displayName: Namespace
    name: NAMESPACE
    value: "a0ec71"
objects:
  ## Dev
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: api-to-db
      namespace: "${NAMESPACE}-dev"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: api
          ports:
            - port: 5432
              protocol: TCP
      podSelector:
        matchLabels:
          role: db
      policyTypes:
        - Ingress
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: queue-to-db
      namespace: "${NAMESPACE}-dev"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: queue
          ports:
            - port: 5432
              protocol: TCP
      podSelector:
        matchLabels:
          role: db
      policyTypes:
        - Ingress
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: app-to-api
      namespace: "${NAMESPACE}-dev"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: app
          ports:
            - port: 6100
              protocol: TCP
      podSelector:
        matchLabels:
          role: api
      policyTypes:
        - Ingress

  ## Test
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: api-to-db
      namespace: "${NAMESPACE}-test"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: api
          ports:
            - port: 5432
              protocol: TCP
      podSelector:
        matchLabels:
          role: db
      policyTypes:
        - Ingress
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: queue-to-db
      namespace: "${NAMESPACE}-dev"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: queue
          ports:
            - port: 5432
              protocol: TCP
      podSelector:
        matchLabels:
          role: db
      policyTypes:
        - Ingress
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: app-to-api
      namespace: "${NAMESPACE}-test"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: app
          ports:
            - port: 6100
              protocol: TCP
      podSelector:
        matchLabels:
          role: api
      policyTypes:
        - Ingress
  ## Prod
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: api-to-db
      namespace: "${NAMESPACE}-prod"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: api
          ports:
            - port: 5432
              protocol: TCP
      podSelector:
        matchLabels:
          role: db
      policyTypes:
        - Ingress
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: queue-to-db
      namespace: "${NAMESPACE}-dev"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: queue
          ports:
            - port: 5432
              protocol: TCP
      podSelector:
        matchLabels:
          role: db
      policyTypes:
        - Ingress
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: app-to-api
      namespace: "${NAMESPACE}-prod"
    spec:
      ingress:
        - from:
            - podSelector:
                matchLabels:
                  role: app
          ports:
            - port: 6100
              protocol: TCP
      podSelector:
        matchLabels:
          role: api
      policyTypes:
        - Ingress
  ## Setup
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: setup-to-db
      namespace: "${NAMESPACE}-dev"
    spec:
      podSelector:
        matchLabels:
          role: db
      ingress:
        - ports:
            - protocol: TCP
              port: 5432
          from:
            - podSelector:
                matchLabels:
                  role: setup
      policyTypes:
        - Ingress

  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: setup-to-db
      namespace: "${NAMESPACE}-test"
    spec:
      podSelector:
        matchLabels:
          role: db
      ingress:
        - ports:
            - protocol: TCP
              port: 5432
          from:
            - podSelector:
                matchLabels:
                  role: setup
      policyTypes:
        - Ingress
  - apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: setup-to-db
      namespace: "${NAMESPACE}-prod"
    spec:
      podSelector:
        matchLabels:
          role: db
      ingress:
        - ports:
            - protocol: TCP
              port: 5432
          from:
            - podSelector:
                matchLabels:
                  role: setup
      policyTypes:
        - Ingress
