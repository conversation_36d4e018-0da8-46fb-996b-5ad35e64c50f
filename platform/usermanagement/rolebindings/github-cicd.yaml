apiVersion: template.openshift.io/v1
kind: Template
labels:
  template: github-cicd
metadata:
  name: github-cicd
parameters:
  - description: The OpenShift Namespace license plate
    displayName: Namespace
    name: NAMESPACE
    value: "a0ec71"
objects:
  - apiVersion: v1
    kind: ServiceAccount
    metadata:
      name: github-cicd
      namespace: "${NAMESPACE}-tools"
  - apiVersion: rbac.authorization.k8s.io/v1
    kind: RoleBinding
    metadata:
      name: githib-cicd
      namespace: "${NAMESPACE}-dev"
    subjects:
      - kind: ServiceAccount
        name: github-cicd
        namespace: "${NAMESPACE}-tools"
    roleRef:
      apiGroup: rbac.authorization.k8s.io
      kind: ClusterRole
      name: edit
  - apiVersion: rbac.authorization.k8s.io/v1
    kind: RoleBinding
    metadata:
      name: githib-cicd
      namespace: "${NAMESPACE}-test"
    subjects:
      - kind: ServiceAccount
        name: github-cicd
        namespace: "${NAMESPACE}-tools"
    roleRef:
      apiGroup: rbac.authorization.k8s.io
      kind: ClusterRole
      name: edit
  - apiVersion: rbac.authorization.k8s.io/v1
    kind: RoleBinding
    metadata:
      name: githib-cicd
      namespace: "${NAMESPACE}-prod"
    subjects:
      - kind: ServiceAccount
        name: github-cicd
        namespace: "${NAMESPACE}-tools"
    roleRef:
      apiGroup: rbac.authorization.k8s.io
      kind: ClusterRole
      name: edit
  - apiVersion: rbac.authorization.k8s.io/v1
    kind: RoleBinding
    metadata:
      name: githib-cicd
      namespace: "${NAMESPACE}-tools"
    subjects:
      - kind: ServiceAccount
        name: github-cicd
        namespace: "${NAMESPACE}-tools"
    roleRef:
      apiGroup: rbac.authorization.k8s.io
      kind: ClusterRole
      name: edit
