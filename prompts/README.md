# FaunaLogic Monorepo Prompts Directory

This directory contains structured prompts for Claude <PERSON> to help maintain consistency and effectiveness across the FaunaLogic monorepo development tasks.

## Monorepo Structure Overview

FaunaLogic is organized as a monorepo with service-specific prompt directories:

```
prompts/                              # Global/shared prompts
├── system/                          # Project-wide system prompts
├── features/                        # Cross-service feature specifications
├── tasks/                           # Project-level tasks
└── templates/                       # Prompt templates

database/prompts/                     # Database service prompts
├── system/                          # Database-specific system configuration
├── features/                        # Database feature specifications
├── tasks/                           # Database development tasks
└── templates/                       # Database-specific templates

api/prompts/                         # API service prompts
├── system/                          # API service system configuration
├── features/                        # API feature specifications
├── tasks/                           # API development tasks
└── templates/                       # API-specific templates

frontend/prompts/                    # Frontend service prompts
├── system/                          # Frontend system configuration
├── features/                        # Frontend feature specifications
├── tasks/                           # Frontend development tasks
└── templates/                       # Frontend-specific templates
```

## Global Prompts (`prompts/`)

### 📁 System Prompts (`system/`)
Core project configuration and standards that apply across all services.

| File | Status | Description |
|------|--------|-------------|
| `main-system.md` | ✅ Active | Project overview, architecture guidelines, and development constraints |
| `coding-standards.md` | ✅ Active | Code style preferences, naming conventions, and file structure expectations |

### 📁 Feature Prompts (`features/`)
Cross-service feature requirements and implementation guidelines.

| File | Status | Description |
|------|--------|-------------|
| `authentication.md` | 🔄 Draft | User authentication system requirements and constraints |
| `data-processing.md` | 🔄 Draft | Data flow requirements and processing rules |
| `ui-components.md` | 🔄 Draft | Component architecture and styling guidelines |

### 📁 Task Prompts (`tasks/`)
Project-level development tasks with clear objectives and implementation steps.

| File | Status | Description |
|------|--------|-------------|
| `refactor-auth.md` | 📋 Pending | Authentication system refactoring objectives |
| `add-logging.md` | 📋 Pending | Logging strategy and implementation approach |
| `optimize-queries.md` | 📋 Pending | Database query optimization targets |

### 📁 Templates (`templates/`)
Standardized formats for creating new prompts and maintaining consistency.

| File | Description |
|------|-------------|
| `feature-template.md` | Template for new feature development prompts |
| `bug-fix-template.md` | Template for bug fix and issue resolution tasks |
| `task-template.md` | General template for development tasks |

## Service-Specific Prompts

### Database Service (`database/prompts/`)
**Technology Stack**: PostgreSQL with PostGIS, spatial data management, security transformations

| Directory | Status | Purpose |
|-----------|---------|---------|
| `system/` | ✅ Active | Database-specific system configuration and standards |
| `features/` | 📋 Pending | Database feature specifications |
| `tasks/` | 📋 Pending | Database development and optimization tasks |
| `templates/` | 📋 Pending | Database-specific prompt templates |

**Key Features**: Spatial data storage, multi-tenant architecture, security transforms, audit logging

### API Service (`api/prompts/`)
**Technology Stack**: Node.js/TypeScript, Express.js, JWT authentication, spatial processing

| Directory | Status | Purpose |
|-----------|---------|---------|
| `system/` | ✅ Active | API service system configuration and standards |
| `features/` | 📋 Pending | API feature specifications |
| `tasks/` | 📋 Pending | API development and optimization tasks |
| `templates/` | 📋 Pending | API-specific prompt templates |

**Key Features**: RESTful endpoints, spatial data processing, authentication/authorization, rate limiting

### Frontend Service (`frontend/prompts/`)
**Technology Stack**: React/TypeScript, Redux Toolkit, Leaflet mapping, responsive design

| Directory | Status | Purpose |
|-----------|---------|---------|
| `system/` | ✅ Active | Frontend system configuration and standards |
| `features/` | 📋 Pending | Frontend feature specifications |
| `tasks/` | 📋 Pending | Frontend development and optimization tasks |
| `templates/` | 📋 Pending | Frontend-specific prompt templates |

**Key Features**: Interactive mapping, data visualization, responsive UI, accessibility compliance

## Usage Guidelines

### Using Prompts with Claude Code in Monorepo

#### Service-Specific Development
When working on a specific service, always include the service-specific system prompt:

```bash
# Database development
claude-code --include database/prompts/system/main-system.md \
           --include database/prompts/system/coding-standards.md \
           "Add spatial index optimization"

# API development  
claude-code --include api/prompts/system/main-system.md \
           --include api/prompts/system/coding-standards.md \
           "Implement user authentication endpoints"

# Frontend development
claude-code --include frontend/prompts/system/main-system.md \
           --include frontend/prompts/system/coding-standards.md \
           "Create interactive map component"
```

#### Cross-Service Development
For features spanning multiple services, include relevant prompts from multiple services:

```bash
# Authentication feature across services
claude-code --include prompts/features/authentication.md \
           --include database/prompts/system/main-system.md \
           --include api/prompts/system/main-system.md \
           --include frontend/prompts/system/main-system.md \
           "Implement end-to-end authentication system"
```

#### Project-Level Tasks
For project-wide tasks, use global prompts:

```bash
# Project-wide refactoring
claude-code --include prompts/system/main-system.md \
           --include prompts/tasks/refactor-auth.md \
           "Refactor authentication across all services"
```

### Maintenance Guidelines

#### Service-Specific Maintenance
- **Database**: Review prompts when schema changes or performance requirements evolve
- **API**: Update prompts when adding new endpoints or changing authentication
- **Frontend**: Refresh prompts when UI/UX requirements or accessibility standards change

#### Cross-Service Coordination
- **Feature Prompts**: Update when requirements span multiple services
- **Global Standards**: Maintain consistency across all service-specific prompts
- **Integration Points**: Keep service interaction patterns up-to-date

### Status Legend

| Symbol | Status | Description |
|--------|--------|-------------|
| ✅ | Active | Currently in use and up-to-date |
| 🔄 | Draft | Under development or review |
| 📋 | Pending | Planned but not yet started |
| ⚠️ | Review | Needs updating or verification |
| 🚫 | Deprecated | No longer in use |

## Best Practices for Monorepo

### When Creating New Prompts
1. **Choose the Right Location**: 
   - Service-specific: Use `{service}/prompts/`
   - Cross-service: Use global `prompts/`
   - Shared patterns: Consider both locations with cross-references

2. **Service Integration**: 
   - Reference integration points with other services
   - Include API contracts and data flow considerations
   - Document service dependencies

3. **Consistency Across Services**:
   - Follow the same template structure in all services
   - Maintain consistent naming conventions
   - Use similar success criteria formats

### When Working Across Services
1. **Dependency Management**: Understand service dependencies before making changes
2. **API Contracts**: Ensure changes don't break service interfaces
3. **Data Flow**: Consider how changes affect data flow between services
4. **Testing Strategy**: Plan integration testing across service boundaries

### Service-Specific Considerations

#### Database Service
- Always consider spatial data implications
- Plan for multi-tenant data isolation
- Include migration and rollback strategies
- Consider performance impact on large datasets

#### API Service  
- Maintain backward compatibility in API changes
- Plan for proper error handling and validation
- Consider rate limiting and security implications
- Document API contract changes

#### Frontend Service
- Plan for responsive design across devices
- Consider accessibility in all UI changes
- Plan for offline functionality where applicable
- Test across different browsers and screen sizes

## Integration with Claude Code

### Service-Specific Commands
```bash
# Database-focused development
claude-code --include database/prompts/system/ \
           --include prompts/templates/task-template.md \
           "Optimize spatial query performance"

# API-focused development
claude-code --include api/prompts/system/ \
           --include prompts/features/authentication.md \
           "Add JWT refresh token functionality"

# Frontend-focused development  
claude-code --include frontend/prompts/system/ \
           --include prompts/features/ui-components.md \
           "Create responsive data table component"
```

### Multi-Service Commands
```bash
# Full-stack feature development
claude-code --include prompts/features/data-processing.md \
           --include database/prompts/system/main-system.md \
           --include api/prompts/system/main-system.md \
           --include frontend/prompts/system/main-system.md \
           "Implement real-time spatial data processing pipeline"
```

## Contributing

When adding new prompts:
1. Use the appropriate template
2. Follow the established naming conventions
3. Update this README with the new prompt information
4. Test the prompt with Claude Code before merging

---

**Last Updated**: 2025-06-27  
**Version**: 1.0.0