# Coding Standards

**Version**: 1.0.0  
**Last Updated**: 2025-06-27  
**Status**: ✅ Active

## Code Style Preferences

### General Principles
- **Clarity over Cleverness**: Write code that is easy to read and understand
- **Consistency**: Follow established patterns throughout the codebase
- **Simplicity**: Prefer simple solutions over complex ones
- **Testability**: Write code that is easy to test and debug

### TypeScript/JavaScript Standards

#### Variable Declarations
```typescript
// Prefer const over let, let over var
const userConfig = { name: '<PERSON>', age: 30 };
let counter = 0;

// Use descriptive names
const isUserLoggedIn = checkAuthStatus();
const userAccountBalance = calculateBalance(userId);

// Avoid abbreviations unless commonly understood
const httpClient = new HttpClient(); // ✅
const hc = new HttpClient(); // ❌
```

#### Function Definitions
```typescript
// Use async/await for asynchronous operations
async function fetchUserData(userId: string): Promise<UserData> {
  try {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    logger.error('Failed to fetch user data', { userId, error });
    throw new Error('User data retrieval failed');
  }
}

// Use arrow functions for short callbacks
const activeUsers = users.filter(user => user.isActive);
const userNames = users.map(user => user.name);

// Use function declarations for main functions
function calculateTotalRevenue(orders: Order[]): number {
  return orders.reduce((total, order) => total + order.amount, 0);
}
```

#### Type Definitions
```typescript
// Use interfaces for object shapes
interface UserProfile {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  preferences: UserPreferences;
}

// Use type aliases for unions and primitives
type Status = 'pending' | 'approved' | 'rejected';
type UserId = string;

// Use generics for reusable types
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

## Naming Conventions

### File and Directory Names
```
// Files: kebab-case with descriptive names
user-service.ts
email-validator.ts
payment-processor.ts

// Directories: kebab-case, grouped by feature or type
src/
├── user-management/
├── payment-processing/
├── shared-components/
└── api-clients/

// Component files: PascalCase matching component name
UserProfile.tsx
PaymentForm.tsx
NavigationMenu.tsx
```

### Code Identifiers
```typescript
// Constants: SCREAMING_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3;
const API_BASE_URL = 'https://api.example.com';
const DEFAULT_TIMEOUT_MS = 5000;

// Functions and variables: camelCase
const calculateUserScore = (user: User) => { ... };
const isEmailValid = validateEmail(email);
const userAccountBalance = getUserBalance(userId);

// Classes: PascalCase
class UserAccountManager {
  private accountBalance: number;
  
  public updateBalance(amount: number): void { ... }
}

// Interfaces and Types: PascalCase with descriptive names
interface DatabaseConnection {
  host: string;
  port: number;
  database: string;
}

type PaymentMethod = 'credit_card' | 'paypal' | 'bank_transfer';
```

### Boolean Variables
```typescript
// Use is/has/can/should prefixes for boolean variables
const isUserLoggedIn = checkAuthStatus();
const hasPermission = user.permissions.includes('admin');
const canUserEdit = checkEditPermissions(user, document);
const shouldShowNotification = user.preferences.notifications;

// Avoid negative boolean names
const isEnabled = true; // ✅
const isNotDisabled = true; // ❌
```

## File Structure Expectations

### Component Structure (React/Vue)
```typescript
// Component file structure
import React from 'react';
import { User } from '../types/user';
import { userService } from '../services/user-service';
import './UserProfile.css';

interface UserProfileProps {
  userId: string;
  onUpdate?: (user: User) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ 
  userId, 
  onUpdate 
}) => {
  // Hooks and state
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Effects
  useEffect(() => {
    loadUserData();
  }, [userId]);
  
  // Event handlers
  const handleUpdate = async (userData: Partial<User>) => {
    // Implementation
  };
  
  // Helper functions
  const loadUserData = async () => {
    // Implementation
  };
  
  // Render
  return (
    <div className="user-profile">
      {/* Component JSX */}
    </div>
  );
};
```

### Service/Utility Structure
```typescript
// Service file structure
import { logger } from './logger';
import { config } from './config';
import { ApiClient } from './api-client';

interface UserService {
  getUser(id: string): Promise<User>;
  updateUser(id: string, data: Partial<User>): Promise<User>;
  deleteUser(id: string): Promise<void>;
}

class UserServiceImpl implements UserService {
  private apiClient: ApiClient;
  
  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }
  
  async getUser(id: string): Promise<User> {
    try {
      const response = await this.apiClient.get(`/users/${id}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get user', { id, error });
      throw error;
    }
  }
  
  // Other methods...
}

// Export instance
export const userService = new UserServiceImpl(apiClient);
```

## Code Organization Standards

### Import Organization
```typescript
// Group imports in this order:
// 1. External libraries
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { z } from 'zod';

// 2. Internal utilities and services
import { logger } from '../utils/logger';
import { userService } from '../services/user-service';

// 3. Types and interfaces
import { User, UserPreferences } from '../types/user';
import { ApiResponse } from '../types/api';

// 4. Local components
import { UserCard } from './UserCard';
import { LoadingSpinner } from './LoadingSpinner';

// 5. Styles (last)
import './UserList.css';
```

### Function Organization
```typescript
// Organize functions within files:
// 1. Main exported functions first
export async function processUserData(userId: string): Promise<ProcessedData> {
  const userData = await fetchUserData(userId);
  const validatedData = validateUserData(userData);
  return transformUserData(validatedData);
}

// 2. Helper functions below
async function fetchUserData(userId: string): Promise<RawUserData> {
  // Implementation
}

function validateUserData(data: RawUserData): ValidatedUserData {
  // Implementation
}

function transformUserData(data: ValidatedUserData): ProcessedData {
  // Implementation
}
```

## Error Handling Standards

### Error Handling Patterns
```typescript
// Use Result pattern for operations that can fail
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

async function safeUserOperation(userId: string): Promise<Result<User>> {
  try {
    const user = await userService.getUser(userId);
    return { success: true, data: user };
  } catch (error) {
    logger.error('User operation failed', { userId, error });
    return { success: false, error: error as Error };
  }
}

// Custom error classes for different error types
class ValidationError extends Error {
  constructor(message: string, public field: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class NotFoundError extends Error {
  constructor(resource: string, id: string) {
    super(`${resource} with id ${id} not found`);
    this.name = 'NotFoundError';
  }
}
```

## Documentation Standards

### Function Documentation
```typescript
/**
 * Processes user data and returns transformed result
 * 
 * @param userId - The unique identifier for the user
 * @param options - Processing options (optional)
 * @returns Promise resolving to processed user data
 * @throws {NotFoundError} When user doesn't exist
 * @throws {ValidationError} When user data is invalid
 * 
 * @example
 * ```typescript
 * const result = await processUserData('user-123', { includeMetadata: true });
 * console.log(result.name); // User's processed name
 * ```
 */
async function processUserData(
  userId: string,
  options?: ProcessingOptions
): Promise<ProcessedUserData> {
  // Implementation
}
```

### Inline Comments
```typescript
// Use comments to explain WHY, not WHAT
function calculatePricing(basePrice: number, userTier: string): number {
  // Apply tier-based discount to encourage upgrades
  const discount = getTierDiscount(userTier);
  
  // Minimum price protection to maintain profitability
  const discountedPrice = basePrice * (1 - discount);
  return Math.max(discountedPrice, MINIMUM_PRICE);
}

// Avoid obvious comments
const userName = user.name; // ❌ Gets the user name
const userName = user.name; // ✅ No comment needed
```

## Testing Standards

### Test File Structure
```typescript
// user-service.test.ts
import { userService } from '../user-service';
import { mockApiClient } from '../__mocks__/api-client';

describe('UserService', () => {
  beforeEach(() => {
    mockApiClient.reset();
  });
  
  describe('getUser', () => {
    it('should return user data when user exists', async () => {
      // Arrange
      const userId = 'user-123';
      const expectedUser = { id: userId, name: 'John Doe' };
      mockApiClient.get.mockResolvedValue({ data: expectedUser });
      
      // Act
      const result = await userService.getUser(userId);
      
      // Assert
      expect(result).toEqual(expectedUser);
      expect(mockApiClient.get).toHaveBeenCalledWith(`/users/${userId}`);
    });
    
    it('should throw error when user not found', async () => {
      // Arrange
      const userId = 'nonexistent';
      mockApiClient.get.mockRejectedValue(new Error('User not found'));
      
      // Act & Assert
      await expect(userService.getUser(userId)).rejects.toThrow('User not found');
    });
  });
});
```

### Test Naming
```typescript
// Use descriptive test names that explain the scenario
describe('User authentication', () => {
  it('should allow login with valid credentials', () => { ... });
  it('should reject login with invalid password', () => { ... });
  it('should lock account after 3 failed attempts', () => { ... });
  it('should redirect to dashboard after successful login', () => { ... });
});
```

## Performance Guidelines

### Optimization Patterns
```typescript
// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return performExpensiveCalculation(data);
}, [data]);

// Use useCallback for function references
const handleUserUpdate = useCallback((userData: User) => {
  updateUser(userData);
}, [updateUser]);

// Implement proper cleanup
useEffect(() => {
  const subscription = api.subscribe(handleDataUpdate);
  
  return () => {
    subscription.unsubscribe();
  };
}, []);
```

---

**Important**: These standards should be followed consistently across the entire codebase. Update this document as new patterns emerge or standards evolve.