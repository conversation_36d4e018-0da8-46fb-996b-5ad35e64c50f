# Main System Prompt

**Version**: 1.0.0  
**Last Updated**: 2025-06-27  
**Status**: ✅ Active

## Project Overview

This is the primary system prompt for the FaunaLogic project. Use this as the foundational context for all development work.

### Project Description
<!-- Update with your actual project description -->
FaunaLogic is a [description of your project - data processing, web application, etc.]. The system is designed to [primary purpose and key functionality].

### Architecture Overview
<!-- Update with your actual architecture -->
- **Frontend**: [Technology stack - React, Vue, etc.]
- **Backend**: [Technology stack - Node.js, Python, etc.]
- **Database**: [Database technology - PostgreSQL, MongoDB, etc.]
- **Infrastructure**: [Deployment and hosting details]

## Development Constraints

### Technical Requirements
- [ ] All code must be production-ready with appropriate error handling
- [ ] Follow existing patterns and conventions in the codebase
- [ ] Include comprehensive tests for new functionality
- [ ] Maintain backward compatibility unless explicitly approved
- [ ] Document public APIs and complex business logic
- [ ] Use TypeScript/type hints where applicable

### Security Requirements
- [ ] Never expose sensitive data in logs or error messages
- [ ] Validate all user inputs and external data
- [ ] Use parameterized queries for database operations
- [ ] Implement proper authentication and authorization
- [ ] Follow OWASP security guidelines

### Performance Requirements
- [ ] Optimize for performance in data-heavy operations
- [ ] Implement caching where appropriate
- [ ] Monitor and avoid N+1 query problems
- [ ] Use async/await patterns for I/O operations
- [ ] Consider memory usage in large data processing

## Context Files

### Always Include These Files
When working on any task, include these files for context:

- `package.json` - Dependencies and scripts
- `README.md` - Project setup and documentation
- `CLAUDE.md` - Claude-specific project information (if exists)
- Configuration files (`.env.example`, `config/`, etc.)

### Common Important Files
<!-- Update with your actual important files -->
- `src/types/` - Type definitions
- `src/utils/` - Utility functions
- `src/config/` - Application configuration
- `src/services/` - Business logic and external integrations
- `src/models/` - Data models and schemas
- `docs/` - Technical documentation

## Development Guidelines

### Before Starting Any Task
1. **Read the Requirements**: Understand the complete scope before coding
2. **Check Dependencies**: Verify all required dependencies are available
3. **Review Existing Code**: Look for similar implementations to follow patterns
4. **Plan the Implementation**: Break down complex tasks into smaller steps
5. **Consider Side Effects**: Think about impact on existing functionality

### During Development
1. **Follow Existing Patterns**: Maintain consistency with existing codebase
2. **Write Tests First**: Implement tests alongside or before the feature
3. **Handle Edge Cases**: Consider error conditions and boundary cases
4. **Add Logging**: Include appropriate logging for debugging and monitoring
5. **Document as You Go**: Add inline comments for complex logic

### After Implementation
1. **Test Thoroughly**: Run all tests and verify functionality
2. **Check Performance**: Ensure changes don't degrade performance
3. **Update Documentation**: Modify relevant documentation files
4. **Review Dependencies**: Ensure no unnecessary dependencies were added
5. **Verify Security**: Check for potential security vulnerabilities

## File Organization Standards

### Directory Structure
```
src/
├── components/     # Reusable UI components
├── pages/         # Route-specific components
├── services/      # Business logic and API calls
├── utils/         # Helper functions and utilities
├── types/         # TypeScript type definitions
├── hooks/         # Custom React hooks (if applicable)
├── stores/        # State management
├── config/        # Application configuration
└── tests/         # Test files
```

### Naming Conventions
- **Files**: Use kebab-case for file names (`user-service.ts`)
- **Directories**: Use kebab-case for directory names (`user-management/`)
- **Components**: Use PascalCase for component names (`UserProfile.tsx`)
- **Functions**: Use camelCase for function names (`getUserById`)
- **Constants**: Use SCREAMING_SNAKE_CASE (`API_BASE_URL`)
- **Types/Interfaces**: Use PascalCase with descriptive names (`UserProfile`, `ApiResponse`)

## Integration Points

### External Services
<!-- Update with your actual integrations -->
- **Database**: Connection configuration in `src/config/database.ts`
- **APIs**: External API configurations in `src/services/`
- **Authentication**: Auth logic in `src/services/auth/`
- **Caching**: Cache implementation in `src/services/cache/`

### Internal Dependencies
- **Logging**: Use centralized logging utility
- **Error Handling**: Follow established error handling patterns
- **Configuration**: Access config through centralized config service
- **Validation**: Use established validation schemas

## Common Patterns

### Error Handling
```typescript
// Preferred error handling pattern
try {
  const result = await someAsyncOperation();
  return { success: true, data: result };
} catch (error) {
  logger.error('Operation failed', { error, context });
  return { success: false, error: error.message };
}
```

### API Response Format
```typescript
// Standard API response structure
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
```

### Validation Pattern
```typescript
// Use validation schemas for data validation
const schema = z.object({
  email: z.string().email(),
  name: z.string().min(1),
});

const validatedData = schema.parse(inputData);
```

## Quality Standards

### Code Quality Checklist
- [ ] Code follows established patterns and conventions
- [ ] All functions have clear, single responsibilities
- [ ] Complex logic is broken into smaller, testable functions
- [ ] No duplicate code (DRY principle followed)
- [ ] No magic numbers or hardcoded values
- [ ] Proper error handling implemented
- [ ] Performance considerations addressed

### Testing Standards
- [ ] Unit tests for all business logic
- [ ] Integration tests for API endpoints
- [ ] End-to-end tests for critical user flows
- [ ] Test coverage above 80% for new code
- [ ] Tests are deterministic and isolated
- [ ] Mock external dependencies appropriately

## Troubleshooting

### Common Issues and Solutions
1. **Dependency Conflicts**: Check `package-lock.json` and resolve version conflicts
2. **Type Errors**: Ensure all types are properly imported and defined
3. **Database Connections**: Verify connection strings and credentials
4. **Performance Issues**: Profile queries and optimize N+1 problems
5. **Memory Leaks**: Check for unclosed connections and event listeners

### Debug Commands
```bash
# Run tests with coverage
npm run test:coverage

# Type check without building
npm run type-check

# Lint and fix code style issues
npm run lint:fix

# Build and check for errors
npm run build
```

---

**Important**: Always refer to this prompt when starting new development work. Update this document as the project evolves and new patterns emerge.