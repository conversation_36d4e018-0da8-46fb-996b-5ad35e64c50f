# Optimize Database Queries

**Version**: 1.0.0  
**Last Updated**: 2025-06-27  
**Status**: 📋 Pending

## Background/Context

Database query performance has been degrading as data volume grows. This task focuses on identifying and optimizing slow queries, improving database schema design, and implementing query optimization best practices.

### Current Issues
- Slow page load times due to inefficient queries
- N+1 query problems in API endpoints
- Missing database indexes on frequently queried columns
- Unoptimized JOIN operations and subqueries
- Large result sets being loaded into memory
- No query performance monitoring or alerting
- Inefficient pagination implementation

### Performance Impact
- Average API response time: 2.3 seconds (target: <500ms)
- Database CPU utilization: 85% (target: <60%)
- Some queries taking 10+ seconds to complete
- User complaints about slow dashboard loading
- Timeouts during peak usage periods
- High database connection pool exhaustion

## Requirements

### Performance Goals
- [ ] Reduce average query response time to <100ms
- [ ] Eliminate queries taking >1 second
- [ ] Reduce database CPU utilization to <60%
- [ ] Improve API endpoint response times to <500ms
- [ ] Handle 10x current load without degradation
- [ ] Implement efficient pagination for large datasets
- [ ] Add query performance monitoring and alerting

### Query Optimization Targets
- [ ] Identify and fix N+1 query problems
- [ ] Optimize JOIN operations and query plans
- [ ] Implement proper indexing strategy
- [ ] Add query result caching where appropriate
- [ ] Optimize data loading patterns
- [ ] Improve database schema design
- [ ] Implement connection pooling optimization

### Monitoring Requirements
- [ ] Query performance tracking and logging
- [ ] Slow query identification and alerting
- [ ] Database performance metrics dashboard
- [ ] Real-time query analysis tools
- [ ] Historical performance trend analysis

## Technical Approach

### Query Analysis and Optimization
```sql
-- Example of current problematic query
SELECT u.*, p.*, o.*
FROM users u
LEFT JOIN profiles p ON u.id = p.user_id
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.created_at > '2024-01-01'
ORDER BY u.created_at DESC;

-- Optimized version with selective fields and proper indexing
SELECT 
  u.id, u.name, u.email, u.created_at,
  p.bio, p.avatar_url,
  COUNT(o.id) as order_count
FROM users u
LEFT JOIN profiles p ON u.id = p.user_id
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.created_at > '2024-01-01'
GROUP BY u.id, p.id
ORDER BY u.created_at DESC
LIMIT 50;

-- Required indexes
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_profiles_user_id ON profiles(user_id);
```

### N+1 Query Resolution
```typescript
// Current problematic code (N+1 problem)
const users = await User.findAll();
for (const user of users) {
  user.orders = await Order.findAll({ where: { userId: user.id } });
}

// Optimized version with eager loading
const users = await User.findAll({
  include: [{
    model: Order,
    attributes: ['id', 'total', 'status', 'createdAt']
  }]
});

// Alternative: Batch loading approach
const users = await User.findAll();
const userIds = users.map(u => u.id);
const orders = await Order.findAll({ 
  where: { userId: { [Op.in]: userIds } }
});

// Group orders by user
const ordersByUser = orders.reduce((acc, order) => {
  if (!acc[order.userId]) acc[order.userId] = [];
  acc[order.userId].push(order);
  return acc;
}, {});

users.forEach(user => {
  user.orders = ordersByUser[user.id] || [];
});
```

### Query Optimization Service
```typescript
interface QueryOptimizer {
  analyzeQuery(query: string): QueryAnalysis;
  optimizeQuery(query: string): OptimizedQuery;
  getExecutionPlan(query: string): ExecutionPlan;
  suggestIndexes(query: string): IndexSuggestion[];
}

interface QueryAnalysis {
  executionTime: number;
  rowsExamined: number;
  rowsReturned: number;
  indexesUsed: string[];
  bottlenecks: QueryBottleneck[];
  optimizationSuggestions: string[];
}

interface QueryBottleneck {
  type: 'table_scan' | 'nested_loop' | 'sorting' | 'temporary_table';
  table: string;
  estimatedCost: number;
  suggestion: string;
}

class DatabaseOptimizer {
  async analyzeSlowQueries(): Promise<SlowQueryReport> {
    const slowQueries = await this.getSlowQueries();
    const analysis = await Promise.all(
      slowQueries.map(query => this.analyzeQuery(query.sql))
    );
    
    return {
      totalQueries: slowQueries.length,
      averageExecutionTime: this.calculateAverage(analysis.map(a => a.executionTime)),
      worstPerformers: this.identifyWorstPerformers(analysis),
      indexSuggestions: this.aggregateIndexSuggestions(analysis)
    };
  }
}
```

## Implementation Steps

### Phase 1: Analysis and Monitoring
1. **Set up Query Performance Monitoring** (2-3 hours)
   - Enable slow query logging
   - Set up performance monitoring dashboard
   - Implement query timing middleware

2. **Identify Problematic Queries** (3-4 hours)
   - Analyze slow query logs
   - Profile API endpoints
   - Identify N+1 query patterns

3. **Database Schema Analysis** (2-3 hours)
   - Review table structures
   - Analyze existing indexes
   - Identify missing foreign keys

### Phase 2: Index Optimization
4. **Create Missing Indexes** (2-3 hours)
   - Add indexes for frequently queried columns
   - Create composite indexes for complex queries
   - Optimize existing indexes

5. **Analyze Query Execution Plans** (2-3 hours)
   - Review EXPLAIN plans for slow queries
   - Identify table scans and inefficient joins
   - Optimize query structure

### Phase 3: Application-Level Optimization
6. **Fix N+1 Query Problems** (4-6 hours)
   - Implement eager loading strategies
   - Add batch loading where appropriate
   - Optimize ORM query patterns

7. **Implement Query Result Caching** (3-4 hours)
   - Add Redis caching for expensive queries
   - Implement cache invalidation strategies
   - Add query result memoization

8. **Optimize Pagination** (2-3 hours)
   - Implement cursor-based pagination
   - Add efficient offset alternatives
   - Optimize large result set handling

### Phase 4: Advanced Optimization
9. **Connection Pool Optimization** (1-2 hours)
   - Tune connection pool settings
   - Implement connection monitoring
   - Add connection leak detection

10. **Database Configuration Tuning** (2-3 hours)
    - Optimize database server settings
    - Tune memory allocation
    - Configure query cache settings

## Query Optimization Patterns

### Efficient Pagination
```typescript
// Inefficient offset-based pagination
async function getUsersWithOffset(page: number, limit: number) {
  const offset = (page - 1) * limit;
  return await User.findAll({
    offset,
    limit,
    order: [['createdAt', 'DESC']]
  });
}

// Efficient cursor-based pagination
async function getUsersWithCursor(cursor?: string, limit: number = 20) {
  const whereCondition = cursor 
    ? { createdAt: { [Op.lt]: new Date(cursor) } }
    : {};
    
  const users = await User.findAll({
    where: whereCondition,
    order: [['createdAt', 'DESC']],
    limit: limit + 1 // Fetch one extra to check if there are more
  });
  
  const hasMore = users.length > limit;
  if (hasMore) users.pop(); // Remove the extra record
  
  return {
    users,
    hasMore,
    nextCursor: users.length > 0 ? users[users.length - 1].createdAt : null
  };
}
```

### Batch Loading Implementation
```typescript
class DataLoader {
  private batchCache = new Map<string, Promise<any>>();
  
  async loadUserOrders(userIds: string[]): Promise<Order[][]> {
    const cacheKey = userIds.sort().join(',');
    
    if (this.batchCache.has(cacheKey)) {
      return this.batchCache.get(cacheKey);
    }
    
    const promise = this.batchLoadOrders(userIds);
    this.batchCache.set(cacheKey, promise);
    
    // Clear cache after execution
    setTimeout(() => this.batchCache.delete(cacheKey), 100);
    
    return promise;
  }
  
  private async batchLoadOrders(userIds: string[]): Promise<Order[][]> {
    const orders = await Order.findAll({
      where: { userId: { [Op.in]: userIds } }
    });
    
    // Group orders by user ID
    const ordersByUser = userIds.map(userId => 
      orders.filter(order => order.userId === userId)
    );
    
    return ordersByUser;
  }
}
```

### Query Result Caching
```typescript
class QueryCache {
  private cache: Redis;
  private defaultTTL = 300; // 5 minutes
  
  async getCachedQuery<T>(
    cacheKey: string, 
    queryFn: () => Promise<T>,
    ttl: number = this.defaultTTL
  ): Promise<T> {
    const cached = await this.cache.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }
    
    const result = await queryFn();
    await this.cache.setex(cacheKey, ttl, JSON.stringify(result));
    
    return result;
  }
  
  async invalidateCache(pattern: string): Promise<void> {
    const keys = await this.cache.keys(pattern);
    if (keys.length > 0) {
      await this.cache.del(...keys);
    }
  }
}

// Usage example
const queryCache = new QueryCache();

async function getUserDashboardData(userId: string) {
  return queryCache.getCachedQuery(
    `user_dashboard:${userId}`,
    () => this.loadUserDashboardData(userId),
    600 // 10 minutes
  );
}
```

## Database Schema Optimization

### Index Strategy
```sql
-- Current indexes audit
SELECT 
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
ORDER BY tablename, indexname;

-- Proposed new indexes
CREATE INDEX CONCURRENTLY idx_users_email_active ON users(email) WHERE active = true;
CREATE INDEX CONCURRENTLY idx_orders_user_status ON orders(user_id, status);
CREATE INDEX CONCURRENTLY idx_products_category_price ON products(category_id, price);
CREATE INDEX CONCURRENTLY idx_audit_logs_created_at ON audit_logs(created_at) WHERE created_at > '2024-01-01';

-- Composite indexes for complex queries
CREATE INDEX CONCURRENTLY idx_user_orders_composite ON orders(user_id, status, created_at DESC);
CREATE INDEX CONCURRENTLY idx_product_search ON products(name, category_id, price) WHERE active = true;
```

### Table Partitioning
```sql
-- Partition large tables by date
CREATE TABLE audit_logs_2024 PARTITION OF audit_logs
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE audit_logs_2025 PARTITION OF audit_logs
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');

-- Automatic partition management
CREATE OR REPLACE FUNCTION create_monthly_partitions()
RETURNS void AS $$
DECLARE
  start_date date;
  end_date date;
  partition_name text;
BEGIN
  start_date := date_trunc('month', CURRENT_DATE + interval '1 month');
  end_date := start_date + interval '1 month';
  partition_name := 'audit_logs_' || to_char(start_date, 'YYYY_MM');
  
  EXECUTE format('CREATE TABLE %I PARTITION OF audit_logs FOR VALUES FROM (%L) TO (%L)',
    partition_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

## Performance Monitoring

### Query Performance Metrics
```typescript
class QueryPerformanceMonitor {
  private metrics: Map<string, QueryMetrics> = new Map();
  
  async recordQuery(query: string, duration: number, rowCount: number) {
    const queryHash = this.hashQuery(query);
    const existing = this.metrics.get(queryHash) || {
      totalExecutions: 0,
      totalDuration: 0,
      maxDuration: 0,
      avgDuration: 0,
      rowsProcessed: 0
    };
    
    existing.totalExecutions++;
    existing.totalDuration += duration;
    existing.maxDuration = Math.max(existing.maxDuration, duration);
    existing.avgDuration = existing.totalDuration / existing.totalExecutions;
    existing.rowsProcessed += rowCount;
    
    this.metrics.set(queryHash, existing);
    
    // Alert on slow queries
    if (duration > 1000) { // 1 second
      await this.alertSlowQuery(query, duration);
    }
  }
  
  getSlowQueries(threshold: number = 100): SlowQuery[] {
    return Array.from(this.metrics.entries())
      .filter(([_, metrics]) => metrics.avgDuration > threshold)
      .map(([hash, metrics]) => ({ hash, ...metrics }))
      .sort((a, b) => b.avgDuration - a.avgDuration);
  }
}
```

### Database Health Dashboard
```typescript
interface DatabaseHealthMetrics {
  connectionCount: number;
  activeQueries: number;
  slowQueries: number;
  cacheHitRatio: number;
  averageQueryTime: number;
  deadlocks: number;
  diskUsage: number;
  memoryUsage: number;
}

class DatabaseHealthMonitor {
  async getHealthMetrics(): Promise<DatabaseHealthMetrics> {
    const [
      connections,
      activeQueries,
      slowQueries,
      cacheStats,
      queryStats
    ] = await Promise.all([
      this.getConnectionCount(),
      this.getActiveQueryCount(),
      this.getSlowQueryCount(),
      this.getCacheStats(),
      this.getQueryStats()
    ]);
    
    return {
      connectionCount: connections,
      activeQueries: activeQueries,
      slowQueries: slowQueries,
      cacheHitRatio: cacheStats.hitRatio,
      averageQueryTime: queryStats.avgTime,
      deadlocks: await this.getDeadlockCount(),
      diskUsage: await this.getDiskUsage(),
      memoryUsage: await this.getMemoryUsage()
    };
  }
}
```

## Testing Strategy

### Performance Testing
```typescript
describe('Query Performance', () => {
  it('should handle large dataset queries efficiently', async () => {
    const startTime = Date.now();
    const result = await getUsersWithPagination(1, 100);
    const duration = Date.now() - startTime;
    
    expect(duration).toBeLessThan(500); // Should complete in <500ms
    expect(result.users).toHaveLength(100);
  });
  
  it('should avoid N+1 queries in user orders endpoint', async () => {
    const queryCount = await getQueryCount();
    await getUsersWithOrders();
    const finalQueryCount = await getQueryCount();
    
    // Should execute only 2 queries regardless of user count
    expect(finalQueryCount - queryCount).toBeLessThanOrEqual(2);
  });
  
  it('should use indexes for frequently queried columns', async () => {
    const plan = await getQueryPlan('SELECT * FROM users WHERE email = $1');
    expect(plan).toContain('Index Scan');
    expect(plan).not.toContain('Seq Scan');
  });
});
```

### Load Testing
- [ ] Simulate concurrent user load
- [ ] Test database under peak traffic
- [ ] Verify query performance with large datasets
- [ ] Test connection pool behavior under load
- [ ] Validate cache effectiveness

## Files Affected

### New Files to Create
- `src/services/query-optimizer.ts` - Query optimization service
- `src/services/database-monitor.ts` - Performance monitoring
- `src/utils/query-cache.ts` - Query result caching
- `src/utils/data-loader.ts` - Batch loading utilities
- `src/middleware/query-timing.ts` - Query performance middleware
- `migrations/add-indexes.sql` - Database index creation
- `migrations/optimize-schema.sql` - Schema optimization
- `tests/performance/` - Performance test suite
- `scripts/analyze-queries.js` - Query analysis scripts

### Files to Modify
- `src/models/*.ts` - Add eager loading configurations
- `src/services/user-service.ts` - Fix N+1 query problems
- `src/services/order-service.ts` - Optimize order queries
- `src/routes/api/*.ts` - Add query optimization
- `src/config/database.ts` - Connection pool optimization
- `package.json` - Add performance monitoring dependencies

### Database Scripts
```sql
-- Performance analysis queries
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  stddev_time,
  min_time,
  max_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 20;

-- Index usage analysis
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_tup_read DESC;
```

## Success Criteria

### Definition of Done
- [ ] Average query response time <100ms
- [ ] No queries taking longer than 1 second
- [ ] N+1 query problems eliminated
- [ ] Appropriate indexes created for all slow queries
- [ ] Query result caching implemented
- [ ] Performance monitoring dashboard operational
- [ ] Database CPU utilization <60%
- [ ] Connection pool optimized
- [ ] Comprehensive performance test suite
- [ ] Documentation updated with optimization guidelines

### Performance Benchmarks
- [ ] User dashboard loads in <500ms
- [ ] Product search results in <200ms
- [ ] Order history pagination in <300ms
- [ ] User profile page loads in <400ms
- [ ] Admin reports generate in <2 seconds
- [ ] Database handles 10x current load
- [ ] Zero query timeouts during peak hours

### Monitoring Criteria
- [ ] Slow query alerts configured
- [ ] Database health dashboard shows green
- [ ] Query performance trends tracked
- [ ] Cache hit ratio >80%
- [ ] Connection pool utilization <80%
- [ ] No deadlocks or lock contention
- [ ] Memory usage optimized

---

**Estimated Effort**: 18-25 hours  
**Target Completion**: 3 weeks  
**Prerequisites**: Performance monitoring tools, database backup, staging environment