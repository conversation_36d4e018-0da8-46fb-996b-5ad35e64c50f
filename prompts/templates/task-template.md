# General Task Template

**Version**: 1.0.0  
**Last Updated**: 2025-06-27  
**Status**: 📋 Template

## Task Overview

### Task Summary
**Task Name**: [Clear, descriptive name for the task]

**Type**: [Development | Refactoring | Bug Fix | Enhancement | Research | Documentation | Infrastructure]

**Priority**: [Critical | High | Medium | Low]

**Complexity**: [High | Medium | Low]

**Estimated Effort**: [X hours/days]

### Task Context
**Task ID**: [Project management reference, e.g., TASK-123, TICKET-456]

**Assignee**: [Team member responsible]

**Requestor**: [Who requested this task]

**Due Date**: [YYYY-MM-DD]

**Sprint/Milestone**: [Sprint X or Milestone name]

## Background/Context

### Problem Statement
<!-- Describe the current situation that needs to be addressed -->
**Current Situation**: [What is the current state?]

**Issues Identified**:
- [ ] Issue 1: [Specific problem or limitation]
- [ ] Issue 2: [Additional issues]
- [ ] Issue 3: [More issues if applicable]

**Impact**: [How these issues affect users, performance, or development]

### Business Justification
**Why This Task Matters**:
- [ ] User experience improvement
- [ ] Performance optimization
- [ ] Technical debt reduction
- [ ] Security enhancement
- [ ] Compliance requirement
- [ ] Cost reduction
- [ ] Developer productivity

**Success Impact**: [What will improve when this task is completed]

## Requirements

### Functional Requirements
- [ ] Requirement 1: [Specific functionality or outcome needed]
- [ ] Requirement 2: [Additional requirements]
- [ ] Requirement 3: [More requirements]
- [ ] Requirement 4: [Edge cases or special conditions]

### Non-Functional Requirements
**Performance**:
- [ ] Response time: [<X milliseconds/seconds]
- [ ] Throughput: [X requests per second]
- [ ] Resource usage: [Memory, CPU constraints]
- [ ] Scalability: [Must handle X concurrent users]

**Quality**:
- [ ] Code coverage: [>X% test coverage required]
- [ ] Code quality: [Linting, formatting standards]
- [ ] Documentation: [API docs, README updates]
- [ ] Error handling: [Graceful error management]

**Security**:
- [ ] Authentication: [Required access controls]
- [ ] Authorization: [Permission levels needed]
- [ ] Data protection: [Encryption, validation]
- [ ] Audit logging: [Security event tracking]

### Constraints
- [ ] Technology constraints: [Must use specific technologies]
- [ ] Resource constraints: [Budget, time, personnel limits]
- [ ] Integration constraints: [Must work with existing systems]
- [ ] Compliance constraints: [Regulatory requirements]

## Technical Approach

### Solution Strategy
**Approach**: [High-level description of how you plan to solve this]

**Key Decisions**:
- [ ] Decision 1: [Technology or architectural choice made]
- [ ] Decision 2: [Additional decisions with rationale]
- [ ] Decision 3: [More decisions if applicable]

### Implementation Plan
**Phase 1**: [Description - X hours]
- [ ] Subtask 1.1: [Specific work item]
- [ ] Subtask 1.2: [Additional work items]

**Phase 2**: [Description - X hours]  
- [ ] Subtask 2.1: [Specific work item]
- [ ] Subtask 2.2: [Additional work items]

**Phase 3**: [Description - X hours]
- [ ] Subtask 3.1: [Specific work item]
- [ ] Subtask 3.2: [Additional work items]

### Technical Details
**Architecture Changes**:
```
Current State:
[Describe current architecture/implementation]

Proposed State:
[Describe target architecture/implementation]
```

**Code Structure**:
```typescript
// Key interfaces or classes to implement
interface TaskInterface {
  property1: string;
  property2: number;
  method1(): Promise<Result>;
}

class TaskImplementation implements TaskInterface {
  // Implementation details
}
```

**Data Model Changes**:
```sql
-- Database changes if applicable
ALTER TABLE existing_table ADD COLUMN new_column VARCHAR(255);
CREATE INDEX idx_new_column ON existing_table(new_column);
```

## Integration Points

### System Dependencies
**Internal Dependencies**:
- [ ] Component A: [How this task affects/uses Component A]
- [ ] Service B: [Integration requirements with Service B]
- [ ] Database: [Data access or schema changes]

**External Dependencies**:
- [ ] Third-party API: [External service integration]
- [ ] Library/Framework: [New dependencies to add]
- [ ] Infrastructure: [Deployment or hosting changes]

### API Changes
```typescript
// New or modified API endpoints
interface TaskAPI {
  createTask(data: TaskData): Promise<TaskResponse>;
  updateTask(id: string, data: Partial<TaskData>): Promise<TaskResponse>;
  getTask(id: string): Promise<TaskResponse>;
  deleteTask(id: string): Promise<void>;
}

// Request/Response formats
interface TaskData {
  name: string;
  description?: string;
  priority: Priority;
  dueDate?: Date;
}
```

### UI/UX Changes
**User Interface Updates**:
- [ ] New pages/screens: [List new UI components]
- [ ] Modified pages: [Changes to existing UI]
- [ ] Navigation changes: [Menu or routing updates]
- [ ] Form updates: [New fields or validation]

## Testing Strategy

### Test Coverage
**Unit Tests** (Target: >90%):
- [ ] Core business logic functions
- [ ] Utility functions
- [ ] Data validation methods
- [ ] Error handling scenarios

**Integration Tests**:
- [ ] API endpoint functionality
- [ ] Database operations
- [ ] Service-to-service communication
- [ ] Third-party integrations

**End-to-End Tests**:
- [ ] Complete user workflows
- [ ] Cross-browser compatibility
- [ ] Mobile responsiveness
- [ ] Performance under load

### Test Cases
```typescript
// Example test structure
describe('Task Implementation', () => {
  describe('Core Functionality', () => {
    it('should handle valid input correctly', async () => {
      const input = { /* valid test data */ };
      const result = await taskFunction(input);
      expect(result).toEqual(expectedOutput);
    });
    
    it('should reject invalid input', async () => {
      const invalidInput = { /* invalid test data */ };
      await expect(taskFunction(invalidInput))
        .rejects.toThrow('Validation error');
    });
  });
  
  describe('Edge Cases', () => {
    it('should handle empty data gracefully', async () => {
      const result = await taskFunction({});
      expect(result).toBeDefined();
    });
  });
});
```

### Performance Testing
- [ ] Load testing: [X concurrent users]
- [ ] Stress testing: [Beyond normal capacity]
- [ ] Memory profiling: [Memory usage optimization]
- [ ] Response time validation: [<X ms response time]

## Files Affected

### New Files to Create
**Source Code**:
- `src/services/task-service.ts` - [Core task logic]
- `src/models/task-model.ts` - [Data model definitions]
- `src/controllers/task-controller.ts` - [API endpoints]
- `src/utils/task-utils.ts` - [Utility functions]

**Configuration**:
- `config/task-config.json` - [Task-specific configuration]
- `migrations/add-task-features.sql` - [Database changes]

**Tests**:
- `tests/unit/task-service.test.ts` - [Unit tests]
- `tests/integration/task-api.test.ts` - [Integration tests]

### Files to Modify
- [ ] `src/app.ts` - [Add new routes or middleware]
- [ ] `src/types/index.ts` - [Add new type definitions]
- [ ] `package.json` - [Add new dependencies]
- [ ] `README.md` - [Update documentation]
- [ ] `.env.example` - [Add new environment variables]

### Dependencies
```json
{
  "dependencies": {
    "new-library": "^1.0.0",
    "helper-util": "^2.1.0"
  },
  "devDependencies": {
    "@types/new-library": "^1.0.0"
  }
}
```

## Risk Assessment

### Identified Risks
**High Risk**:
- [ ] Risk 1: [Description and likelihood]
  - **Impact**: [What happens if this risk occurs]
  - **Mitigation**: [How to prevent or handle]

**Medium Risk**:
- [ ] Risk 2: [Description and likelihood]  
  - **Impact**: [What happens if this risk occurs]
  - **Mitigation**: [How to prevent or handle]

**Low Risk**:
- [ ] Risk 3: [Description and likelihood]
  - **Impact**: [What happens if this risk occurs]
  - **Mitigation**: [How to prevent or handle]

### Rollback Plan
- [ ] Feature flags available to disable changes
- [ ] Database rollback scripts prepared
- [ ] Previous version deployment ready
- [ ] Monitoring alerts configured for issues
- [ ] Communication plan for stakeholders

## Success Criteria

### Definition of Done
- [ ] All functional requirements implemented
- [ ] All non-functional requirements met
- [ ] Code review completed and approved
- [ ] Comprehensive test coverage achieved (>90%)
- [ ] Performance benchmarks met
- [ ] Security review passed (if applicable)
- [ ] Documentation updated
- [ ] Deployed to production successfully
- [ ] Monitoring and alerting configured
- [ ] Stakeholder acceptance received

### Acceptance Criteria
- [ ] Criterion 1: [Specific measurable outcome]
- [ ] Criterion 2: [Additional measurable outcome]
- [ ] Criterion 3: [More specific criteria]
- [ ] Performance criteria: [Response time, throughput, etc.]
- [ ] Quality criteria: [Error rates, user satisfaction, etc.]

### Success Metrics
**Quantitative Metrics**:
- [ ] Performance improvement: [X% faster response time]
- [ ] Error reduction: [X% fewer errors]
- [ ] User adoption: [X% of users use new functionality]
- [ ] Cost savings: [$X reduction in operational costs]

**Qualitative Metrics**:
- [ ] User satisfaction: [Positive feedback from users]
- [ ] Developer experience: [Easier to maintain/extend]
- [ ] Code quality: [Improved maintainability score]
- [ ] Technical debt: [Reduced complexity]

## Monitoring and Maintenance

### Monitoring Requirements
**Key Metrics to Track**:
- [ ] Task completion rate
- [ ] Error frequency and types
- [ ] Performance metrics (response time, throughput)
- [ ] User engagement with new functionality
- [ ] System resource utilization

**Alerting Rules**:
- [ ] Error rate exceeds threshold (>1%)
- [ ] Response time degrades (>Xms)
- [ ] System resource usage high (>80%)
- [ ] Failed task processing
- [ ] Security incidents

### Maintenance Plan
**Regular Maintenance Tasks**:
- [ ] Weekly: [Performance monitoring review]
- [ ] Monthly: [Usage analytics analysis]
- [ ] Quarterly: [Code quality assessment]
- [ ] Annually: [Architecture review and optimization]

**Documentation Updates**:
- [ ] API documentation refresh
- [ ] User guide updates
- [ ] Technical documentation maintenance
- [ ] Troubleshooting guide updates

## Additional Notes

### Assumptions Made
- [ ] Assumption 1: [Key assumption about requirements]  
- [ ] Assumption 2: [Technical assumption]
- [ ] Assumption 3: [Resource or timeline assumption]

### Future Considerations
- [ ] Enhancement 1: [Potential future improvements]
- [ ] Enhancement 2: [Scalability considerations]
- [ ] Enhancement 3: [Integration opportunities]

### Lessons Learned
<!-- Update after task completion -->
- **What Went Well**: [Successful aspects of implementation]
- **Challenges Faced**: [Difficulties encountered and resolved]
- **Process Improvements**: [Better ways to handle similar tasks]
- **Technical Learnings**: [New knowledge or skills gained]

### Related Tasks
- [ ] Dependency Task 1: [Must be completed before this task]
- [ ] Follow-up Task 1: [Should be done after this task]
- [ ] Related Task 1: [Connected work that may be affected]

---

**Template Usage Instructions**:
1. Copy this template for each new development task
2. Customize sections based on task type and complexity
3. Remove sections that don't apply to your specific task
4. Keep the prompt updated as you work through the task
5. Use checkboxes to track progress and completion
6. Update the status and dates as work progresses
7. Archive completed task prompts for future reference and learning