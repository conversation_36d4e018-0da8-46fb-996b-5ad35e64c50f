# FaunaLogic Submission Service Environment Configuration
# Copy this file to .env and customize for your environment

# Application Environment
ENV=development
DEBUG=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# Database Configuration (CRITICAL: Use API user only)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=faunalogic
DB_USER=faunalogic_api
DB_PASSWORD=flatpass
DB_SCHEMA=faunalogic_dapi_v1
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=10

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# File Upload Configuration
MAX_UPLOAD_SIZE=104857600  # 100MB in bytes
UPLOAD_DIR=./temp/uploads
TEMP_DIR=./temp

# Darwin Core Archive Processing
DWC_VALIDATION_LEVEL=strict
DWC_MAX_RECORDS=100000
DWC_BATCH_SIZE=1000

# Security Configuration
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=
LOG_DIR=./logs

# Monitoring Configuration (optional)
SENTRY_DSN=
PROMETHEUS_METRICS=true

# Processing Configuration
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT=3600
RETRY_ATTEMPTS=3
RETRY_DELAY=60

# Spatial Data Configuration
DEFAULT_SRID=4326
SPATIAL_PRECISION=6