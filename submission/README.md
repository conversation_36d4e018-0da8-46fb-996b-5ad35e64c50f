# FaunaLogic Submission Service

A Python-based service for processing Darwin Core Archive biodiversity informatics file submissions using Celery for asynchronous processing.

## 🏗️ Architecture

```
submission/
├── app/                    # Application source code
├── config/                 # Configuration files
├── docker/                 # Docker configuration
├── requirements/           # Python dependencies
├── scripts/               # Utility scripts
├── tests/                 # Test suite
└── prompts/              # AI prompts and documentation
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Redis (for Celery broker)
- PostgreSQL database (via FaunaLogic database service)
- Docker and Docker Compose (optional)

### 1. Development Setup

```bash
# Set up development environment
make setup

# Activate virtual environment
source .venv/bin/activate

# Copy environment configuration
cp .env.example .env
# Edit .env with your settings
```

### 2. Start Services

**Option A: Local Development**
```bash
# Start Redis (if not using Docker)
redis-server

# Start development server
make dev

# In separate terminals:
make worker    # Celery worker
make beat      # Celery beat scheduler
make flower    # Monitoring (optional)
```

**Option B: Docker Compose**
```bash
# Start all services
make docker-up

# View logs
make docker-logs

# Stop services
make docker-down
```

## 🔐 Database Integration (CRITICAL)

**⚠️ ALL database connections MUST use the dedicated API user:**

```python
# Database Configuration (REQUIRED)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=faunalogic
DB_USER=faunalogic_api     # NEVER use postgres user
DB_PASSWORD=flatpass
DB_SCHEMA=faunalogic_dapi_v1
```

The service automatically:
- Sets `search_path = faunalogic_dapi_v1, public`
- Uses API functions for all database operations
- Maintains audit trails through database context

## 📊 Service Features

### Darwin Core Archive Processing
- **File Format Support**: Standard DwC-A ZIP files
- **Core Types**: Occurrence, Event, Taxon, Location
- **Extensions**: Multimedia, Measurements, Resource Relationships
- **Validation**: Comprehensive format and data validation
- **Spatial Data**: Coordinate transformation and security obfuscation

### Asynchronous Processing
- **Task Queue**: Celery with Redis broker
- **File Processing**: Chunked processing for large files
- **Error Handling**: Automatic retry with exponential backoff
- **Monitoring**: Celery Flower for task monitoring

### API Endpoints
```
POST /api/v1/submissions/upload     # Upload DwC-A file
GET  /api/v1/submissions/{id}       # Get submission status
GET  /api/v1/submissions/           # List submissions
GET  /api/v1/health                 # Health check
```

## 🛠️ Development Workflow

### Code Quality
```bash
# Format code
make format

# Run linting
make lint

# Check code quality
make check
```

### Testing
```bash
# Run all tests
make test

# Run specific test types
make test-unit          # Unit tests only
make test-integration   # Integration tests only

# Watch mode for development
make test-watch

# Generate coverage report
make coverage
```

### Database Operations
```bash
# Check database connection
make db-check

# The service uses the database service migration system
# No separate migrations needed
```

## 📁 Key Components

### File Processing Pipeline
```python
# Standard processing workflow
1. File upload and validation
2. Darwin Core Archive extraction
3. Metadata parsing and validation
4. Core data processing
5. Extension data handling
6. Spatial data transformation
7. Database insertion via API functions
8. Status updates and notifications
```

### Service Architecture
```
Upload API → Queue → Worker → Database
    ↓         ↓        ↓         ↓
FastAPI   Celery   DwC-A    PostgreSQL
          Redis   Processor  API Layer
```

## 🔧 Configuration

### Environment Variables
Key configuration options in `.env`:

```bash
# Processing Configuration
DWC_VALIDATION_LEVEL=strict     # strict|relaxed|minimal
DWC_MAX_RECORDS=100000         # Maximum records per submission
DWC_BATCH_SIZE=1000            # Processing batch size

# File Upload Configuration
MAX_UPLOAD_SIZE=104857600      # 100MB max file size
UPLOAD_DIR=./temp/uploads      # Upload directory

# Task Configuration
MAX_CONCURRENT_TASKS=10        # Concurrent processing limit
TASK_TIMEOUT=3600             # Task timeout (1 hour)
RETRY_ATTEMPTS=3              # Retry attempts on failure
```

### Celery Configuration
- **Broker**: Redis
- **Serializer**: JSON
- **Result Backend**: Redis
- **Timezone**: UTC
- **Concurrency**: 4 workers (configurable)

## 🧪 Testing

### Test Structure
```
tests/
├── unit/                  # Unit tests
│   ├── test_services/
│   ├── test_models/
│   └── test_utils/
├── integration/           # Integration tests
│   ├── test_database/
│   ├── test_api/
│   └── test_tasks/
├── fixtures/             # Test data
└── conftest.py          # Test configuration
```

### Sample Test Data
The test suite includes:
- Sample Darwin Core Archive files
- Mock database responses
- Test fixtures for various scenarios
- Performance benchmarks

## 📚 API Documentation

### Submission Upload
```bash
curl -X POST "http://localhost:8001/api/v1/submissions/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@sample.zip" \
  -F "validation_level=strict"
```

### Check Status
```bash
curl "http://localhost:8001/api/v1/submissions/{submission_id}"
```

### List Submissions
```bash
curl "http://localhost:8001/api/v1/submissions/?limit=10&offset=0"
```

## 🔍 Monitoring

### Service Health
```bash
# Check service status
make status

# Health check endpoint
make health

# View service logs
make docker-logs
```

### Celery Monitoring
- **Flower UI**: http://localhost:5555
- **Redis CLI**: Monitor queue status
- **Logs**: Structured logging with context

### Metrics and Alerting
- Prometheus metrics endpoint: `/metrics`
- Custom metrics: processing time, success rate, error counts
- Integration with monitoring systems

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failures**:
   ```bash
   # Check database connectivity
   make db-check
   
   # Verify API user permissions
   psql -h localhost -U faunalogic_api -d faunalogic
   ```

2. **File Processing Errors**:
   ```bash
   # Check worker logs
   make docker-logs
   
   # Restart workers
   docker-compose restart celery-worker
   ```

3. **Redis Connection Issues**:
   ```bash
   # Check Redis status
   redis-cli ping
   
   # Restart Redis
   docker-compose restart redis
   ```

### Error Recovery
- Failed tasks are automatically retried (3 attempts by default)
- Corrupted files are quarantined for manual review
- Database transactions ensure data consistency
- Comprehensive error logging for debugging

## 🤝 Development Guidelines

### Code Style
- **Formatter**: Black
- **Import Sorting**: isort
- **Linting**: Flake8
- **Type Checking**: MyPy
- **Line Length**: 88 characters

### Commit Guidelines
- Use conventional commit messages
- Include tests for new features
- Update documentation as needed
- Run `make check` before committing

### Adding New Features
1. Create feature branch
2. Add tests first (TDD approach)
3. Implement feature
4. Update documentation
5. Run full test suite
6. Submit pull request

## 📈 Performance Considerations

### File Processing
- Streaming processing for large files
- Memory-efficient parsing
- Parallel processing where possible
- Resource limits and timeouts

### Database Operations
- Batch insertions for performance
- Connection pooling
- API function usage for security
- Spatial index utilization

### Scalability
- Horizontal scaling via additional workers
- Redis clustering for high availability
- Load balancing for API endpoints
- Resource monitoring and auto-scaling

## 🔒 Security

### File Security
- File type validation
- Archive extraction protection
- Malware scanning (configurable)
- Size limits and timeouts

### Data Security
- API user database access
- Spatial data obfuscation
- Audit logging
- Input sanitization

### Network Security
- HTTPS in production
- API rate limiting
- Authentication/authorization
- Request validation

---

**🔑 Remember**: Always use `faunalogic_api` user for database connections and follow the established API patterns from the database service!

## 📝 License

This service is part of the FaunaLogic spatial data management system.