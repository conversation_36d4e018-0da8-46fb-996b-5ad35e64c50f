"""
FaunaLogic Submission Service Configuration
"""

import os
from pathlib import Path
from typing import Optional, List
from pydantic import BaseSettings, Field, validator


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "FaunaLogic Submission Service"
    APP_VERSION: str = "1.0.0"
    ENV: str = Field(default="development", env="ENV")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # API Configuration
    API_HOST: str = Field(default="0.0.0.0", env="API_HOST")
    API_PORT: int = Field(default=8000, env="API_PORT")
    API_PREFIX: str = Field(default="/api/v1", env="API_PREFIX")
    
    # Database Configuration - MUST use faunalogic_api user
    DB_HOST: str = Field(default="localhost", env="DB_HOST")
    DB_PORT: int = Field(default=5432, env="DB_PORT")
    DB_NAME: str = Field(default="faunalogic", env="DB_NAME")
    DB_USER: str = Field(default="faunalogic_api", env="DB_USER")  # CRITICAL: API user only
    DB_PASSWORD: str = Field(default="flatpass", env="DB_PASSWORD")
    DB_SCHEMA: str = Field(default="faunalogic_dapi_v1", env="DB_SCHEMA")
    DB_POOL_SIZE: int = Field(default=10, env="DB_POOL_SIZE")
    DB_MAX_OVERFLOW: int = Field(default=20, env="DB_MAX_OVERFLOW")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_MAX_CONNECTIONS: int = Field(default=10, env="REDIS_MAX_CONNECTIONS")
    
    # Celery Configuration
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/0", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/0", env="CELERY_RESULT_BACKEND")
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: List[str] = ["json"]
    CELERY_TIMEZONE: str = "UTC"
    CELERY_ENABLE_UTC: bool = True
    
    # File Upload Configuration
    MAX_UPLOAD_SIZE: int = Field(default=100 * 1024 * 1024, env="MAX_UPLOAD_SIZE")  # 100MB
    UPLOAD_DIR: Path = Field(default=Path("./temp/uploads"), env="UPLOAD_DIR")
    TEMP_DIR: Path = Field(default=Path("./temp"), env="TEMP_DIR")
    ALLOWED_EXTENSIONS: List[str] = [".zip", ".dwca"]
    
    # Darwin Core Archive Processing
    DWC_VALIDATION_LEVEL: str = Field(default="strict", env="DWC_VALIDATION_LEVEL")
    DWC_MAX_RECORDS: int = Field(default=100000, env="DWC_MAX_RECORDS")
    DWC_BATCH_SIZE: int = Field(default=1000, env="DWC_BATCH_SIZE")
    
    # Security Configuration
    SECRET_KEY: str = Field(default="dev-secret-key-change-in-production", env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    ALGORITHM: str = "HS256"
    
    # Logging Configuration
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    LOG_DIR: Path = Field(default=Path("./logs"), env="LOG_DIR")
    
    # Monitoring Configuration
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    PROMETHEUS_METRICS: bool = Field(default=True, env="PROMETHEUS_METRICS")
    
    # Processing Configuration
    MAX_CONCURRENT_TASKS: int = Field(default=10, env="MAX_CONCURRENT_TASKS")
    TASK_TIMEOUT: int = Field(default=3600, env="TASK_TIMEOUT")  # 1 hour
    RETRY_ATTEMPTS: int = Field(default=3, env="RETRY_ATTEMPTS")
    RETRY_DELAY: int = Field(default=60, env="RETRY_DELAY")  # seconds
    
    # Spatial Data Configuration
    DEFAULT_SRID: int = Field(default=4326, env="DEFAULT_SRID")
    SPATIAL_PRECISION: int = Field(default=6, env="SPATIAL_PRECISION")
    
    @validator("DB_USER")
    def validate_db_user(cls, v):
        """Ensure only API user is used for database connections"""
        if v != "faunalogic_api":
            raise ValueError("Database connections must use 'faunalogic_api' user only")
        return v
    
    @validator("UPLOAD_DIR", "TEMP_DIR", "LOG_DIR")
    def ensure_directories_exist(cls, v):
        """Ensure directories exist"""
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    @validator("DWC_VALIDATION_LEVEL")
    def validate_dwc_level(cls, v):
        """Validate DwC validation level"""
        allowed_levels = ["strict", "relaxed", "minimal"]
        if v not in allowed_levels:
            raise ValueError(f"DWC validation level must be one of {allowed_levels}")
        return v
    
    @property
    def database_url(self) -> str:
        """Get database URL for asyncpg"""
        return (
            f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}"
            f"@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        )
    
    @property
    def database_dsn(self) -> str:
        """Get database DSN with schema context"""
        return (
            f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}"
            f"@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
            f"?options=-c%20search_path={self.DB_SCHEMA},public"
        )
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.ENV.lower() in ["development", "dev", "local"]
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.ENV.lower() in ["production", "prod"]
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing mode"""
        return self.ENV.lower() in ["testing", "test"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings