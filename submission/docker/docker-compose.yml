# FaunaLogic Submission Service - Docker Compose
version: '3.8'

services:
  submission-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: faunalogic-submission-api
    ports:
      - "8001:8000"
    environment:
      - ENV=development
      - DB_HOST=host.docker.internal  # Connect to database service
      - DB_PORT=5432
      - DB_NAME=faunalogic
      - DB_USER=faunalogic_api
      - DB_PASSWORD=flatpass
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ../app:/app/app
      - ../temp:/app/temp
      - ../logs:/app/logs
    depends_on:
      - redis
    networks:
      - faunalogic-network
    restart: unless-stopped

  celery-worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: faunalogic-celery-worker
    command: ["celery", "-A", "app.tasks.celery_app", "worker", "--loglevel=info", "--concurrency=4"]
    environment:
      - ENV=development
      - DB_HOST=host.docker.internal
      - DB_PORT=5432
      - DB_NAME=faunalogic
      - DB_USER=faunalogic_api
      - DB_PASSWORD=flatpass
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ../app:/app/app
      - ../temp:/app/temp
      - ../logs:/app/logs
    depends_on:
      - redis
    networks:
      - faunalogic-network
    restart: unless-stopped

  celery-beat:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: faunalogic-celery-beat
    command: ["celery", "-A", "app.tasks.celery_app", "beat", "--loglevel=info"]
    environment:
      - ENV=development
      - DB_HOST=host.docker.internal
      - DB_PORT=5432
      - DB_NAME=faunalogic
      - DB_USER=faunalogic_api
      - DB_PASSWORD=flatpass
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ../app:/app/app
      - ../temp:/app/temp
      - ../logs:/app/logs
    depends_on:
      - redis
    networks:
      - faunalogic-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: faunalogic-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - faunalogic-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  flower:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: faunalogic-flower
    command: ["celery", "-A", "app.tasks.celery_app", "flower", "--port=5555"]
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    networks:
      - faunalogic-network
    restart: unless-stopped

volumes:
  redis_data:

networks:
  faunalogic-network:
    driver: bridge