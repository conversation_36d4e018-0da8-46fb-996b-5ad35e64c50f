# Submission Service Coding Standards

**Version**: 1.0.0  
**Last Updated**: 2025-06-29  
**Status**: 🚧 Development Ready  
**Service**: Data Submission Processing Layer

## 🔐 MANDATORY: Database Access Standards

### Critical Connection Requirements
**ALL database connections MUST use the dedicated API user:**

```python
# Database Configuration (REQUIRED)
DATABASE_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': 'faunalogic',
    'user': 'faunalogic_api',  # NEVER use postgres user
    'password': os.getenv('DB_API_PASSWORD', 'flatpass'),
    'options': '-c search_path=faunalogic_dapi_v1,public'
}

# Connection Pattern (MANDATORY)
async def get_db_connection():
    """Get database connection with proper API user setup"""
    conn = await asyncpg.connect(**DATABASE_CONFIG)
    
    # ALWAYS set schema context first
    await conn.execute("SET search_path = faunalogic_dapi_v1, public")
    
    return conn
```

### API-Only Database Access Pattern
```python
# ✅ CORRECT: Use API functions through database connection
async def create_submission(user_id: str, submission_data: dict):
    async with get_db_connection() as conn:
        # Set user context for audit trail
        await conn.execute("SELECT api_set_context($1, $2)", user_id, tenant_id)
        
        # Use API functions for data insertion
        result = await conn.fetchrow(
            "SELECT api_create_submission($1, $2, $3)",
            submission_data['type'],
            submission_data['metadata'],
            submission_data['file_info']
        )
        return result['submission_id']

# ❌ WRONG: Direct table access (not allowed for API user)
# await conn.execute("INSERT INTO submission ...")
```

## Python Code Style Standards

### General Principles
- **Type Hints**: All functions must include type hints
- **Async/Await**: Use async/await for all I/O operations
- **Error Handling**: Comprehensive exception handling with specific error types
- **Documentation**: Docstrings for all classes and public methods
- **Testing**: Unit tests for all core functionality
- **Security**: Input validation and sanitization

### Project Structure
```
submission/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI application entry
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py           # Application settings
│   │   ├── database.py           # Database configuration
│   │   └── celery_config.py      # Celery configuration
│   ├── models/
│   │   ├── __init__.py
│   │   ├── submission.py         # Submission data models
│   │   ├── dwc_archive.py        # Darwin Core models
│   │   └── database.py           # Database models
│   ├── services/
│   │   ├── __init__.py
│   │   ├── submission_handler.py # File upload handling
│   │   ├── dwc_processor.py      # Darwin Core processing
│   │   ├── database_service.py   # Database operations
│   │   └── validation_service.py # Data validation
│   ├── tasks/
│   │   ├── __init__.py
│   │   ├── processing_tasks.py   # Celery tasks
│   │   └── cleanup_tasks.py      # Cleanup tasks
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── submissions.py    # Submission endpoints
│   │   │   └── status.py         # Status endpoints
│   │   └── dependencies.py       # FastAPI dependencies
│   └── utils/
│       ├── __init__.py
│       ├── file_utils.py         # File handling utilities
│       ├── validation_utils.py   # Validation utilities
│       └── spatial_utils.py      # Spatial data utilities
├── tests/
│   ├── __init__.py
│   ├── conftest.py              # Test configuration
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── fixtures/                # Test data fixtures
├── requirements/
│   ├── base.txt                 # Base requirements
│   ├── dev.txt                  # Development requirements
│   └── prod.txt                 # Production requirements
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
├── scripts/
│   ├── setup.py                 # Setup script
│   └── migrate.py               # Migration script
└── prompts/                     # AI prompts directory
```

### Naming Conventions

#### File and Directory Naming
```python
# Files: lowercase with underscores
submission_handler.py          # ✅ Clear and descriptive
dwc_processor.py              # ✅ Abbreviation commonly understood
validation_service.py         # ✅ Purpose-based naming

# Avoid camelCase for files
submissionHandler.py          # ❌ Not Python convention
DwCProcessor.py              # ❌ Mixed case not standard
```

#### Class Naming
```python
# Classes: PascalCase with descriptive names
class SubmissionHandler:
    """Handle file submission processing"""
    pass

class DarwinCoreArchiveProcessor:
    """Process Darwin Core Archive files"""
    pass

class DatabaseIntegrationService:
    """Handle database integration operations"""
    pass

# Avoid abbreviated names unless very common
class SubHandler:            # ❌ Unclear abbreviation
class DwCAProc:             # ❌ Too abbreviated
```

#### Variable and Function Naming
```python
# Variables and functions: snake_case
def process_submission_file(file_path: str, user_id: str) -> dict:
    """Process uploaded submission file"""
    submission_id = generate_submission_id()
    processing_result = validate_and_process(file_path)
    
    return {
        'submission_id': submission_id,
        'status': processing_result.status
    }

# Constants: UPPER_CASE
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
SUPPORTED_FORMATS = ['zip', 'dwca']
DEFAULT_TIMEOUT = 300
```

### Type Hints and Documentation

#### Function Signatures
```python
from typing import Optional, List, Dict, Any, Union
from pathlib import Path
import asyncio

async def process_darwin_core_archive(
    file_path: Path,
    user_id: str,
    tenant_id: str,
    validation_level: str = 'strict',
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Union[str, int, List[str]]]:
    """
    Process Darwin Core Archive file and insert data into database.
    
    Args:
        file_path: Path to the uploaded DwC-A file
        user_id: ID of the user submitting the data
        tenant_id: Tenant ID for multi-tenant isolation
        validation_level: Level of validation ('strict', 'relaxed', 'minimal')
        metadata: Optional additional metadata for the submission
    
    Returns:
        Dictionary containing:
            - submission_id: Unique identifier for the submission
            - records_processed: Number of records successfully processed
            - warnings: List of validation warnings
            - status: Processing status ('success', 'partial', 'failed')
    
    Raises:
        ValidationError: If file format is invalid or required fields missing
        DatabaseError: If database insertion fails
        ProcessingError: If file processing encounters unrecoverable errors
    """
    pass
```

#### Class Documentation
```python
class DarwinCoreArchiveProcessor:
    """
    Process Darwin Core Archive files for biodiversity data submission.
    
    This class handles the complete workflow of Darwin Core Archive processing:
    - File validation and extraction
    - Metadata parsing and validation
    - Core data and extension processing
    - Spatial data transformation
    - Database insertion with security transforms
    
    Attributes:
        validation_level: Current validation strictness level
        supported_cores: List of supported core data types
        max_file_size: Maximum allowed file size in bytes
    
    Example:
        processor = DarwinCoreArchiveProcessor(validation_level='strict')
        result = await processor.process_archive(
            file_path='/path/to/archive.zip',
            user_id='user-123',
            tenant_id='tenant-456'
        )
    """
    
    def __init__(self, validation_level: str = 'strict'):
        self.validation_level = validation_level
        self.supported_cores = ['occurrence', 'event', 'taxon', 'location']
        self.max_file_size = 100 * 1024 * 1024  # 100MB
```

### Error Handling Standards

#### Custom Exception Classes
```python
# Define specific exception types
class SubmissionError(Exception):
    """Base exception for submission processing errors"""
    pass

class ValidationError(SubmissionError):
    """Raised when data validation fails"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message)
        self.field = field
        self.value = value

class ProcessingError(SubmissionError):
    """Raised when file processing fails"""
    def __init__(self, message: str, file_path: str = None, stage: str = None):
        super().__init__(message)
        self.file_path = file_path
        self.stage = stage

class DatabaseError(SubmissionError):
    """Raised when database operations fail"""
    def __init__(self, message: str, operation: str = None, details: dict = None):
        super().__init__(message)
        self.operation = operation
        self.details = details or {}
```

#### Error Handling Patterns
```python
async def process_submission_with_retry(
    file_path: Path, 
    user_id: str, 
    max_retries: int = 3
) -> Dict[str, Any]:
    """Process submission with automatic retry on transient failures"""
    
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return await process_submission_internal(file_path, user_id)
            
        except ValidationError as e:
            # Validation errors are not retryable
            logger.error(f"Validation failed for {file_path}: {e}")
            raise
            
        except DatabaseError as e:
            # Database errors might be transient
            last_exception = e
            if attempt < max_retries:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(
                    f"Database error on attempt {attempt + 1}/{max_retries + 1}: {e}. "
                    f"Retrying in {wait_time} seconds..."
                )
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"Database error persisted after {max_retries} retries: {e}")
                
        except ProcessingError as e:
            # Processing errors might be retryable depending on stage
            last_exception = e
            if e.stage in ['file_extraction', 'temporary_failure'] and attempt < max_retries:
                wait_time = 2 ** attempt
                logger.warning(f"Retrying processing error: {e}")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"Non-retryable processing error: {e}")
                raise
                
        except Exception as e:
            # Unexpected errors
            logger.error(f"Unexpected error processing submission: {e}")
            raise ProcessingError(f"Unexpected error: {str(e)}", str(file_path), "unknown")
    
    # If we get here, all retries failed
    raise last_exception
```

### Asynchronous Programming Standards

#### Async/Await Patterns
```python
import asyncio
from typing import List
import aiofiles
import asyncpg

class AsyncSubmissionProcessor:
    """Asynchronous submission processing with proper resource management"""
    
    def __init__(self):
        self.db_pool: Optional[asyncpg.Pool] = None
        self.semaphore = asyncio.Semaphore(10)  # Limit concurrent processing
    
    async def initialize(self):
        """Initialize async resources"""
        self.db_pool = await asyncpg.create_pool(
            **DATABASE_CONFIG,
            min_size=2,
            max_size=10,
            command_timeout=60
        )
    
    async def process_multiple_files(
        self, 
        file_paths: List[Path],
        user_id: str,
        tenant_id: str
    ) -> List[Dict[str, Any]]:
        """Process multiple files concurrently with rate limiting"""
        
        async def process_single_file(file_path: Path) -> Dict[str, Any]:
            async with self.semaphore:  # Rate limiting
                try:
                    return await self.process_file(file_path, user_id, tenant_id)
                except Exception as e:
                    return {
                        'file_path': str(file_path),
                        'status': 'failed',
                        'error': str(e)
                    }
        
        # Process files concurrently
        tasks = [process_single_file(fp) for fp in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    async def process_file(
        self, 
        file_path: Path, 
        user_id: str, 
        tenant_id: str
    ) -> Dict[str, Any]:
        """Process a single file with proper resource cleanup"""
        
        temp_dir = None
        try:
            # Create temporary directory for extraction
            temp_dir = await self.create_temp_directory()
            
            # Extract and validate archive
            extracted_files = await self.extract_archive(file_path, temp_dir)
            
            # Process data with database connection from pool
            async with self.db_pool.acquire() as conn:
                # Set database context
                await conn.execute("SET search_path = faunalogic_dapi_v1, public")
                await conn.execute("SELECT api_set_context($1, $2)", user_id, tenant_id)
                
                # Process the data
                result = await self.process_extracted_data(extracted_files, conn)
                
            return result
            
        finally:
            # Always cleanup temp directory
            if temp_dir:
                await self.cleanup_temp_directory(temp_dir)
    
    async def cleanup(self):
        """Cleanup async resources"""
        if self.db_pool:
            await self.db_pool.close()
```

#### File I/O Best Practices
```python
import aiofiles
from pathlib import Path

async def read_dwc_file_async(file_path: Path, encoding: str = 'utf-8') -> List[Dict[str, str]]:
    """Read Darwin Core data file asynchronously"""
    
    records = []
    
    async with aiofiles.open(file_path, 'r', encoding=encoding) as file:
        # Read header
        header_line = await file.readline()
        headers = [h.strip() for h in header_line.strip().split('\t')]
        
        # Read data rows
        async for line in file:
            if line.strip():  # Skip empty lines
                values = [v.strip() for v in line.strip().split('\t')]
                
                # Pad with empty strings if needed
                while len(values) < len(headers):
                    values.append('')
                
                record = dict(zip(headers, values))
                records.append(record)
    
    return records

async def write_processing_log(
    log_path: Path, 
    submission_id: str, 
    messages: List[str]
) -> None:
    """Write processing log asynchronously"""
    
    async with aiofiles.open(log_path, 'a', encoding='utf-8') as log_file:
        timestamp = datetime.now().isoformat()
        
        await log_file.write(f"\n=== Submission {submission_id} - {timestamp} ===\n")
        
        for message in messages:
            await log_file.write(f"{message}\n")
```

### Celery Task Standards

#### Task Definition Patterns
```python
from celery import Celery
from celery.exceptions import Retry
from typing import Dict, Any
import logging

# Initialize Celery app
celery_app = Celery('submission_processor')
celery_app.config_from_object('app.config.celery_config')

logger = logging.getLogger(__name__)

@celery_app.task(
    bind=True,
    max_retries=3,
    default_retry_delay=60,
    autoretry_for=(DatabaseError, ProcessingError),
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True
)
def process_submission_task(
    self, 
    submission_id: str, 
    file_path: str, 
    user_id: str, 
    tenant_id: str
) -> Dict[str, Any]:
    """
    Process Darwin Core Archive submission asynchronously.
    
    This task handles the complete processing workflow:
    - File validation and extraction
    - Data parsing and validation  
    - Database insertion
    - Status updates and notifications
    
    Args:
        submission_id: Unique submission identifier
        file_path: Path to uploaded file
        user_id: User who submitted the file
        tenant_id: Tenant context for the submission
    
    Returns:
        Processing result dictionary with status and metrics
    """
    try:
        logger.info(f"Starting processing for submission {submission_id}")
        
        # Update submission status
        update_submission_status.delay(submission_id, 'processing')
        
        # Initialize processor
        processor = DarwinCoreArchiveProcessor()
        
        # Process the submission
        result = asyncio.run(processor.process_submission(
            Path(file_path), user_id, tenant_id
        ))
        
        # Update completion status
        update_submission_status.delay(submission_id, 'completed')
        
        # Send notification
        send_completion_notification.delay(submission_id, result)
        
        logger.info(f"Successfully processed submission {submission_id}")
        return result
        
    except ValidationError as exc:
        # Validation errors are not retryable
        logger.error(f"Validation failed for submission {submission_id}: {exc}")
        update_submission_status.delay(submission_id, 'failed', str(exc))
        raise
        
    except (DatabaseError, ProcessingError) as exc:
        # These exceptions are configured for automatic retry
        logger.warning(
            f"Retryable error processing submission {submission_id}: {exc}. "
            f"Retry {self.request.retries}/{self.max_retries}"
        )
        
        if self.request.retries >= self.max_retries:
            logger.error(f"Max retries exceeded for submission {submission_id}")
            update_submission_status.delay(submission_id, 'failed', str(exc))
        
        raise
        
    except Exception as exc:
        # Unexpected errors
        logger.error(f"Unexpected error processing submission {submission_id}: {exc}")
        update_submission_status.delay(submission_id, 'failed', f"Unexpected error: {str(exc)}")
        raise

@celery_app.task
def update_submission_status(
    submission_id: str, 
    status: str, 
    error_message: str = None
) -> None:
    """Update submission status in database"""
    
    asyncio.run(_update_submission_status_async(submission_id, status, error_message))

async def _update_submission_status_async(
    submission_id: str, 
    status: str, 
    error_message: str = None
) -> None:
    """Internal async function to update submission status"""
    
    async with get_db_connection() as conn:
        await conn.execute(
            "SELECT api_update_submission_status($1, $2, $3)",
            submission_id, status, error_message
        )
```

#### Task Monitoring and Health Checks
```python
@celery_app.task
def health_check_task() -> Dict[str, Any]:
    """Health check task for monitoring system"""
    
    try:
        # Test database connectivity
        async def test_db():
            async with get_db_connection() as conn:
                result = await conn.fetchval("SELECT 1")
                return result == 1
        
        db_healthy = asyncio.run(test_db())
        
        # Test file system access
        temp_file = Path('/tmp/health_check.txt')
        temp_file.write_text('health check')
        fs_healthy = temp_file.exists()
        temp_file.unlink()
        
        return {
            'status': 'healthy' if db_healthy and fs_healthy else 'unhealthy',
            'database': 'ok' if db_healthy else 'error',
            'filesystem': 'ok' if fs_healthy else 'error',
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# Schedule periodic health checks
celery_app.conf.beat_schedule = {
    'health-check': {
        'task': 'app.tasks.processing_tasks.health_check_task',
        'schedule': 300.0,  # Every 5 minutes
    },
}
```

### Testing Standards

#### Unit Test Structure
```python
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path
import tempfile
import zipfile

from app.services.dwc_processor import DarwinCoreArchiveProcessor
from app.models.submission import SubmissionStatus
from app.exceptions import ValidationError, ProcessingError

class TestDarwinCoreArchiveProcessor:
    """Test suite for Darwin Core Archive processing"""
    
    @pytest.fixture
    def processor(self):
        """Create processor instance for testing"""
        return DarwinCoreArchiveProcessor(validation_level='strict')
    
    @pytest.fixture
    def sample_dwc_archive(self):
        """Create sample Darwin Core Archive for testing"""
        
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
            archive_path = Path(temp_file.name)
        
        # Create sample archive
        with zipfile.ZipFile(archive_path, 'w') as archive:
            # Add meta.xml
            meta_xml = """<?xml version="1.0" encoding="UTF-8"?>
            <archive xmlns="http://rs.tdwg.org/dwc/text/">
                <core encoding="UTF-8" fieldsTerminatedBy="\\t" linesTerminatedBy="\\n" 
                      fieldsEnclosedBy="" ignoreHeaderLines="1" rowType="http://rs.tdwg.org/dwc/terms/Occurrence">
                    <files>
                        <location>occurrence.txt</location>
                    </files>
                    <id index="0" />
                    <field index="1" term="http://rs.tdwg.org/dwc/terms/scientificName"/>
                    <field index="2" term="http://rs.tdwg.org/dwc/terms/decimalLatitude"/>
                    <field index="3" term="http://rs.tdwg.org/dwc/terms/decimalLongitude"/>
                </core>
            </archive>"""
            archive.writestr('meta.xml', meta_xml)
            
            # Add occurrence data
            occurrence_data = """id\tscientificName\tdecimalLatitude\tdecimalLongitude
1\tHomo sapiens\t37.7749\t-122.4194
2\tCanis lupus\t40.7128\t-74.0060"""
            archive.writestr('occurrence.txt', occurrence_data)
        
        yield archive_path
        
        # Cleanup
        archive_path.unlink()
    
    @pytest.mark.asyncio
    async def test_valid_archive_processing(self, processor, sample_dwc_archive):
        """Test processing of valid Darwin Core Archive"""
        
        with patch('app.services.dwc_processor.get_db_connection') as mock_db:
            # Mock database connection
            mock_conn = AsyncMock()
            mock_db.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = {'submission_id': 'test-submission-id'}
            
            result = await processor.process_submission(
                sample_dwc_archive,
                'test-user-id',
                'test-tenant-id'
            )
            
            assert result['status'] == 'success'
            assert result['records_processed'] == 2
            assert 'submission_id' in result
    
    @pytest.mark.asyncio
    async def test_invalid_archive_format(self, processor):
        """Test handling of invalid archive format"""
        
        # Create invalid ZIP file
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
            temp_file.write(b'invalid zip content')
            invalid_path = Path(temp_file.name)
        
        try:
            with pytest.raises(ValidationError) as exc_info:
                await processor.process_submission(
                    invalid_path,
                    'test-user-id',
                    'test-tenant-id'
                )
            
            assert 'invalid archive format' in str(exc_info.value).lower()
            
        finally:
            invalid_path.unlink()
    
    @pytest.mark.asyncio
    async def test_missing_required_fields(self, processor):
        """Test handling of missing required fields"""
        
        # Create archive without required fields
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
            archive_path = Path(temp_file.name)
        
        with zipfile.ZipFile(archive_path, 'w') as archive:
            # Meta.xml without required fields
            meta_xml = """<?xml version="1.0" encoding="UTF-8"?>
            <archive xmlns="http://rs.tdwg.org/dwc/text/">
                <core encoding="UTF-8" fieldsTerminatedBy="\\t" linesTerminatedBy="\\n" 
                      fieldsEnclosedBy="" ignoreHeaderLines="1" rowType="http://rs.tdwg.org/dwc/terms/Occurrence">
                    <files>
                        <location>occurrence.txt</location>
                    </files>
                    <id index="0" />
                </core>
            </archive>"""
            archive.writestr('meta.xml', meta_xml)
            
            # Occurrence data without scientific name
            occurrence_data = """id\tdecimalLatitude\tdecimalLongitude
1\t37.7749\t-122.4194"""
            archive.writestr('occurrence.txt', occurrence_data)
        
        try:
            with pytest.raises(ValidationError) as exc_info:
                await processor.process_submission(
                    archive_path,
                    'test-user-id',
                    'test-tenant-id'
                )
            
            assert 'scientificName' in str(exc_info.value)
            
        finally:
            archive_path.unlink()
```

#### Integration Test Examples
```python
@pytest.mark.integration
class TestSubmissionIntegration:
    """Integration tests with real database"""
    
    @pytest.fixture(scope='class')
    async def db_connection(self):
        """Create test database connection"""
        conn = await asyncpg.connect(**TEST_DATABASE_CONFIG)
        await conn.execute("SET search_path = faunalogic_dapi_v1, public")
        yield conn
        await conn.close()
    
    @pytest.mark.asyncio
    async def test_end_to_end_submission(self, db_connection, sample_dwc_archive):
        """Test complete submission workflow"""
        
        # Set up test user and tenant
        user_id = str(uuid.uuid4())
        tenant_id = str(uuid.uuid4())
        
        # Set database context
        await db_connection.execute(
            "SELECT api_set_context($1, $2)", user_id, tenant_id
        )
        
        # Process submission
        processor = DarwinCoreArchiveProcessor()
        result = await processor.process_submission(
            sample_dwc_archive, user_id, tenant_id
        )
        
        # Verify submission was created
        submission = await db_connection.fetchrow(
            "SELECT * FROM submission WHERE id = $1",
            result['submission_id']
        )
        
        assert submission is not None
        assert submission['created_by'] == user_id
        assert submission['tenant_id'] == tenant_id
        
        # Verify observations were inserted
        observations = await db_connection.fetch(
            "SELECT * FROM submission_observation WHERE submission_id = $1",
            result['submission_id']
        )
        
        assert len(observations) == 2
```

---

**Important**: These coding standards ensure consistency, reliability, and maintainability across the FaunaLogic submission service. All development should follow these patterns to maintain code quality and system integration.