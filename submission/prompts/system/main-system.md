# Submission Service System Prompt

**Version**: 1.0.0  
**Last Updated**: 2025-06-29  
**Status**: 🚧 Development Ready  
**Service**: Data Submission Processing Layer

## Service Overview

### Submission Service Description
The FaunaLogic submission service provides a serverside system for processing Darwin Core Archive biodiversity informatics file submissions. Built with Python and Celery, it handles asynchronous file processing, data validation, and database insertion for biodiversity data submissions.

### Service Architecture
- **Runtime**: Python 3.11+ with async/await support
- **Task Queue**: Celery with Redis/RabbitMQ broker
- **File Processing**: Darwin Core Archive (DwC-A) format support
- **Database Integration**: Direct integration with FaunaLogic PostgreSQL database
- **API Framework**: FastAPI or Flask for submission endpoints
- **File Storage**: Local filesystem with potential cloud storage integration
- **Data Validation**: Comprehensive DwC-A format validation
- **Error Handling**: Robust error tracking and retry mechanisms

## 🔐 CRITICAL: Database Integration Requirements

**⚠️ ALL DATABASE CONNECTIONS MUST USE THE DEDICATED API USER:**

```python
# Database Connection Configuration (REQUIRED)
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'faunalogic',
    'user': 'faunalogic_api',
    'password': 'flatpass',
    'options': '-c search_path=faunalogic_dapi_v1,public'
}
```

**Database Access Pattern:**
```python
# ✅ CORRECT: Use API functions through database connection
async def insert_submission_data(submission_data):
    async with get_db_connection() as conn:
        # Set proper schema context
        await conn.execute("SET search_path = faunalogic_dapi_v1, public")
        
        # Use API functions for data insertion
        result = await conn.fetch(
            "SELECT api_create_submission($1, $2, $3)",
            user_id, tenant_id, submission_data
        )
        return result

# ❌ WRONG: Direct table access (not allowed for API user)
# await conn.execute("INSERT INTO submission ...")
```

## Darwin Core Archive Processing

### Supported File Formats
- **Darwin Core Archive**: Standard DwC-A ZIP files
- **Core Types**: Occurrence, Event, Taxon, Location
- **Extensions**: Multimedia, Measurements, Resource Relationships
- **Metadata**: EML (Ecological Metadata Language) support
- **File Encodings**: UTF-8, Latin-1 with auto-detection

### Processing Pipeline
```python
# Standard DwC-A Processing Workflow
class DwCAProcessor:
    async def process_submission(self, file_path: str, user_id: str):
        """Process Darwin Core Archive submission"""
        
        # 1. File validation and extraction
        archive = await self.extract_archive(file_path)
        
        # 2. Metadata validation
        metadata = await self.validate_metadata(archive)
        
        # 3. Core data validation
        core_data = await self.validate_core_data(archive)
        
        # 4. Extension data processing
        extensions = await self.process_extensions(archive)
        
        # 5. Database insertion
        submission_id = await self.insert_to_database(
            metadata, core_data, extensions, user_id
        )
        
        return submission_id
```

## Service Components

### Core Processing Classes
```python
# File submission handler
class SubmissionHandler:
    - validate_file_format()
    - extract_archive()
    - process_metadata()
    - queue_processing_task()

# Darwin Core data processor
class DwCAProcessor:
    - validate_dwc_structure()
    - parse_core_data()
    - process_extensions()
    - transform_spatial_data()

# Database integration
class DatabaseIntegrator:
    - connect_with_api_user()
    - insert_submission_record()
    - insert_observation_data()
    - apply_security_transforms()

# Task queue management
class CeleryTasks:
    - process_submission_async()
    - validate_data_async()
    - cleanup_temp_files()
    - send_completion_notifications()
```

### Data Flow Architecture
```
Upload → Validation → Queue → Processing → Database → Notification
   ↓         ↓          ↓         ↓           ↓          ↓
File     Format    Celery    DwC-A     PostgreSQL   Email/
Store    Check     Task      Parser    API Layer    Webhook
```

## Integration with Database Service

### Database Schema Integration
The submission service integrates with these database tables:
- `submission` - Main submission tracking
- `submission_observation` - Individual observation records
- `submission_metadata` - Archive metadata storage
- `spatial_component` - Spatial data from DwC-A
- `security_transform_submission` - Applied security transformations

### Spatial Data Handling
```python
# Spatial data processing from DwC-A
class SpatialDataProcessor:
    def process_coordinates(self, dwc_record):
        """Process Darwin Core spatial coordinates"""
        
        # Extract coordinate data
        lat = dwc_record.get('decimalLatitude')
        lon = dwc_record.get('decimalLongitude')
        
        if lat and lon:
            # Create PostGIS geometry
            geometry = f"POINT({lon} {lat})"
            
            # Apply coordinate validation
            if self.validate_coordinates(lat, lon):
                return {
                    'spatial_location': geometry,
                    'coordinate_precision': dwc_record.get('coordinatePrecision'),
                    'geodetic_datum': dwc_record.get('geodeticDatum', 'WGS84')
                }
        
        return None

    def apply_security_transforms(self, geometry, security_level):
        """Apply spatial security transformations"""
        # Use database API functions for security transforms
        return await self.db.fetch(
            "SELECT sec_apply_spatial_obfuscation($1, $2)",
            geometry, security_level
        )
```

## Development Constraints

### Submission Service Requirements
- [ ] All file uploads must be validated before processing
- [ ] Darwin Core Archive structure must be verified
- [ ] Spatial coordinates must be validated and transformed to WGS84
- [ ] All processing must be asynchronous using Celery
- [ ] Failed submissions must be logged and retryable
- [ ] Temporary files must be cleaned up after processing
- [ ] Database connections must use the faunalogic_api user
- [ ] All submissions must be associated with a tenant and user

### Security Requirements
- [ ] File uploads must be scanned for malicious content
- [ ] Archive extraction must be protected against zip bombs
- [ ] Spatial data must be evaluated for security classification
- [ ] User permissions must be validated before processing
- [ ] Audit trails must be maintained for all submissions
- [ ] Sensitive locations must use security transformations

### Performance Requirements
- [ ] Large files must be processed in chunks
- [ ] Memory usage must be controlled during processing
- [ ] Processing status must be trackable by users
- [ ] Failed tasks must be retried with exponential backoff
- [ ] Metrics must be collected for processing performance
- [ ] Resource usage must be monitored and limited

## Context Files

### Always Include These Files
When working on submission service tasks, include these files for context:

**Database Integration:**
- `database/migrations/V1_0_3__create_core_tables.sql` - Core tables including submission
- `database/migrations/V1_0_6__create_api_functions.sql` - API functions for data insertion
- `database/prompts/system/main-system.md` - Database service context

**Service Configuration:**
- `submission/config/celery_config.py` - Celery task configuration
- `submission/config/database_config.py` - Database connection settings
- `submission/models/` - Data models for DwC-A processing

## Development Guidelines

### Darwin Core Archive Processing Best Practices
```python
# Standard DwC-A validation
class DwCAValidator:
    def validate_archive_structure(self, archive_path):
        """Validate Darwin Core Archive structure"""
        required_files = ['meta.xml']
        
        with zipfile.ZipFile(archive_path, 'r') as archive:
            file_list = archive.namelist()
            
            # Check for required files
            for required_file in required_files:
                if required_file not in file_list:
                    raise ValidationError(f"Missing required file: {required_file}")
            
            # Validate meta.xml structure
            meta_xml = archive.read('meta.xml')
            return self.parse_metadata(meta_xml)

    def validate_core_data(self, core_file_path, core_type):
        """Validate core data file against DwC standards"""
        required_fields = DWC_CORE_FIELDS.get(core_type, [])
        
        with open(core_file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            # Check required fields
            missing_fields = set(required_fields) - set(reader.fieldnames)
            if missing_fields:
                raise ValidationError(f"Missing required fields: {missing_fields}")
            
            # Validate data rows
            for row_num, row in enumerate(reader, 1):
                self.validate_row_data(row, row_num)
```

### Celery Task Patterns
```python
# Asynchronous submission processing
@celery_app.task(bind=True, max_retries=3)
def process_submission_task(self, submission_id: str, file_path: str):
    """Process Darwin Core Archive submission asynchronously"""
    try:
        # Initialize processor
        processor = DwCAProcessor()
        
        # Update submission status
        await update_submission_status(submission_id, 'processing')
        
        # Process the archive
        result = await processor.process_submission(file_path, submission_id)
        
        # Update completion status
        await update_submission_status(submission_id, 'completed')
        
        # Send notification
        await send_completion_notification(submission_id, result)
        
        return result
        
    except Exception as exc:
        # Log error and retry
        logger.error(f"Submission processing failed: {exc}")
        
        if self.request.retries < self.max_retries:
            # Exponential backoff retry
            countdown = 60 * (2 ** self.request.retries)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            # Mark as failed after max retries
            await update_submission_status(submission_id, 'failed')
            raise exc
```

### Database Integration Patterns
```python
# Database connection management
class DatabaseManager:
    def __init__(self):
        self.config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'faunalogic'),
            'user': 'faunalogic_api',  # ALWAYS use API user
            'password': os.getenv('DB_API_PASSWORD'),
            'options': '-c search_path=faunalogic_dapi_v1,public'
        }
    
    async def get_connection(self):
        """Get database connection with proper API user setup"""
        conn = await asyncpg.connect(**self.config)
        
        # Ensure proper schema context
        await conn.execute("SET search_path = faunalogic_dapi_v1, public")
        
        return conn
    
    async def insert_submission(self, user_id: str, tenant_id: str, 
                               submission_data: dict):
        """Insert submission using database API functions"""
        async with self.get_connection() as conn:
            # Set user context for audit trail
            await conn.execute(
                "SELECT api_set_context($1, $2)", 
                user_id, tenant_id
            )
            
            # Insert submission using API function
            result = await conn.fetchrow(
                "SELECT api_create_submission($1, $2, $3)",
                submission_data['type'],
                submission_data['metadata'],
                submission_data['file_info']
            )
            
            return result['submission_id']
```

## Testing Standards

### Unit Testing Framework
```python
# Test structure for submission processing
class TestSubmissionProcessing:
    def setup_method(self):
        """Setup test database and sample files"""
        self.test_db = create_test_database()
        self.sample_dwca = create_sample_dwc_archive()
    
    async def test_valid_dwca_processing(self):
        """Test processing of valid Darwin Core Archive"""
        processor = DwCAProcessor()
        
        result = await processor.process_submission(
            self.sample_dwca, 
            'test-user-id'
        )
        
        assert result['status'] == 'success'
        assert result['records_processed'] > 0
    
    async def test_invalid_archive_handling(self):
        """Test handling of invalid archive files"""
        processor = DwCAProcessor()
        
        with pytest.raises(ValidationError):
            await processor.process_submission(
                'invalid_file.zip', 
                'test-user-id'
            )
    
    def teardown_method(self):
        """Cleanup test resources"""
        cleanup_test_files()
        self.test_db.close()
```

### Integration Testing
```python
# Integration test with database
class TestDatabaseIntegration:
    async def test_submission_insertion(self):
        """Test full submission insertion workflow"""
        
        # Create test submission data
        submission_data = {
            'type': 'occurrence',
            'metadata': {'title': 'Test Submission'},
            'observations': [
                {
                    'scientific_name': 'Homo sapiens',
                    'decimal_latitude': 37.7749,
                    'decimal_longitude': -122.4194
                }
            ]
        }
        
        # Process submission
        integrator = DatabaseIntegrator()
        submission_id = await integrator.insert_submission(
            'test-user-id',
            'test-tenant-id',
            submission_data
        )
        
        # Verify insertion
        assert submission_id is not None
        
        # Verify data in database
        async with get_db_connection() as conn:
            result = await conn.fetchrow(
                "SELECT * FROM submission WHERE id = $1",
                submission_id
            )
            assert result is not None
```

## Environment-Specific Considerations

### Development Environment
- Enable detailed logging for debugging
- Use local Redis for Celery broker
- Allow larger file uploads for testing
- Enable hot reloading for code changes

### Production Environment
- Configure Redis/RabbitMQ clustering
- Set up file storage with backup
- Enable monitoring and alerting
- Configure proper logging levels
- Set resource limits for processing
- Enable SSL for all connections

## Troubleshooting Common Issues

### File Processing Issues
```python
# Handle common file processing problems
class ErrorHandler:
    def handle_corrupted_archive(self, file_path):
        """Handle corrupted ZIP files"""
        try:
            with zipfile.ZipFile(file_path, 'r') as archive:
                # Test archive integrity
                archive.testzip()
        except zipfile.BadZipFile:
            raise ProcessingError("Archive file is corrupted")
    
    def handle_encoding_issues(self, file_path):
        """Handle text encoding problems"""
        encodings = ['utf-8', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    file.read()  # Test if readable
                    return encoding
            except UnicodeDecodeError:
                continue
        
        raise ProcessingError("Unable to determine file encoding")
```

### Celery Task Issues
```python
# Monitor and handle task failures
def monitor_task_health():
    """Monitor Celery task queue health"""
    inspector = celery_app.control.inspect()
    
    # Check active tasks
    active_tasks = inspector.active()
    
    # Check failed tasks
    failed_tasks = inspector.stats()
    
    # Alert if too many failures
    if failed_tasks and failed_tasks.get('failed', 0) > 10:
        send_alert("High number of failed tasks detected")
```

---

**Important**: This submission service is a critical component for biodiversity data ingestion into the FaunaLogic system. All Darwin Core Archive processing, spatial data handling, and database integration must be carefully implemented and tested.