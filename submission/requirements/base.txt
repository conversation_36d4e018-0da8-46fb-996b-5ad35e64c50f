# FaunaLogic Submission Service - Base Requirements
# Python 3.11+ required

# Core Dependencies
fastapi>=0.104.0,<0.105.0
uvicorn[standard]>=0.24.0,<0.25.0
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# Async Database
asyncpg>=0.29.0,<0.30.0
databases[postgresql]>=0.8.0,<0.9.0

# Task Queue
celery[redis]>=5.3.0,<6.0.0
redis>=5.0.0,<6.0.0

# File Processing
python-multipart>=0.0.6,<0.1.0
aiofiles>=23.2.0,<24.0.0

# Darwin Core Archive Processing
zipfile-deflate64>=0.2.0,<0.3.0
lxml>=4.9.0,<5.0.0
chardet>=5.2.0,<6.0.0

# Spatial Data
shapely>=2.0.0,<3.0.0
pyproj>=3.6.0,<4.0.0

# HTTP Client
httpx>=0.25.0,<0.26.0
aiohttp>=3.9.0,<4.0.0

# Validation and Parsing
email-validator>=2.1.0,<3.0.0
python-dateutil>=2.8.0,<3.0.0

# Environment and Configuration
python-dotenv>=1.0.0,<2.0.0

# Logging and Monitoring
structlog>=23.2.0,<24.0.0
prometheus-client>=0.19.0,<0.20.0

# Security
cryptography>=41.0.0,<42.0.0
passlib[bcrypt]>=1.7.0,<2.0.0

# Utilities
click>=8.1.0,<9.0.0
rich>=13.7.0,<14.0.0