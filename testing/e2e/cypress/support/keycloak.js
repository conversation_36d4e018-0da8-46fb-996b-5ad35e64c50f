/**
 * cypress-keycloak v1.7.0
 *
 * Copyright (c) 2019 babangsund
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
"use strict";function e(){for(var e=[],r=0;r<36;r++)e[r]="0123456789abcdef".substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]="0123456789abcdef".substr(3&parseInt(e[19])|8,1),e[8]=e[13]=e[18]=e[23]="-",e.join("")}Cypress.Commands.add("login",(function(r){var t=r.root,o=r.realm,n=r.username,a=r.password,d=r.client_id,c=r.redirect_uri,i=r.path_prefix,s=void 0===i?"auth":i;return cy.request({url:t+(s?"/"+s:"")+"/realms/"+o+"/protocol/openid-connect/auth",qs:{client_id:d,redirect_uri:c,scope:"openid",state:e(),nonce:e(),response_type:"code",response_mode:"fragment"}}).then((function(e){var r=document.createElement("html");r.innerHTML=e.body;var t=r.getElementsByTagName("form");if(!!t.length)return cy.request({form:!0,method:"POST",url:t[0].action,followRedirect:!1,body:{username:n,password:a}})}))})),Cypress.Commands.add("loginOTP",(function(r){var t=r.root,o=r.realm,n=r.username,a=r.password,d=r.client_id,c=r.redirect_uri,i=r.path_prefix,s=void 0===i?"auth":i,u=r.otp_secret,l=r.otp_credential_id,m=void 0===l?null:l;return cy.request({url:t+"/"+s+"/realms/"+o+"/protocol/openid-connect/auth",qs:{client_id:d,redirect_uri:c,scope:"openid",state:e(),nonce:e(),response_type:"code",response_mode:"fragment"}}).then((function(e){var r=document.createElement("html");r.innerHTML=e.body;var t=r.getElementsByTagName("form");if(!!t.length)return cy.request({form:!0,method:"POST",url:t[0].action,followRedirect:!1,body:{username:n,password:a}}).then((function(e){var r=document.createElement("html");r.innerHTML=e.body;var t=r.getElementsByTagName("form");cy.task("generateOTP",u,{log:!1}).then((function(e){var r={otp:e};m&&(r.selectedCredentialId=m),cy.request({form:!0,method:"POST",url:t[0].action,followRedirect:!1,body:r})}))}))}))})),Cypress.Commands.add("logout",(function(e){var r=e.root,t=e.realm,o=e.redirect_uri,n=e.path_prefix,a=void 0===n?"auth":n;return cy.request({qs:{redirect_uri:o},url:r+(a?"/"+a:"")+"/realms/"+t+"/protocol/openid-connect/logout"})}));
