{"name": "faunalogic-e2e-testing", "version": "1.0.0", "description": "This repository is a starting point adding Cypress tests to FaunaLogic.", "main": "index.js", "scripts": {"cypress": "cypress open", "test:e2e": "cypress run", "test:example": "cypress run --spec cypress/integration/login.spec.ts"}, "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "devDependencies": {"@babel/core": "^7.5.4", "@types/node": "^14.0.13", "cypress": "^9.1.0", "cypress-file-upload": "^5.0.8", "eslint": "^8.0.1", "eslint-config-airbnb-base": "^14.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^3.1.3", "faker": "^4.1.0", "moment": "^2.29.2", "rollup": "^2.21.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript": "^1.0.1", "typescript": "~3.2.2"}}