{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2015", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es5", "resolveJsonModule": true, "types": ["cypress", "cypress-file-upload"], "typeRoots": ["node_modules/@types", "./cypress/typings"], "lib": ["es2015", "dom"]}}